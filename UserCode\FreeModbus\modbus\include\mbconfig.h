/* 
 * FreeModbus Libary: A portable Modbus implementation for Modbus ASCII/RTU.
 * Copyright (c) 2006-2018 <PERSON> <<EMAIL>>
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 * 1. Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 * 3. The name of the author may not be used to endorse or promote products
 *    derived from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE AUTHOR ``AS IS'' AND ANY EXPRESS OR
 * IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.
 * IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY DIRECT, INDIRECT,
 * INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT
 * NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
 * DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
 * THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 * (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF
 * THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 */

#ifndef _MB_CONFIG_H
#define _MB_CONFIG_H

#ifdef __cplusplus
PR_BEGIN_EXTERN_C
#endif
/* ----------------------- Defines ------------------------------------------*/
/*! \defgroup modbus_cfg Modbus Configuration
 *
 * Most modules in the protocol stack are completly optional and can be
 * excluded. This is specially important if target resources are very small
 * and program memory space should be saved.<br>
 *
 * All of these settings are available in the file <code>mbconfig.h</code>
 */
/*! \addtogroup modbus_cfg
 *  @{
 */
/*! \brief If Modbus ASCII support is enabled. */
#define MB_ASCII_ENABLED                        (  0 )

/*! \brief If Modbus RTU support is enabled. */
#define MB_RTU_ENABLED                          (  1 )

/*! \brief If Modbus TCP support is enabled. */
#define MB_TCP_ENABLED                          (  0 )

/*! \brief The character timeout value for Modbus ASCII.
 *
 * The character timeout value is not fixed for Modbus ASCII and is therefore
 * a configuration option. It should be set to the maximum expected delay
 * time of the network.
 */
#define MB_ASCII_TIMEOUT_SEC                    (  1 )

/*! \brief Timeout to wait in ASCII prior to enabling transmitter.
 *
 * If defined the function calls vMBPortSerialDelay with the argument
 * MB_ASCII_TIMEOUT_WAIT_BEFORE_SEND_MS to allow for a delay before
 * the serial transmitter is enabled. This is required because some
 * targets are so fast that there is no time between receiving and
 * transmitting the frame. If the master is to slow with enabling its 
 * receiver then he will not receive the response correctly.
 */
#ifndef MB_ASCII_TIMEOUT_WAIT_BEFORE_SEND_MS
#define MB_ASCII_TIMEOUT_WAIT_BEFORE_SEND_MS    ( 0 )
#endif

/*! \brief Timeout to wait in RTU prior to enabling transmitter.
 *
 * As per ASCII, this waits before replying, to help master devices
 * that are too slow to switch to listen mode
 */
#ifndef MB_RTU_TIMEOUT_WAIT_BEFORE_SEND_MS
#define MB_RTU_TIMEOUT_WAIT_BEFORE_SEND_MS      ( 0 )
#endif

/*! \brief Maximum number of Modbus functions codes the protocol stack
 *    should support.
 *
 * The maximum number of supported Modbus functions must be greater than
 * the sum of all enabled functions in this file and custom function
 * handlers. If set to small adding more functions will fail.
 */
#define MB_FUNC_HANDLERS_MAX                    ( 16 )

/*! \brief Number of bytes which should be allocated for the <em>Report Slave ID
 *    </em>command.
 *
 * This number limits the maximum size of the additional segment in the
 * report slave id function. See eMBSetSlaveID(  ) for more information on
 * how to set this value. It is only used if MB_FUNC_OTHER_REP_SLAVEID_ENABLED
 * is set to <code>1</code>.
 */
#define MB_FUNC_OTHER_REP_SLAVEID_BUF           ( 32 )

/*! \brief If the <em>Report Slave ID</em> function should be enabled. */
#ifndef MB_FUNC_OTHER_REP_SLAVEID_ENABLED
#define MB_FUNC_OTHER_REP_SLAVEID_ENABLED       ( 1 )
#endif

/*! \brief If the <em>Read Input Registers</em> function should be enabled. */
#ifndef MB_FUNC_READ_INPUT_ENABLED
#define MB_FUNC_READ_INPUT_ENABLED              ( 1 )
#endif

/*! \brief If the <em>Read Holding Registers</em> function should be enabled. */
#ifndef MB_FUNC_READ_HOLDING_ENABLED
#define MB_FUNC_READ_HOLDING_ENABLED            ( 1 )
#endif

/*! \brief If the <em>Write Single Register</em> function should be enabled. */
#ifndef MB_FUNC_WRITE_HOLDING_ENABLED
#define MB_FUNC_WRITE_HOLDING_ENABLED           ( 1 )
#endif

/*! \brief If the <em>Write Multiple registers</em> function should be enabled. */
#ifndef MB_FUNC_WRITE_MULTIPLE_HOLDING_ENABLED
#define MB_FUNC_WRITE_MULTIPLE_HOLDING_ENABLED  ( 1 )
#endif

/*! \brief If the <em>Read Coils</em> function should be enabled. */
#ifndef MB_FUNC_READ_COILS_ENABLED
#define MB_FUNC_READ_COILS_ENABLED              ( 1 )
#endif

/*! \brief If the <em>Write Coils</em> function should be enabled. */
#ifndef MB_FUNC_WRITE_COIL_ENABLED
#define MB_FUNC_WRITE_COIL_ENABLED              ( 1 )
#endif

/*! \brief If the <em>Write Multiple Coils</em> function should be enabled. */
#ifndef MB_FUNC_WRITE_MULTIPLE_COILS_ENABLED
#define MB_FUNC_WRITE_MULTIPLE_COILS_ENABLED    ( 1 )
#endif

/*! \brief If the <em>Read Discrete Inputs</em> function should be enabled. */
#ifndef MB_FUNC_READ_DISCRETE_INPUTS_ENABLED
#define MB_FUNC_READ_DISCRETE_INPUTS_ENABLED    ( 1 )
#endif

/*! \brief If the <em>Read/Write Multiple Registers</em> function should be enabled. */
#ifndef MB_FUNC_READWRITE_HOLDING_ENABLED
#define MB_FUNC_READWRITE_HOLDING_ENABLED       ( 1 )
#endif

/*! \brief If the <em>Write File Record</em> function should be enabled. */
#define MB_FUNC_WRITE_FILE_ENABLED              (  0 )

/*! \brief If the <em>Read File Record</em> function should be enabled. */
#define MB_FUNC_READ_FILE_ENABLED               (  0 )

/*! @} */
#ifdef __cplusplus
    PR_END_EXTERN_C
#endif
#endif
