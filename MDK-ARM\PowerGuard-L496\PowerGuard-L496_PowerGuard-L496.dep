Dependencies for Project 'PowerGuard-L496', Target 'PowerGuard-L496': (DO NOT MODIFY !)
F (startup_stm32l496xx.s)(0x67E50668)(--cpu Cortex-M4.fp -g --apcs=interwork -I ..\Core\Inc

-I.\RTE\_PowerGuard-L496

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.5.1\CMSIS\Core\Include

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32L4xx_DFP\2.6.0\Drivers\CMSIS\Device\ST\STM32L4xx\Include

--pd "__UVISION_VERSION SETA 525" --pd "_RTE_ SETA 1" --pd "STM32L496xx SETA 1"

--list startup_stm32l496xx.lst --xref -o powerguard-l496\startup_stm32l496xx.o --depend powerguard-l496\startup_stm32l496xx.d)
F (../Core/Src/main.c)(0x685A3647)(--c99 -c --cpu Cortex-M4.fp -g -O1 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I ../Drivers/CMSIS/Device/ST/STM32L4xx/Include -I ../Drivers/CMSIS/Include -I ../UserCode/App/Include -I ../UserCode/Driver/Include -I ../UserCode/Support/Include -I ../UserCode/Service/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ..\UserCode\FreeModbus\modbus\ascii -I ..\UserCode\FreeModbus\modbus\include -I ..\UserCode\FreeModbus\port -I ..\UserCode\FreeModbus\modbus\rtu -I ..\UserCode\FreeModbus\modbus\tcp

-I.\RTE\_PowerGuard-L496

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.5.1\CMSIS\Core\Include

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32L4xx_DFP\2.6.0\Drivers\CMSIS\Device\ST\STM32L4xx\Include

-D__UVISION_VERSION="525" -D_RTE_ -DSTM32L496xx -DUSE_HAL_DRIVER -DSTM32L496xx

-o powerguard-l496\main.o --omf_browse powerguard-l496\main.crf --depend powerguard-l496\main.d)
I (../Core/Inc/main.h)(0x685A1667)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal.h)(0x65829150)
I (../Core/Inc/stm32l4xx_hal_conf.h)(0x68593DF1)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_def.h)(0x65829150)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l4xx.h)(0x6582914F)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l496xx.h)(0x6582914F)
I (../Drivers/CMSIS/Include/core_cm4.h)(0x658290EB)
I (D:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5DC27889)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x658290EB)
I (../Drivers/CMSIS/Include/mpu_armv7.h)(0x658290EB)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/system_stm32l4xx.h)(0x6582914F)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x65829150)
I (D:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x5CEB79E2)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_dma.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_cortex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_can.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_exti.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ramfunc.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_iwdg.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_spi.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_spi_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart_ex.h)(0x65829150)
I (../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os.h)(0x658290EF)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/FreeRTOS.h)(0x658290EF)
I (../Core/Inc/FreeRTOSConfig.h)(0x68593DEB)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/projdefs.h)(0x658290EF)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/portable.h)(0x658290EF)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/deprecated_definitions.h)(0x658290EF)
I (../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F/portmacro.h)(0x658290EF)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/mpu_wrappers.h)(0x658290EF)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/task.h)(0x658290EF)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/list.h)(0x658290EF)
I (../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.h)(0x658290EF)
I (../Core/Inc/can.h)(0x68593DEC)
I (../Core/Inc/i2c.h)(0x68593DEC)
I (../Core/Inc/iwdg.h)(0x68593DED)
I (../Core/Inc/spi.h)(0x68593DED)
I (../Core/Inc/tim.h)(0x68593DEE)
I (../Core/Inc/usart.h)(0x68593DEF)
I (../Core/Inc/gpio.h)(0x68593DE6)
F (../Core/Src/gpio.c)(0x68593DE6)(--c99 -c --cpu Cortex-M4.fp -g -O1 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I ../Drivers/CMSIS/Device/ST/STM32L4xx/Include -I ../Drivers/CMSIS/Include -I ../UserCode/App/Include -I ../UserCode/Driver/Include -I ../UserCode/Support/Include -I ../UserCode/Service/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ..\UserCode\FreeModbus\modbus\ascii -I ..\UserCode\FreeModbus\modbus\include -I ..\UserCode\FreeModbus\port -I ..\UserCode\FreeModbus\modbus\rtu -I ..\UserCode\FreeModbus\modbus\tcp

-I.\RTE\_PowerGuard-L496

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.5.1\CMSIS\Core\Include

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32L4xx_DFP\2.6.0\Drivers\CMSIS\Device\ST\STM32L4xx\Include

-D__UVISION_VERSION="525" -D_RTE_ -DSTM32L496xx -DUSE_HAL_DRIVER -DSTM32L496xx

-o powerguard-l496\gpio.o --omf_browse powerguard-l496\gpio.crf --depend powerguard-l496\gpio.d)
I (../Core/Inc/gpio.h)(0x68593DE6)
I (../Core/Inc/main.h)(0x685A1667)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal.h)(0x65829150)
I (../Core/Inc/stm32l4xx_hal_conf.h)(0x68593DF1)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_def.h)(0x65829150)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l4xx.h)(0x6582914F)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l496xx.h)(0x6582914F)
I (../Drivers/CMSIS/Include/core_cm4.h)(0x658290EB)
I (D:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5DC27889)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x658290EB)
I (../Drivers/CMSIS/Include/mpu_armv7.h)(0x658290EB)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/system_stm32l4xx.h)(0x6582914F)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x65829150)
I (D:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x5CEB79E2)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_dma.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_cortex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_can.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_exti.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ramfunc.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_iwdg.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_spi.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_spi_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart_ex.h)(0x65829150)
F (../Core/Src/freertos.c)(0x685A47CA)(--c99 -c --cpu Cortex-M4.fp -g -O1 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I ../Drivers/CMSIS/Device/ST/STM32L4xx/Include -I ../Drivers/CMSIS/Include -I ../UserCode/App/Include -I ../UserCode/Driver/Include -I ../UserCode/Support/Include -I ../UserCode/Service/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ..\UserCode\FreeModbus\modbus\ascii -I ..\UserCode\FreeModbus\modbus\include -I ..\UserCode\FreeModbus\port -I ..\UserCode\FreeModbus\modbus\rtu -I ..\UserCode\FreeModbus\modbus\tcp

-I.\RTE\_PowerGuard-L496

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.5.1\CMSIS\Core\Include

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32L4xx_DFP\2.6.0\Drivers\CMSIS\Device\ST\STM32L4xx\Include

-D__UVISION_VERSION="525" -D_RTE_ -DSTM32L496xx -DUSE_HAL_DRIVER -DSTM32L496xx

-o powerguard-l496\freertos.o --omf_browse powerguard-l496\freertos.crf --depend powerguard-l496\freertos.d)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/FreeRTOS.h)(0x658290EF)
I (D:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x5CEB79E2)
I (D:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5DC27889)
I (../Core/Inc/FreeRTOSConfig.h)(0x68593DEB)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/projdefs.h)(0x658290EF)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/portable.h)(0x658290EF)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/deprecated_definitions.h)(0x658290EF)
I (../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F/portmacro.h)(0x658290EF)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/mpu_wrappers.h)(0x658290EF)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/task.h)(0x658290EF)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/list.h)(0x658290EF)
I (../Core/Inc/main.h)(0x685A1667)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal.h)(0x65829150)
I (../Core/Inc/stm32l4xx_hal_conf.h)(0x68593DF1)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_def.h)(0x65829150)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l4xx.h)(0x6582914F)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l496xx.h)(0x6582914F)
I (../Drivers/CMSIS/Include/core_cm4.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x658290EB)
I (../Drivers/CMSIS/Include/mpu_armv7.h)(0x658290EB)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/system_stm32l4xx.h)(0x6582914F)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_dma.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_cortex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_can.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_exti.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ramfunc.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_iwdg.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_spi.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_spi_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart_ex.h)(0x65829150)
I (../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os.h)(0x658290EF)
I (../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.h)(0x658290EF)
I (../Core/Inc/iwdg.h)(0x68593DED)
I (../Core/Inc/usart.h)(0x68593DEF)
I (../UserCode/Driver/Include/rs485.h)(0x67E603BF)
I (../Core/Inc/gpio.h)(0x68593DE6)
I (../UserCode/App/Include/modbus_rtu_slave.h)(0x67E5FCCB)
I (..\UserCode\FreeModbus\modbus\include\mb.h)(0x68590A56)
I (..\UserCode\FreeModbus\port\port.h)(0x6858FC97)
I (D:\Keil_v5\ARM\ARMCC\include\assert.h)(0x5CEB79D6)
I (D:\Keil_v5\ARM\ARMCC\include\inttypes.h)(0x5CEB79E0)
I (..\UserCode\FreeModbus\modbus\include\mbport.h)(0x6858FC97)
I (..\UserCode\FreeModbus\modbus\include\mbproto.h)(0x6840A947)
I (../UserCode/Service/Include/rn7326.h)(0x68594607)
I (../Core/Inc/spi.h)(0x68593DED)
I (../UserCode/App/Include/modbus_test.h)(0x685A3DD7)
F (../Core/Src/can.c)(0x68593DEC)(--c99 -c --cpu Cortex-M4.fp -g -O1 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I ../Drivers/CMSIS/Device/ST/STM32L4xx/Include -I ../Drivers/CMSIS/Include -I ../UserCode/App/Include -I ../UserCode/Driver/Include -I ../UserCode/Support/Include -I ../UserCode/Service/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ..\UserCode\FreeModbus\modbus\ascii -I ..\UserCode\FreeModbus\modbus\include -I ..\UserCode\FreeModbus\port -I ..\UserCode\FreeModbus\modbus\rtu -I ..\UserCode\FreeModbus\modbus\tcp

-I.\RTE\_PowerGuard-L496

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.5.1\CMSIS\Core\Include

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32L4xx_DFP\2.6.0\Drivers\CMSIS\Device\ST\STM32L4xx\Include

-D__UVISION_VERSION="525" -D_RTE_ -DSTM32L496xx -DUSE_HAL_DRIVER -DSTM32L496xx

-o powerguard-l496\can.o --omf_browse powerguard-l496\can.crf --depend powerguard-l496\can.d)
I (../Core/Inc/can.h)(0x68593DEC)
I (../Core/Inc/main.h)(0x685A1667)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal.h)(0x65829150)
I (../Core/Inc/stm32l4xx_hal_conf.h)(0x68593DF1)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_def.h)(0x65829150)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l4xx.h)(0x6582914F)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l496xx.h)(0x6582914F)
I (../Drivers/CMSIS/Include/core_cm4.h)(0x658290EB)
I (D:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5DC27889)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x658290EB)
I (../Drivers/CMSIS/Include/mpu_armv7.h)(0x658290EB)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/system_stm32l4xx.h)(0x6582914F)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x65829150)
I (D:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x5CEB79E2)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_dma.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_cortex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_can.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_exti.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ramfunc.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_iwdg.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_spi.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_spi_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart_ex.h)(0x65829150)
F (../Core/Src/i2c.c)(0x68593DEC)(--c99 -c --cpu Cortex-M4.fp -g -O1 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I ../Drivers/CMSIS/Device/ST/STM32L4xx/Include -I ../Drivers/CMSIS/Include -I ../UserCode/App/Include -I ../UserCode/Driver/Include -I ../UserCode/Support/Include -I ../UserCode/Service/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ..\UserCode\FreeModbus\modbus\ascii -I ..\UserCode\FreeModbus\modbus\include -I ..\UserCode\FreeModbus\port -I ..\UserCode\FreeModbus\modbus\rtu -I ..\UserCode\FreeModbus\modbus\tcp

-I.\RTE\_PowerGuard-L496

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.5.1\CMSIS\Core\Include

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32L4xx_DFP\2.6.0\Drivers\CMSIS\Device\ST\STM32L4xx\Include

-D__UVISION_VERSION="525" -D_RTE_ -DSTM32L496xx -DUSE_HAL_DRIVER -DSTM32L496xx

-o powerguard-l496\i2c.o --omf_browse powerguard-l496\i2c.crf --depend powerguard-l496\i2c.d)
I (../Core/Inc/i2c.h)(0x68593DEC)
I (../Core/Inc/main.h)(0x685A1667)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal.h)(0x65829150)
I (../Core/Inc/stm32l4xx_hal_conf.h)(0x68593DF1)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_def.h)(0x65829150)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l4xx.h)(0x6582914F)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l496xx.h)(0x6582914F)
I (../Drivers/CMSIS/Include/core_cm4.h)(0x658290EB)
I (D:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5DC27889)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x658290EB)
I (../Drivers/CMSIS/Include/mpu_armv7.h)(0x658290EB)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/system_stm32l4xx.h)(0x6582914F)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x65829150)
I (D:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x5CEB79E2)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_dma.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_cortex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_can.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_exti.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ramfunc.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_iwdg.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_spi.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_spi_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart_ex.h)(0x65829150)
F (../Core/Src/iwdg.c)(0x68593DED)(--c99 -c --cpu Cortex-M4.fp -g -O1 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I ../Drivers/CMSIS/Device/ST/STM32L4xx/Include -I ../Drivers/CMSIS/Include -I ../UserCode/App/Include -I ../UserCode/Driver/Include -I ../UserCode/Support/Include -I ../UserCode/Service/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ..\UserCode\FreeModbus\modbus\ascii -I ..\UserCode\FreeModbus\modbus\include -I ..\UserCode\FreeModbus\port -I ..\UserCode\FreeModbus\modbus\rtu -I ..\UserCode\FreeModbus\modbus\tcp

-I.\RTE\_PowerGuard-L496

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.5.1\CMSIS\Core\Include

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32L4xx_DFP\2.6.0\Drivers\CMSIS\Device\ST\STM32L4xx\Include

-D__UVISION_VERSION="525" -D_RTE_ -DSTM32L496xx -DUSE_HAL_DRIVER -DSTM32L496xx

-o powerguard-l496\iwdg.o --omf_browse powerguard-l496\iwdg.crf --depend powerguard-l496\iwdg.d)
I (../Core/Inc/iwdg.h)(0x68593DED)
I (../Core/Inc/main.h)(0x685A1667)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal.h)(0x65829150)
I (../Core/Inc/stm32l4xx_hal_conf.h)(0x68593DF1)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_def.h)(0x65829150)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l4xx.h)(0x6582914F)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l496xx.h)(0x6582914F)
I (../Drivers/CMSIS/Include/core_cm4.h)(0x658290EB)
I (D:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5DC27889)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x658290EB)
I (../Drivers/CMSIS/Include/mpu_armv7.h)(0x658290EB)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/system_stm32l4xx.h)(0x6582914F)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x65829150)
I (D:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x5CEB79E2)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_dma.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_cortex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_can.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_exti.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ramfunc.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_iwdg.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_spi.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_spi_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart_ex.h)(0x65829150)
F (../Core/Src/spi.c)(0x6859FDC9)(--c99 -c --cpu Cortex-M4.fp -g -O1 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I ../Drivers/CMSIS/Device/ST/STM32L4xx/Include -I ../Drivers/CMSIS/Include -I ../UserCode/App/Include -I ../UserCode/Driver/Include -I ../UserCode/Support/Include -I ../UserCode/Service/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ..\UserCode\FreeModbus\modbus\ascii -I ..\UserCode\FreeModbus\modbus\include -I ..\UserCode\FreeModbus\port -I ..\UserCode\FreeModbus\modbus\rtu -I ..\UserCode\FreeModbus\modbus\tcp

-I.\RTE\_PowerGuard-L496

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.5.1\CMSIS\Core\Include

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32L4xx_DFP\2.6.0\Drivers\CMSIS\Device\ST\STM32L4xx\Include

-D__UVISION_VERSION="525" -D_RTE_ -DSTM32L496xx -DUSE_HAL_DRIVER -DSTM32L496xx

-o powerguard-l496\spi.o --omf_browse powerguard-l496\spi.crf --depend powerguard-l496\spi.d)
I (../Core/Inc/spi.h)(0x68593DED)
I (../Core/Inc/main.h)(0x685A1667)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal.h)(0x65829150)
I (../Core/Inc/stm32l4xx_hal_conf.h)(0x68593DF1)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_def.h)(0x65829150)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l4xx.h)(0x6582914F)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l496xx.h)(0x6582914F)
I (../Drivers/CMSIS/Include/core_cm4.h)(0x658290EB)
I (D:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5DC27889)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x658290EB)
I (../Drivers/CMSIS/Include/mpu_armv7.h)(0x658290EB)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/system_stm32l4xx.h)(0x6582914F)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x65829150)
I (D:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x5CEB79E2)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_dma.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_cortex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_can.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_exti.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ramfunc.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_iwdg.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_spi.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_spi_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart_ex.h)(0x65829150)
F (../Core/Src/tim.c)(0x685A3066)(--c99 -c --cpu Cortex-M4.fp -g -O1 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I ../Drivers/CMSIS/Device/ST/STM32L4xx/Include -I ../Drivers/CMSIS/Include -I ../UserCode/App/Include -I ../UserCode/Driver/Include -I ../UserCode/Support/Include -I ../UserCode/Service/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ..\UserCode\FreeModbus\modbus\ascii -I ..\UserCode\FreeModbus\modbus\include -I ..\UserCode\FreeModbus\port -I ..\UserCode\FreeModbus\modbus\rtu -I ..\UserCode\FreeModbus\modbus\tcp

-I.\RTE\_PowerGuard-L496

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.5.1\CMSIS\Core\Include

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32L4xx_DFP\2.6.0\Drivers\CMSIS\Device\ST\STM32L4xx\Include

-D__UVISION_VERSION="525" -D_RTE_ -DSTM32L496xx -DUSE_HAL_DRIVER -DSTM32L496xx

-o powerguard-l496\tim.o --omf_browse powerguard-l496\tim.crf --depend powerguard-l496\tim.d)
I (../Core/Inc/tim.h)(0x68593DEE)
I (../Core/Inc/main.h)(0x685A1667)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal.h)(0x65829150)
I (../Core/Inc/stm32l4xx_hal_conf.h)(0x68593DF1)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_def.h)(0x65829150)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l4xx.h)(0x6582914F)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l496xx.h)(0x6582914F)
I (../Drivers/CMSIS/Include/core_cm4.h)(0x658290EB)
I (D:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5DC27889)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x658290EB)
I (../Drivers/CMSIS/Include/mpu_armv7.h)(0x658290EB)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/system_stm32l4xx.h)(0x6582914F)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x65829150)
I (D:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x5CEB79E2)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_dma.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_cortex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_can.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_exti.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ramfunc.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_iwdg.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_spi.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_spi_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart_ex.h)(0x65829150)
F (../Core/Src/usart.c)(0x68593DEE)(--c99 -c --cpu Cortex-M4.fp -g -O1 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I ../Drivers/CMSIS/Device/ST/STM32L4xx/Include -I ../Drivers/CMSIS/Include -I ../UserCode/App/Include -I ../UserCode/Driver/Include -I ../UserCode/Support/Include -I ../UserCode/Service/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ..\UserCode\FreeModbus\modbus\ascii -I ..\UserCode\FreeModbus\modbus\include -I ..\UserCode\FreeModbus\port -I ..\UserCode\FreeModbus\modbus\rtu -I ..\UserCode\FreeModbus\modbus\tcp

-I.\RTE\_PowerGuard-L496

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.5.1\CMSIS\Core\Include

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32L4xx_DFP\2.6.0\Drivers\CMSIS\Device\ST\STM32L4xx\Include

-D__UVISION_VERSION="525" -D_RTE_ -DSTM32L496xx -DUSE_HAL_DRIVER -DSTM32L496xx

-o powerguard-l496\usart.o --omf_browse powerguard-l496\usart.crf --depend powerguard-l496\usart.d)
I (../Core/Inc/usart.h)(0x68593DEF)
I (../Core/Inc/main.h)(0x685A1667)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal.h)(0x65829150)
I (../Core/Inc/stm32l4xx_hal_conf.h)(0x68593DF1)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_def.h)(0x65829150)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l4xx.h)(0x6582914F)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l496xx.h)(0x6582914F)
I (../Drivers/CMSIS/Include/core_cm4.h)(0x658290EB)
I (D:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5DC27889)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x658290EB)
I (../Drivers/CMSIS/Include/mpu_armv7.h)(0x658290EB)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/system_stm32l4xx.h)(0x6582914F)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x65829150)
I (D:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x5CEB79E2)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_dma.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_cortex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_can.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_exti.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ramfunc.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_iwdg.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_spi.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_spi_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart_ex.h)(0x65829150)
F (../Core/Src/stm32l4xx_it.c)(0x685A39E1)(--c99 -c --cpu Cortex-M4.fp -g -O1 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I ../Drivers/CMSIS/Device/ST/STM32L4xx/Include -I ../Drivers/CMSIS/Include -I ../UserCode/App/Include -I ../UserCode/Driver/Include -I ../UserCode/Support/Include -I ../UserCode/Service/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ..\UserCode\FreeModbus\modbus\ascii -I ..\UserCode\FreeModbus\modbus\include -I ..\UserCode\FreeModbus\port -I ..\UserCode\FreeModbus\modbus\rtu -I ..\UserCode\FreeModbus\modbus\tcp

-I.\RTE\_PowerGuard-L496

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.5.1\CMSIS\Core\Include

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32L4xx_DFP\2.6.0\Drivers\CMSIS\Device\ST\STM32L4xx\Include

-D__UVISION_VERSION="525" -D_RTE_ -DSTM32L496xx -DUSE_HAL_DRIVER -DSTM32L496xx

-o powerguard-l496\stm32l4xx_it.o --omf_browse powerguard-l496\stm32l4xx_it.crf --depend powerguard-l496\stm32l4xx_it.d)
I (../Core/Inc/main.h)(0x685A1667)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal.h)(0x65829150)
I (../Core/Inc/stm32l4xx_hal_conf.h)(0x68593DF1)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_def.h)(0x65829150)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l4xx.h)(0x6582914F)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l496xx.h)(0x6582914F)
I (../Drivers/CMSIS/Include/core_cm4.h)(0x658290EB)
I (D:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5DC27889)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x658290EB)
I (../Drivers/CMSIS/Include/mpu_armv7.h)(0x658290EB)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/system_stm32l4xx.h)(0x6582914F)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x65829150)
I (D:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x5CEB79E2)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_dma.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_cortex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_can.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_exti.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ramfunc.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_iwdg.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_spi.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_spi_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart_ex.h)(0x65829150)
I (../Core/Inc/stm32l4xx_it.h)(0x68593DEF)
I (..\UserCode\FreeModbus\modbus\include\mb.h)(0x68590A56)
I (..\UserCode\FreeModbus\port\port.h)(0x6858FC97)
I (D:\Keil_v5\ARM\ARMCC\include\assert.h)(0x5CEB79D6)
I (D:\Keil_v5\ARM\ARMCC\include\inttypes.h)(0x5CEB79E0)
I (..\UserCode\FreeModbus\modbus\include\mbport.h)(0x6858FC97)
I (..\UserCode\FreeModbus\modbus\include\mbproto.h)(0x6840A947)
F (../Core/Src/stm32l4xx_hal_msp.c)(0x68593DF0)(--c99 -c --cpu Cortex-M4.fp -g -O1 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I ../Drivers/CMSIS/Device/ST/STM32L4xx/Include -I ../Drivers/CMSIS/Include -I ../UserCode/App/Include -I ../UserCode/Driver/Include -I ../UserCode/Support/Include -I ../UserCode/Service/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ..\UserCode\FreeModbus\modbus\ascii -I ..\UserCode\FreeModbus\modbus\include -I ..\UserCode\FreeModbus\port -I ..\UserCode\FreeModbus\modbus\rtu -I ..\UserCode\FreeModbus\modbus\tcp

-I.\RTE\_PowerGuard-L496

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.5.1\CMSIS\Core\Include

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32L4xx_DFP\2.6.0\Drivers\CMSIS\Device\ST\STM32L4xx\Include

-D__UVISION_VERSION="525" -D_RTE_ -DSTM32L496xx -DUSE_HAL_DRIVER -DSTM32L496xx

-o powerguard-l496\stm32l4xx_hal_msp.o --omf_browse powerguard-l496\stm32l4xx_hal_msp.crf --depend powerguard-l496\stm32l4xx_hal_msp.d)
I (../Core/Inc/main.h)(0x685A1667)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal.h)(0x65829150)
I (../Core/Inc/stm32l4xx_hal_conf.h)(0x68593DF1)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_def.h)(0x65829150)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l4xx.h)(0x6582914F)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l496xx.h)(0x6582914F)
I (../Drivers/CMSIS/Include/core_cm4.h)(0x658290EB)
I (D:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5DC27889)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x658290EB)
I (../Drivers/CMSIS/Include/mpu_armv7.h)(0x658290EB)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/system_stm32l4xx.h)(0x6582914F)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x65829150)
I (D:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x5CEB79E2)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_dma.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_cortex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_can.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_exti.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ramfunc.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_iwdg.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_spi.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_spi_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart_ex.h)(0x65829150)
F (../Core/Src/stm32l4xx_hal_timebase_tim.c)(0x68593DF0)(--c99 -c --cpu Cortex-M4.fp -g -O1 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I ../Drivers/CMSIS/Device/ST/STM32L4xx/Include -I ../Drivers/CMSIS/Include -I ../UserCode/App/Include -I ../UserCode/Driver/Include -I ../UserCode/Support/Include -I ../UserCode/Service/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ..\UserCode\FreeModbus\modbus\ascii -I ..\UserCode\FreeModbus\modbus\include -I ..\UserCode\FreeModbus\port -I ..\UserCode\FreeModbus\modbus\rtu -I ..\UserCode\FreeModbus\modbus\tcp

-I.\RTE\_PowerGuard-L496

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.5.1\CMSIS\Core\Include

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32L4xx_DFP\2.6.0\Drivers\CMSIS\Device\ST\STM32L4xx\Include

-D__UVISION_VERSION="525" -D_RTE_ -DSTM32L496xx -DUSE_HAL_DRIVER -DSTM32L496xx

-o powerguard-l496\stm32l4xx_hal_timebase_tim.o --omf_browse powerguard-l496\stm32l4xx_hal_timebase_tim.crf --depend powerguard-l496\stm32l4xx_hal_timebase_tim.d)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal.h)(0x65829150)
I (../Core/Inc/stm32l4xx_hal_conf.h)(0x68593DF1)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_def.h)(0x65829150)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l4xx.h)(0x6582914F)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l496xx.h)(0x6582914F)
I (../Drivers/CMSIS/Include/core_cm4.h)(0x658290EB)
I (D:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5DC27889)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x658290EB)
I (../Drivers/CMSIS/Include/mpu_armv7.h)(0x658290EB)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/system_stm32l4xx.h)(0x6582914F)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x65829150)
I (D:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x5CEB79E2)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_dma.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_cortex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_can.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_exti.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ramfunc.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_iwdg.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_spi.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_spi_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart_ex.h)(0x65829150)
F (../Drivers/STM32L4xx_HAL_Driver/Src/stm32l4xx_hal_can.c)(0x65829150)(--c99 -c --cpu Cortex-M4.fp -g -O1 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I ../Drivers/CMSIS/Device/ST/STM32L4xx/Include -I ../Drivers/CMSIS/Include -I ../UserCode/App/Include -I ../UserCode/Driver/Include -I ../UserCode/Support/Include -I ../UserCode/Service/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ..\UserCode\FreeModbus\modbus\ascii -I ..\UserCode\FreeModbus\modbus\include -I ..\UserCode\FreeModbus\port -I ..\UserCode\FreeModbus\modbus\rtu -I ..\UserCode\FreeModbus\modbus\tcp

-I.\RTE\_PowerGuard-L496

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.5.1\CMSIS\Core\Include

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32L4xx_DFP\2.6.0\Drivers\CMSIS\Device\ST\STM32L4xx\Include

-D__UVISION_VERSION="525" -D_RTE_ -DSTM32L496xx -DUSE_HAL_DRIVER -DSTM32L496xx

-o powerguard-l496\stm32l4xx_hal_can.o --omf_browse powerguard-l496\stm32l4xx_hal_can.crf --depend powerguard-l496\stm32l4xx_hal_can.d)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal.h)(0x65829150)
I (../Core/Inc/stm32l4xx_hal_conf.h)(0x68593DF1)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_def.h)(0x65829150)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l4xx.h)(0x6582914F)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l496xx.h)(0x6582914F)
I (../Drivers/CMSIS/Include/core_cm4.h)(0x658290EB)
I (D:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5DC27889)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x658290EB)
I (../Drivers/CMSIS/Include/mpu_armv7.h)(0x658290EB)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/system_stm32l4xx.h)(0x6582914F)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x65829150)
I (D:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x5CEB79E2)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_dma.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_cortex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_can.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_exti.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ramfunc.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_iwdg.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_spi.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_spi_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart_ex.h)(0x65829150)
F (../Drivers/STM32L4xx_HAL_Driver/Src/stm32l4xx_hal.c)(0x65829150)(--c99 -c --cpu Cortex-M4.fp -g -O1 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I ../Drivers/CMSIS/Device/ST/STM32L4xx/Include -I ../Drivers/CMSIS/Include -I ../UserCode/App/Include -I ../UserCode/Driver/Include -I ../UserCode/Support/Include -I ../UserCode/Service/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ..\UserCode\FreeModbus\modbus\ascii -I ..\UserCode\FreeModbus\modbus\include -I ..\UserCode\FreeModbus\port -I ..\UserCode\FreeModbus\modbus\rtu -I ..\UserCode\FreeModbus\modbus\tcp

-I.\RTE\_PowerGuard-L496

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.5.1\CMSIS\Core\Include

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32L4xx_DFP\2.6.0\Drivers\CMSIS\Device\ST\STM32L4xx\Include

-D__UVISION_VERSION="525" -D_RTE_ -DSTM32L496xx -DUSE_HAL_DRIVER -DSTM32L496xx

-o powerguard-l496\stm32l4xx_hal.o --omf_browse powerguard-l496\stm32l4xx_hal.crf --depend powerguard-l496\stm32l4xx_hal.d)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal.h)(0x65829150)
I (../Core/Inc/stm32l4xx_hal_conf.h)(0x68593DF1)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_def.h)(0x65829150)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l4xx.h)(0x6582914F)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l496xx.h)(0x6582914F)
I (../Drivers/CMSIS/Include/core_cm4.h)(0x658290EB)
I (D:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5DC27889)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x658290EB)
I (../Drivers/CMSIS/Include/mpu_armv7.h)(0x658290EB)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/system_stm32l4xx.h)(0x6582914F)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x65829150)
I (D:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x5CEB79E2)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_dma.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_cortex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_can.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_exti.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ramfunc.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_iwdg.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_spi.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_spi_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart_ex.h)(0x65829150)
F (../Drivers/STM32L4xx_HAL_Driver/Src/stm32l4xx_hal_i2c.c)(0x65829150)(--c99 -c --cpu Cortex-M4.fp -g -O1 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I ../Drivers/CMSIS/Device/ST/STM32L4xx/Include -I ../Drivers/CMSIS/Include -I ../UserCode/App/Include -I ../UserCode/Driver/Include -I ../UserCode/Support/Include -I ../UserCode/Service/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ..\UserCode\FreeModbus\modbus\ascii -I ..\UserCode\FreeModbus\modbus\include -I ..\UserCode\FreeModbus\port -I ..\UserCode\FreeModbus\modbus\rtu -I ..\UserCode\FreeModbus\modbus\tcp

-I.\RTE\_PowerGuard-L496

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.5.1\CMSIS\Core\Include

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32L4xx_DFP\2.6.0\Drivers\CMSIS\Device\ST\STM32L4xx\Include

-D__UVISION_VERSION="525" -D_RTE_ -DSTM32L496xx -DUSE_HAL_DRIVER -DSTM32L496xx

-o powerguard-l496\stm32l4xx_hal_i2c.o --omf_browse powerguard-l496\stm32l4xx_hal_i2c.crf --depend powerguard-l496\stm32l4xx_hal_i2c.d)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal.h)(0x65829150)
I (../Core/Inc/stm32l4xx_hal_conf.h)(0x68593DF1)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_def.h)(0x65829150)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l4xx.h)(0x6582914F)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l496xx.h)(0x6582914F)
I (../Drivers/CMSIS/Include/core_cm4.h)(0x658290EB)
I (D:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5DC27889)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x658290EB)
I (../Drivers/CMSIS/Include/mpu_armv7.h)(0x658290EB)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/system_stm32l4xx.h)(0x6582914F)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x65829150)
I (D:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x5CEB79E2)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_dma.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_cortex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_can.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_exti.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ramfunc.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_iwdg.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_spi.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_spi_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart_ex.h)(0x65829150)
F (../Drivers/STM32L4xx_HAL_Driver/Src/stm32l4xx_hal_i2c_ex.c)(0x65829150)(--c99 -c --cpu Cortex-M4.fp -g -O1 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I ../Drivers/CMSIS/Device/ST/STM32L4xx/Include -I ../Drivers/CMSIS/Include -I ../UserCode/App/Include -I ../UserCode/Driver/Include -I ../UserCode/Support/Include -I ../UserCode/Service/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ..\UserCode\FreeModbus\modbus\ascii -I ..\UserCode\FreeModbus\modbus\include -I ..\UserCode\FreeModbus\port -I ..\UserCode\FreeModbus\modbus\rtu -I ..\UserCode\FreeModbus\modbus\tcp

-I.\RTE\_PowerGuard-L496

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.5.1\CMSIS\Core\Include

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32L4xx_DFP\2.6.0\Drivers\CMSIS\Device\ST\STM32L4xx\Include

-D__UVISION_VERSION="525" -D_RTE_ -DSTM32L496xx -DUSE_HAL_DRIVER -DSTM32L496xx

-o powerguard-l496\stm32l4xx_hal_i2c_ex.o --omf_browse powerguard-l496\stm32l4xx_hal_i2c_ex.crf --depend powerguard-l496\stm32l4xx_hal_i2c_ex.d)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal.h)(0x65829150)
I (../Core/Inc/stm32l4xx_hal_conf.h)(0x68593DF1)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_def.h)(0x65829150)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l4xx.h)(0x6582914F)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l496xx.h)(0x6582914F)
I (../Drivers/CMSIS/Include/core_cm4.h)(0x658290EB)
I (D:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5DC27889)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x658290EB)
I (../Drivers/CMSIS/Include/mpu_armv7.h)(0x658290EB)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/system_stm32l4xx.h)(0x6582914F)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x65829150)
I (D:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x5CEB79E2)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_dma.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_cortex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_can.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_exti.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ramfunc.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_iwdg.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_spi.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_spi_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart_ex.h)(0x65829150)
F (../Drivers/STM32L4xx_HAL_Driver/Src/stm32l4xx_hal_rcc.c)(0x65829150)(--c99 -c --cpu Cortex-M4.fp -g -O1 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I ../Drivers/CMSIS/Device/ST/STM32L4xx/Include -I ../Drivers/CMSIS/Include -I ../UserCode/App/Include -I ../UserCode/Driver/Include -I ../UserCode/Support/Include -I ../UserCode/Service/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ..\UserCode\FreeModbus\modbus\ascii -I ..\UserCode\FreeModbus\modbus\include -I ..\UserCode\FreeModbus\port -I ..\UserCode\FreeModbus\modbus\rtu -I ..\UserCode\FreeModbus\modbus\tcp

-I.\RTE\_PowerGuard-L496

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.5.1\CMSIS\Core\Include

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32L4xx_DFP\2.6.0\Drivers\CMSIS\Device\ST\STM32L4xx\Include

-D__UVISION_VERSION="525" -D_RTE_ -DSTM32L496xx -DUSE_HAL_DRIVER -DSTM32L496xx

-o powerguard-l496\stm32l4xx_hal_rcc.o --omf_browse powerguard-l496\stm32l4xx_hal_rcc.crf --depend powerguard-l496\stm32l4xx_hal_rcc.d)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal.h)(0x65829150)
I (../Core/Inc/stm32l4xx_hal_conf.h)(0x68593DF1)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_def.h)(0x65829150)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l4xx.h)(0x6582914F)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l496xx.h)(0x6582914F)
I (../Drivers/CMSIS/Include/core_cm4.h)(0x658290EB)
I (D:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5DC27889)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x658290EB)
I (../Drivers/CMSIS/Include/mpu_armv7.h)(0x658290EB)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/system_stm32l4xx.h)(0x6582914F)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x65829150)
I (D:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x5CEB79E2)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_dma.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_cortex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_can.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_exti.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ramfunc.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_iwdg.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_spi.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_spi_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart_ex.h)(0x65829150)
F (../Drivers/STM32L4xx_HAL_Driver/Src/stm32l4xx_hal_rcc_ex.c)(0x65829150)(--c99 -c --cpu Cortex-M4.fp -g -O1 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I ../Drivers/CMSIS/Device/ST/STM32L4xx/Include -I ../Drivers/CMSIS/Include -I ../UserCode/App/Include -I ../UserCode/Driver/Include -I ../UserCode/Support/Include -I ../UserCode/Service/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ..\UserCode\FreeModbus\modbus\ascii -I ..\UserCode\FreeModbus\modbus\include -I ..\UserCode\FreeModbus\port -I ..\UserCode\FreeModbus\modbus\rtu -I ..\UserCode\FreeModbus\modbus\tcp

-I.\RTE\_PowerGuard-L496

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.5.1\CMSIS\Core\Include

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32L4xx_DFP\2.6.0\Drivers\CMSIS\Device\ST\STM32L4xx\Include

-D__UVISION_VERSION="525" -D_RTE_ -DSTM32L496xx -DUSE_HAL_DRIVER -DSTM32L496xx

-o powerguard-l496\stm32l4xx_hal_rcc_ex.o --omf_browse powerguard-l496\stm32l4xx_hal_rcc_ex.crf --depend powerguard-l496\stm32l4xx_hal_rcc_ex.d)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal.h)(0x65829150)
I (../Core/Inc/stm32l4xx_hal_conf.h)(0x68593DF1)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_def.h)(0x65829150)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l4xx.h)(0x6582914F)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l496xx.h)(0x6582914F)
I (../Drivers/CMSIS/Include/core_cm4.h)(0x658290EB)
I (D:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5DC27889)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x658290EB)
I (../Drivers/CMSIS/Include/mpu_armv7.h)(0x658290EB)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/system_stm32l4xx.h)(0x6582914F)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x65829150)
I (D:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x5CEB79E2)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_dma.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_cortex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_can.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_exti.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ramfunc.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_iwdg.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_spi.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_spi_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart_ex.h)(0x65829150)
F (../Drivers/STM32L4xx_HAL_Driver/Src/stm32l4xx_hal_flash.c)(0x65829150)(--c99 -c --cpu Cortex-M4.fp -g -O1 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I ../Drivers/CMSIS/Device/ST/STM32L4xx/Include -I ../Drivers/CMSIS/Include -I ../UserCode/App/Include -I ../UserCode/Driver/Include -I ../UserCode/Support/Include -I ../UserCode/Service/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ..\UserCode\FreeModbus\modbus\ascii -I ..\UserCode\FreeModbus\modbus\include -I ..\UserCode\FreeModbus\port -I ..\UserCode\FreeModbus\modbus\rtu -I ..\UserCode\FreeModbus\modbus\tcp

-I.\RTE\_PowerGuard-L496

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.5.1\CMSIS\Core\Include

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32L4xx_DFP\2.6.0\Drivers\CMSIS\Device\ST\STM32L4xx\Include

-D__UVISION_VERSION="525" -D_RTE_ -DSTM32L496xx -DUSE_HAL_DRIVER -DSTM32L496xx

-o powerguard-l496\stm32l4xx_hal_flash.o --omf_browse powerguard-l496\stm32l4xx_hal_flash.crf --depend powerguard-l496\stm32l4xx_hal_flash.d)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal.h)(0x65829150)
I (../Core/Inc/stm32l4xx_hal_conf.h)(0x68593DF1)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_def.h)(0x65829150)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l4xx.h)(0x6582914F)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l496xx.h)(0x6582914F)
I (../Drivers/CMSIS/Include/core_cm4.h)(0x658290EB)
I (D:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5DC27889)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x658290EB)
I (../Drivers/CMSIS/Include/mpu_armv7.h)(0x658290EB)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/system_stm32l4xx.h)(0x6582914F)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x65829150)
I (D:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x5CEB79E2)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_dma.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_cortex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_can.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_exti.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ramfunc.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_iwdg.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_spi.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_spi_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart_ex.h)(0x65829150)
F (../Drivers/STM32L4xx_HAL_Driver/Src/stm32l4xx_hal_flash_ex.c)(0x65829150)(--c99 -c --cpu Cortex-M4.fp -g -O1 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I ../Drivers/CMSIS/Device/ST/STM32L4xx/Include -I ../Drivers/CMSIS/Include -I ../UserCode/App/Include -I ../UserCode/Driver/Include -I ../UserCode/Support/Include -I ../UserCode/Service/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ..\UserCode\FreeModbus\modbus\ascii -I ..\UserCode\FreeModbus\modbus\include -I ..\UserCode\FreeModbus\port -I ..\UserCode\FreeModbus\modbus\rtu -I ..\UserCode\FreeModbus\modbus\tcp

-I.\RTE\_PowerGuard-L496

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.5.1\CMSIS\Core\Include

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32L4xx_DFP\2.6.0\Drivers\CMSIS\Device\ST\STM32L4xx\Include

-D__UVISION_VERSION="525" -D_RTE_ -DSTM32L496xx -DUSE_HAL_DRIVER -DSTM32L496xx

-o powerguard-l496\stm32l4xx_hal_flash_ex.o --omf_browse powerguard-l496\stm32l4xx_hal_flash_ex.crf --depend powerguard-l496\stm32l4xx_hal_flash_ex.d)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal.h)(0x65829150)
I (../Core/Inc/stm32l4xx_hal_conf.h)(0x68593DF1)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_def.h)(0x65829150)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l4xx.h)(0x6582914F)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l496xx.h)(0x6582914F)
I (../Drivers/CMSIS/Include/core_cm4.h)(0x658290EB)
I (D:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5DC27889)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x658290EB)
I (../Drivers/CMSIS/Include/mpu_armv7.h)(0x658290EB)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/system_stm32l4xx.h)(0x6582914F)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x65829150)
I (D:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x5CEB79E2)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_dma.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_cortex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_can.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_exti.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ramfunc.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_iwdg.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_spi.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_spi_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart_ex.h)(0x65829150)
F (../Drivers/STM32L4xx_HAL_Driver/Src/stm32l4xx_hal_flash_ramfunc.c)(0x65829150)(--c99 -c --cpu Cortex-M4.fp -g -O1 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I ../Drivers/CMSIS/Device/ST/STM32L4xx/Include -I ../Drivers/CMSIS/Include -I ../UserCode/App/Include -I ../UserCode/Driver/Include -I ../UserCode/Support/Include -I ../UserCode/Service/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ..\UserCode\FreeModbus\modbus\ascii -I ..\UserCode\FreeModbus\modbus\include -I ..\UserCode\FreeModbus\port -I ..\UserCode\FreeModbus\modbus\rtu -I ..\UserCode\FreeModbus\modbus\tcp

-I.\RTE\_PowerGuard-L496

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.5.1\CMSIS\Core\Include

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32L4xx_DFP\2.6.0\Drivers\CMSIS\Device\ST\STM32L4xx\Include

-D__UVISION_VERSION="525" -D_RTE_ -DSTM32L496xx -DUSE_HAL_DRIVER -DSTM32L496xx

-o powerguard-l496\stm32l4xx_hal_flash_ramfunc.o --omf_browse powerguard-l496\stm32l4xx_hal_flash_ramfunc.crf --depend powerguard-l496\stm32l4xx_hal_flash_ramfunc.d)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal.h)(0x65829150)
I (../Core/Inc/stm32l4xx_hal_conf.h)(0x68593DF1)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_def.h)(0x65829150)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l4xx.h)(0x6582914F)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l496xx.h)(0x6582914F)
I (../Drivers/CMSIS/Include/core_cm4.h)(0x658290EB)
I (D:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5DC27889)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x658290EB)
I (../Drivers/CMSIS/Include/mpu_armv7.h)(0x658290EB)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/system_stm32l4xx.h)(0x6582914F)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x65829150)
I (D:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x5CEB79E2)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_dma.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_cortex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_can.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_exti.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ramfunc.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_iwdg.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_spi.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_spi_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart_ex.h)(0x65829150)
F (../Drivers/STM32L4xx_HAL_Driver/Src/stm32l4xx_hal_gpio.c)(0x65829150)(--c99 -c --cpu Cortex-M4.fp -g -O1 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I ../Drivers/CMSIS/Device/ST/STM32L4xx/Include -I ../Drivers/CMSIS/Include -I ../UserCode/App/Include -I ../UserCode/Driver/Include -I ../UserCode/Support/Include -I ../UserCode/Service/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ..\UserCode\FreeModbus\modbus\ascii -I ..\UserCode\FreeModbus\modbus\include -I ..\UserCode\FreeModbus\port -I ..\UserCode\FreeModbus\modbus\rtu -I ..\UserCode\FreeModbus\modbus\tcp

-I.\RTE\_PowerGuard-L496

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.5.1\CMSIS\Core\Include

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32L4xx_DFP\2.6.0\Drivers\CMSIS\Device\ST\STM32L4xx\Include

-D__UVISION_VERSION="525" -D_RTE_ -DSTM32L496xx -DUSE_HAL_DRIVER -DSTM32L496xx

-o powerguard-l496\stm32l4xx_hal_gpio.o --omf_browse powerguard-l496\stm32l4xx_hal_gpio.crf --depend powerguard-l496\stm32l4xx_hal_gpio.d)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal.h)(0x65829150)
I (../Core/Inc/stm32l4xx_hal_conf.h)(0x68593DF1)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_def.h)(0x65829150)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l4xx.h)(0x6582914F)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l496xx.h)(0x6582914F)
I (../Drivers/CMSIS/Include/core_cm4.h)(0x658290EB)
I (D:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5DC27889)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x658290EB)
I (../Drivers/CMSIS/Include/mpu_armv7.h)(0x658290EB)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/system_stm32l4xx.h)(0x6582914F)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x65829150)
I (D:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x5CEB79E2)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_dma.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_cortex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_can.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_exti.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ramfunc.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_iwdg.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_spi.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_spi_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart_ex.h)(0x65829150)
F (../Drivers/STM32L4xx_HAL_Driver/Src/stm32l4xx_hal_dma.c)(0x65829150)(--c99 -c --cpu Cortex-M4.fp -g -O1 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I ../Drivers/CMSIS/Device/ST/STM32L4xx/Include -I ../Drivers/CMSIS/Include -I ../UserCode/App/Include -I ../UserCode/Driver/Include -I ../UserCode/Support/Include -I ../UserCode/Service/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ..\UserCode\FreeModbus\modbus\ascii -I ..\UserCode\FreeModbus\modbus\include -I ..\UserCode\FreeModbus\port -I ..\UserCode\FreeModbus\modbus\rtu -I ..\UserCode\FreeModbus\modbus\tcp

-I.\RTE\_PowerGuard-L496

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.5.1\CMSIS\Core\Include

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32L4xx_DFP\2.6.0\Drivers\CMSIS\Device\ST\STM32L4xx\Include

-D__UVISION_VERSION="525" -D_RTE_ -DSTM32L496xx -DUSE_HAL_DRIVER -DSTM32L496xx

-o powerguard-l496\stm32l4xx_hal_dma.o --omf_browse powerguard-l496\stm32l4xx_hal_dma.crf --depend powerguard-l496\stm32l4xx_hal_dma.d)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal.h)(0x65829150)
I (../Core/Inc/stm32l4xx_hal_conf.h)(0x68593DF1)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_def.h)(0x65829150)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l4xx.h)(0x6582914F)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l496xx.h)(0x6582914F)
I (../Drivers/CMSIS/Include/core_cm4.h)(0x658290EB)
I (D:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5DC27889)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x658290EB)
I (../Drivers/CMSIS/Include/mpu_armv7.h)(0x658290EB)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/system_stm32l4xx.h)(0x6582914F)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x65829150)
I (D:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x5CEB79E2)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_dma.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_cortex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_can.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_exti.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ramfunc.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_iwdg.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_spi.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_spi_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart_ex.h)(0x65829150)
F (../Drivers/STM32L4xx_HAL_Driver/Src/stm32l4xx_hal_dma_ex.c)(0x65829150)(--c99 -c --cpu Cortex-M4.fp -g -O1 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I ../Drivers/CMSIS/Device/ST/STM32L4xx/Include -I ../Drivers/CMSIS/Include -I ../UserCode/App/Include -I ../UserCode/Driver/Include -I ../UserCode/Support/Include -I ../UserCode/Service/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ..\UserCode\FreeModbus\modbus\ascii -I ..\UserCode\FreeModbus\modbus\include -I ..\UserCode\FreeModbus\port -I ..\UserCode\FreeModbus\modbus\rtu -I ..\UserCode\FreeModbus\modbus\tcp

-I.\RTE\_PowerGuard-L496

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.5.1\CMSIS\Core\Include

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32L4xx_DFP\2.6.0\Drivers\CMSIS\Device\ST\STM32L4xx\Include

-D__UVISION_VERSION="525" -D_RTE_ -DSTM32L496xx -DUSE_HAL_DRIVER -DSTM32L496xx

-o powerguard-l496\stm32l4xx_hal_dma_ex.o --omf_browse powerguard-l496\stm32l4xx_hal_dma_ex.crf --depend powerguard-l496\stm32l4xx_hal_dma_ex.d)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal.h)(0x65829150)
I (../Core/Inc/stm32l4xx_hal_conf.h)(0x68593DF1)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_def.h)(0x65829150)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l4xx.h)(0x6582914F)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l496xx.h)(0x6582914F)
I (../Drivers/CMSIS/Include/core_cm4.h)(0x658290EB)
I (D:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5DC27889)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x658290EB)
I (../Drivers/CMSIS/Include/mpu_armv7.h)(0x658290EB)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/system_stm32l4xx.h)(0x6582914F)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x65829150)
I (D:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x5CEB79E2)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_dma.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_cortex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_can.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_exti.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ramfunc.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_iwdg.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_spi.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_spi_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart_ex.h)(0x65829150)
F (../Drivers/STM32L4xx_HAL_Driver/Src/stm32l4xx_hal_pwr.c)(0x65829150)(--c99 -c --cpu Cortex-M4.fp -g -O1 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I ../Drivers/CMSIS/Device/ST/STM32L4xx/Include -I ../Drivers/CMSIS/Include -I ../UserCode/App/Include -I ../UserCode/Driver/Include -I ../UserCode/Support/Include -I ../UserCode/Service/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ..\UserCode\FreeModbus\modbus\ascii -I ..\UserCode\FreeModbus\modbus\include -I ..\UserCode\FreeModbus\port -I ..\UserCode\FreeModbus\modbus\rtu -I ..\UserCode\FreeModbus\modbus\tcp

-I.\RTE\_PowerGuard-L496

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.5.1\CMSIS\Core\Include

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32L4xx_DFP\2.6.0\Drivers\CMSIS\Device\ST\STM32L4xx\Include

-D__UVISION_VERSION="525" -D_RTE_ -DSTM32L496xx -DUSE_HAL_DRIVER -DSTM32L496xx

-o powerguard-l496\stm32l4xx_hal_pwr.o --omf_browse powerguard-l496\stm32l4xx_hal_pwr.crf --depend powerguard-l496\stm32l4xx_hal_pwr.d)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal.h)(0x65829150)
I (../Core/Inc/stm32l4xx_hal_conf.h)(0x68593DF1)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_def.h)(0x65829150)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l4xx.h)(0x6582914F)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l496xx.h)(0x6582914F)
I (../Drivers/CMSIS/Include/core_cm4.h)(0x658290EB)
I (D:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5DC27889)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x658290EB)
I (../Drivers/CMSIS/Include/mpu_armv7.h)(0x658290EB)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/system_stm32l4xx.h)(0x6582914F)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x65829150)
I (D:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x5CEB79E2)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_dma.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_cortex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_can.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_exti.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ramfunc.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_iwdg.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_spi.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_spi_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart_ex.h)(0x65829150)
F (../Drivers/STM32L4xx_HAL_Driver/Src/stm32l4xx_hal_pwr_ex.c)(0x65829150)(--c99 -c --cpu Cortex-M4.fp -g -O1 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I ../Drivers/CMSIS/Device/ST/STM32L4xx/Include -I ../Drivers/CMSIS/Include -I ../UserCode/App/Include -I ../UserCode/Driver/Include -I ../UserCode/Support/Include -I ../UserCode/Service/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ..\UserCode\FreeModbus\modbus\ascii -I ..\UserCode\FreeModbus\modbus\include -I ..\UserCode\FreeModbus\port -I ..\UserCode\FreeModbus\modbus\rtu -I ..\UserCode\FreeModbus\modbus\tcp

-I.\RTE\_PowerGuard-L496

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.5.1\CMSIS\Core\Include

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32L4xx_DFP\2.6.0\Drivers\CMSIS\Device\ST\STM32L4xx\Include

-D__UVISION_VERSION="525" -D_RTE_ -DSTM32L496xx -DUSE_HAL_DRIVER -DSTM32L496xx

-o powerguard-l496\stm32l4xx_hal_pwr_ex.o --omf_browse powerguard-l496\stm32l4xx_hal_pwr_ex.crf --depend powerguard-l496\stm32l4xx_hal_pwr_ex.d)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal.h)(0x65829150)
I (../Core/Inc/stm32l4xx_hal_conf.h)(0x68593DF1)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_def.h)(0x65829150)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l4xx.h)(0x6582914F)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l496xx.h)(0x6582914F)
I (../Drivers/CMSIS/Include/core_cm4.h)(0x658290EB)
I (D:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5DC27889)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x658290EB)
I (../Drivers/CMSIS/Include/mpu_armv7.h)(0x658290EB)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/system_stm32l4xx.h)(0x6582914F)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x65829150)
I (D:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x5CEB79E2)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_dma.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_cortex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_can.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_exti.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ramfunc.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_iwdg.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_spi.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_spi_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart_ex.h)(0x65829150)
F (../Drivers/STM32L4xx_HAL_Driver/Src/stm32l4xx_hal_cortex.c)(0x65829150)(--c99 -c --cpu Cortex-M4.fp -g -O1 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I ../Drivers/CMSIS/Device/ST/STM32L4xx/Include -I ../Drivers/CMSIS/Include -I ../UserCode/App/Include -I ../UserCode/Driver/Include -I ../UserCode/Support/Include -I ../UserCode/Service/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ..\UserCode\FreeModbus\modbus\ascii -I ..\UserCode\FreeModbus\modbus\include -I ..\UserCode\FreeModbus\port -I ..\UserCode\FreeModbus\modbus\rtu -I ..\UserCode\FreeModbus\modbus\tcp

-I.\RTE\_PowerGuard-L496

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.5.1\CMSIS\Core\Include

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32L4xx_DFP\2.6.0\Drivers\CMSIS\Device\ST\STM32L4xx\Include

-D__UVISION_VERSION="525" -D_RTE_ -DSTM32L496xx -DUSE_HAL_DRIVER -DSTM32L496xx

-o powerguard-l496\stm32l4xx_hal_cortex.o --omf_browse powerguard-l496\stm32l4xx_hal_cortex.crf --depend powerguard-l496\stm32l4xx_hal_cortex.d)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal.h)(0x65829150)
I (../Core/Inc/stm32l4xx_hal_conf.h)(0x68593DF1)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_def.h)(0x65829150)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l4xx.h)(0x6582914F)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l496xx.h)(0x6582914F)
I (../Drivers/CMSIS/Include/core_cm4.h)(0x658290EB)
I (D:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5DC27889)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x658290EB)
I (../Drivers/CMSIS/Include/mpu_armv7.h)(0x658290EB)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/system_stm32l4xx.h)(0x6582914F)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x65829150)
I (D:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x5CEB79E2)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_dma.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_cortex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_can.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_exti.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ramfunc.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_iwdg.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_spi.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_spi_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart_ex.h)(0x65829150)
F (../Drivers/STM32L4xx_HAL_Driver/Src/stm32l4xx_hal_exti.c)(0x65829150)(--c99 -c --cpu Cortex-M4.fp -g -O1 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I ../Drivers/CMSIS/Device/ST/STM32L4xx/Include -I ../Drivers/CMSIS/Include -I ../UserCode/App/Include -I ../UserCode/Driver/Include -I ../UserCode/Support/Include -I ../UserCode/Service/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ..\UserCode\FreeModbus\modbus\ascii -I ..\UserCode\FreeModbus\modbus\include -I ..\UserCode\FreeModbus\port -I ..\UserCode\FreeModbus\modbus\rtu -I ..\UserCode\FreeModbus\modbus\tcp

-I.\RTE\_PowerGuard-L496

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.5.1\CMSIS\Core\Include

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32L4xx_DFP\2.6.0\Drivers\CMSIS\Device\ST\STM32L4xx\Include

-D__UVISION_VERSION="525" -D_RTE_ -DSTM32L496xx -DUSE_HAL_DRIVER -DSTM32L496xx

-o powerguard-l496\stm32l4xx_hal_exti.o --omf_browse powerguard-l496\stm32l4xx_hal_exti.crf --depend powerguard-l496\stm32l4xx_hal_exti.d)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal.h)(0x65829150)
I (../Core/Inc/stm32l4xx_hal_conf.h)(0x68593DF1)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_def.h)(0x65829150)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l4xx.h)(0x6582914F)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l496xx.h)(0x6582914F)
I (../Drivers/CMSIS/Include/core_cm4.h)(0x658290EB)
I (D:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5DC27889)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x658290EB)
I (../Drivers/CMSIS/Include/mpu_armv7.h)(0x658290EB)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/system_stm32l4xx.h)(0x6582914F)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x65829150)
I (D:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x5CEB79E2)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_dma.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_cortex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_can.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_exti.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ramfunc.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_iwdg.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_spi.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_spi_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart_ex.h)(0x65829150)
F (../Drivers/STM32L4xx_HAL_Driver/Src/stm32l4xx_hal_iwdg.c)(0x65829150)(--c99 -c --cpu Cortex-M4.fp -g -O1 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I ../Drivers/CMSIS/Device/ST/STM32L4xx/Include -I ../Drivers/CMSIS/Include -I ../UserCode/App/Include -I ../UserCode/Driver/Include -I ../UserCode/Support/Include -I ../UserCode/Service/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ..\UserCode\FreeModbus\modbus\ascii -I ..\UserCode\FreeModbus\modbus\include -I ..\UserCode\FreeModbus\port -I ..\UserCode\FreeModbus\modbus\rtu -I ..\UserCode\FreeModbus\modbus\tcp

-I.\RTE\_PowerGuard-L496

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.5.1\CMSIS\Core\Include

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32L4xx_DFP\2.6.0\Drivers\CMSIS\Device\ST\STM32L4xx\Include

-D__UVISION_VERSION="525" -D_RTE_ -DSTM32L496xx -DUSE_HAL_DRIVER -DSTM32L496xx

-o powerguard-l496\stm32l4xx_hal_iwdg.o --omf_browse powerguard-l496\stm32l4xx_hal_iwdg.crf --depend powerguard-l496\stm32l4xx_hal_iwdg.d)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal.h)(0x65829150)
I (../Core/Inc/stm32l4xx_hal_conf.h)(0x68593DF1)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_def.h)(0x65829150)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l4xx.h)(0x6582914F)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l496xx.h)(0x6582914F)
I (../Drivers/CMSIS/Include/core_cm4.h)(0x658290EB)
I (D:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5DC27889)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x658290EB)
I (../Drivers/CMSIS/Include/mpu_armv7.h)(0x658290EB)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/system_stm32l4xx.h)(0x6582914F)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x65829150)
I (D:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x5CEB79E2)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_dma.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_cortex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_can.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_exti.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ramfunc.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_iwdg.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_spi.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_spi_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart_ex.h)(0x65829150)
F (../Drivers/STM32L4xx_HAL_Driver/Src/stm32l4xx_hal_spi.c)(0x65829150)(--c99 -c --cpu Cortex-M4.fp -g -O1 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I ../Drivers/CMSIS/Device/ST/STM32L4xx/Include -I ../Drivers/CMSIS/Include -I ../UserCode/App/Include -I ../UserCode/Driver/Include -I ../UserCode/Support/Include -I ../UserCode/Service/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ..\UserCode\FreeModbus\modbus\ascii -I ..\UserCode\FreeModbus\modbus\include -I ..\UserCode\FreeModbus\port -I ..\UserCode\FreeModbus\modbus\rtu -I ..\UserCode\FreeModbus\modbus\tcp

-I.\RTE\_PowerGuard-L496

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.5.1\CMSIS\Core\Include

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32L4xx_DFP\2.6.0\Drivers\CMSIS\Device\ST\STM32L4xx\Include

-D__UVISION_VERSION="525" -D_RTE_ -DSTM32L496xx -DUSE_HAL_DRIVER -DSTM32L496xx

-o powerguard-l496\stm32l4xx_hal_spi.o --omf_browse powerguard-l496\stm32l4xx_hal_spi.crf --depend powerguard-l496\stm32l4xx_hal_spi.d)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal.h)(0x65829150)
I (../Core/Inc/stm32l4xx_hal_conf.h)(0x68593DF1)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_def.h)(0x65829150)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l4xx.h)(0x6582914F)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l496xx.h)(0x6582914F)
I (../Drivers/CMSIS/Include/core_cm4.h)(0x658290EB)
I (D:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5DC27889)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x658290EB)
I (../Drivers/CMSIS/Include/mpu_armv7.h)(0x658290EB)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/system_stm32l4xx.h)(0x6582914F)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x65829150)
I (D:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x5CEB79E2)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_dma.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_cortex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_can.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_exti.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ramfunc.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_iwdg.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_spi.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_spi_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart_ex.h)(0x65829150)
F (../Drivers/STM32L4xx_HAL_Driver/Src/stm32l4xx_hal_spi_ex.c)(0x65829150)(--c99 -c --cpu Cortex-M4.fp -g -O1 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I ../Drivers/CMSIS/Device/ST/STM32L4xx/Include -I ../Drivers/CMSIS/Include -I ../UserCode/App/Include -I ../UserCode/Driver/Include -I ../UserCode/Support/Include -I ../UserCode/Service/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ..\UserCode\FreeModbus\modbus\ascii -I ..\UserCode\FreeModbus\modbus\include -I ..\UserCode\FreeModbus\port -I ..\UserCode\FreeModbus\modbus\rtu -I ..\UserCode\FreeModbus\modbus\tcp

-I.\RTE\_PowerGuard-L496

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.5.1\CMSIS\Core\Include

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32L4xx_DFP\2.6.0\Drivers\CMSIS\Device\ST\STM32L4xx\Include

-D__UVISION_VERSION="525" -D_RTE_ -DSTM32L496xx -DUSE_HAL_DRIVER -DSTM32L496xx

-o powerguard-l496\stm32l4xx_hal_spi_ex.o --omf_browse powerguard-l496\stm32l4xx_hal_spi_ex.crf --depend powerguard-l496\stm32l4xx_hal_spi_ex.d)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal.h)(0x65829150)
I (../Core/Inc/stm32l4xx_hal_conf.h)(0x68593DF1)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_def.h)(0x65829150)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l4xx.h)(0x6582914F)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l496xx.h)(0x6582914F)
I (../Drivers/CMSIS/Include/core_cm4.h)(0x658290EB)
I (D:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5DC27889)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x658290EB)
I (../Drivers/CMSIS/Include/mpu_armv7.h)(0x658290EB)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/system_stm32l4xx.h)(0x6582914F)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x65829150)
I (D:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x5CEB79E2)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_dma.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_cortex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_can.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_exti.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ramfunc.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_iwdg.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_spi.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_spi_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart_ex.h)(0x65829150)
F (../Drivers/STM32L4xx_HAL_Driver/Src/stm32l4xx_hal_tim.c)(0x65829150)(--c99 -c --cpu Cortex-M4.fp -g -O1 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I ../Drivers/CMSIS/Device/ST/STM32L4xx/Include -I ../Drivers/CMSIS/Include -I ../UserCode/App/Include -I ../UserCode/Driver/Include -I ../UserCode/Support/Include -I ../UserCode/Service/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ..\UserCode\FreeModbus\modbus\ascii -I ..\UserCode\FreeModbus\modbus\include -I ..\UserCode\FreeModbus\port -I ..\UserCode\FreeModbus\modbus\rtu -I ..\UserCode\FreeModbus\modbus\tcp

-I.\RTE\_PowerGuard-L496

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.5.1\CMSIS\Core\Include

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32L4xx_DFP\2.6.0\Drivers\CMSIS\Device\ST\STM32L4xx\Include

-D__UVISION_VERSION="525" -D_RTE_ -DSTM32L496xx -DUSE_HAL_DRIVER -DSTM32L496xx

-o powerguard-l496\stm32l4xx_hal_tim.o --omf_browse powerguard-l496\stm32l4xx_hal_tim.crf --depend powerguard-l496\stm32l4xx_hal_tim.d)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal.h)(0x65829150)
I (../Core/Inc/stm32l4xx_hal_conf.h)(0x68593DF1)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_def.h)(0x65829150)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l4xx.h)(0x6582914F)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l496xx.h)(0x6582914F)
I (../Drivers/CMSIS/Include/core_cm4.h)(0x658290EB)
I (D:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5DC27889)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x658290EB)
I (../Drivers/CMSIS/Include/mpu_armv7.h)(0x658290EB)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/system_stm32l4xx.h)(0x6582914F)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x65829150)
I (D:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x5CEB79E2)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_dma.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_cortex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_can.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_exti.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ramfunc.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_iwdg.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_spi.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_spi_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart_ex.h)(0x65829150)
F (../Drivers/STM32L4xx_HAL_Driver/Src/stm32l4xx_hal_tim_ex.c)(0x65829150)(--c99 -c --cpu Cortex-M4.fp -g -O1 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I ../Drivers/CMSIS/Device/ST/STM32L4xx/Include -I ../Drivers/CMSIS/Include -I ../UserCode/App/Include -I ../UserCode/Driver/Include -I ../UserCode/Support/Include -I ../UserCode/Service/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ..\UserCode\FreeModbus\modbus\ascii -I ..\UserCode\FreeModbus\modbus\include -I ..\UserCode\FreeModbus\port -I ..\UserCode\FreeModbus\modbus\rtu -I ..\UserCode\FreeModbus\modbus\tcp

-I.\RTE\_PowerGuard-L496

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.5.1\CMSIS\Core\Include

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32L4xx_DFP\2.6.0\Drivers\CMSIS\Device\ST\STM32L4xx\Include

-D__UVISION_VERSION="525" -D_RTE_ -DSTM32L496xx -DUSE_HAL_DRIVER -DSTM32L496xx

-o powerguard-l496\stm32l4xx_hal_tim_ex.o --omf_browse powerguard-l496\stm32l4xx_hal_tim_ex.crf --depend powerguard-l496\stm32l4xx_hal_tim_ex.d)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal.h)(0x65829150)
I (../Core/Inc/stm32l4xx_hal_conf.h)(0x68593DF1)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_def.h)(0x65829150)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l4xx.h)(0x6582914F)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l496xx.h)(0x6582914F)
I (../Drivers/CMSIS/Include/core_cm4.h)(0x658290EB)
I (D:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5DC27889)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x658290EB)
I (../Drivers/CMSIS/Include/mpu_armv7.h)(0x658290EB)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/system_stm32l4xx.h)(0x6582914F)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x65829150)
I (D:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x5CEB79E2)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_dma.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_cortex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_can.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_exti.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ramfunc.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_iwdg.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_spi.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_spi_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart_ex.h)(0x65829150)
F (../Drivers/STM32L4xx_HAL_Driver/Src/stm32l4xx_hal_uart.c)(0x65829150)(--c99 -c --cpu Cortex-M4.fp -g -O1 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I ../Drivers/CMSIS/Device/ST/STM32L4xx/Include -I ../Drivers/CMSIS/Include -I ../UserCode/App/Include -I ../UserCode/Driver/Include -I ../UserCode/Support/Include -I ../UserCode/Service/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ..\UserCode\FreeModbus\modbus\ascii -I ..\UserCode\FreeModbus\modbus\include -I ..\UserCode\FreeModbus\port -I ..\UserCode\FreeModbus\modbus\rtu -I ..\UserCode\FreeModbus\modbus\tcp

-I.\RTE\_PowerGuard-L496

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.5.1\CMSIS\Core\Include

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32L4xx_DFP\2.6.0\Drivers\CMSIS\Device\ST\STM32L4xx\Include

-D__UVISION_VERSION="525" -D_RTE_ -DSTM32L496xx -DUSE_HAL_DRIVER -DSTM32L496xx

-o powerguard-l496\stm32l4xx_hal_uart.o --omf_browse powerguard-l496\stm32l4xx_hal_uart.crf --depend powerguard-l496\stm32l4xx_hal_uart.d)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal.h)(0x65829150)
I (../Core/Inc/stm32l4xx_hal_conf.h)(0x68593DF1)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_def.h)(0x65829150)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l4xx.h)(0x6582914F)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l496xx.h)(0x6582914F)
I (../Drivers/CMSIS/Include/core_cm4.h)(0x658290EB)
I (D:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5DC27889)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x658290EB)
I (../Drivers/CMSIS/Include/mpu_armv7.h)(0x658290EB)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/system_stm32l4xx.h)(0x6582914F)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x65829150)
I (D:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x5CEB79E2)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_dma.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_cortex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_can.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_exti.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ramfunc.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_iwdg.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_spi.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_spi_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart_ex.h)(0x65829150)
F (../Drivers/STM32L4xx_HAL_Driver/Src/stm32l4xx_hal_uart_ex.c)(0x65829150)(--c99 -c --cpu Cortex-M4.fp -g -O1 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I ../Drivers/CMSIS/Device/ST/STM32L4xx/Include -I ../Drivers/CMSIS/Include -I ../UserCode/App/Include -I ../UserCode/Driver/Include -I ../UserCode/Support/Include -I ../UserCode/Service/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ..\UserCode\FreeModbus\modbus\ascii -I ..\UserCode\FreeModbus\modbus\include -I ..\UserCode\FreeModbus\port -I ..\UserCode\FreeModbus\modbus\rtu -I ..\UserCode\FreeModbus\modbus\tcp

-I.\RTE\_PowerGuard-L496

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.5.1\CMSIS\Core\Include

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32L4xx_DFP\2.6.0\Drivers\CMSIS\Device\ST\STM32L4xx\Include

-D__UVISION_VERSION="525" -D_RTE_ -DSTM32L496xx -DUSE_HAL_DRIVER -DSTM32L496xx

-o powerguard-l496\stm32l4xx_hal_uart_ex.o --omf_browse powerguard-l496\stm32l4xx_hal_uart_ex.crf --depend powerguard-l496\stm32l4xx_hal_uart_ex.d)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal.h)(0x65829150)
I (../Core/Inc/stm32l4xx_hal_conf.h)(0x68593DF1)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_def.h)(0x65829150)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l4xx.h)(0x6582914F)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l496xx.h)(0x6582914F)
I (../Drivers/CMSIS/Include/core_cm4.h)(0x658290EB)
I (D:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5DC27889)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x658290EB)
I (../Drivers/CMSIS/Include/mpu_armv7.h)(0x658290EB)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/system_stm32l4xx.h)(0x6582914F)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x65829150)
I (D:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x5CEB79E2)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_dma.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_cortex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_can.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_exti.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ramfunc.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_iwdg.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_spi.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_spi_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart_ex.h)(0x65829150)
F (../Core/Src/system_stm32l4xx.c)(0x65829150)(--c99 -c --cpu Cortex-M4.fp -g -O1 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I ../Drivers/CMSIS/Device/ST/STM32L4xx/Include -I ../Drivers/CMSIS/Include -I ../UserCode/App/Include -I ../UserCode/Driver/Include -I ../UserCode/Support/Include -I ../UserCode/Service/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ..\UserCode\FreeModbus\modbus\ascii -I ..\UserCode\FreeModbus\modbus\include -I ..\UserCode\FreeModbus\port -I ..\UserCode\FreeModbus\modbus\rtu -I ..\UserCode\FreeModbus\modbus\tcp

-I.\RTE\_PowerGuard-L496

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.5.1\CMSIS\Core\Include

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32L4xx_DFP\2.6.0\Drivers\CMSIS\Device\ST\STM32L4xx\Include

-D__UVISION_VERSION="525" -D_RTE_ -DSTM32L496xx -DUSE_HAL_DRIVER -DSTM32L496xx

-o powerguard-l496\system_stm32l4xx.o --omf_browse powerguard-l496\system_stm32l4xx.crf --depend powerguard-l496\system_stm32l4xx.d)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l4xx.h)(0x6582914F)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l496xx.h)(0x6582914F)
I (../Drivers/CMSIS/Include/core_cm4.h)(0x658290EB)
I (D:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5DC27889)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x658290EB)
I (../Drivers/CMSIS/Include/mpu_armv7.h)(0x658290EB)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/system_stm32l4xx.h)(0x6582914F)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal.h)(0x65829150)
I (../Core/Inc/stm32l4xx_hal_conf.h)(0x68593DF1)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_def.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x65829150)
I (D:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x5CEB79E2)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_dma.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_cortex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_can.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_exti.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ramfunc.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_iwdg.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_spi.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_spi_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart_ex.h)(0x65829150)
F (../Middlewares/Third_Party/FreeRTOS/Source/croutine.c)(0x658290EF)(--c99 -c --cpu Cortex-M4.fp -g -O1 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I ../Drivers/CMSIS/Device/ST/STM32L4xx/Include -I ../Drivers/CMSIS/Include -I ../UserCode/App/Include -I ../UserCode/Driver/Include -I ../UserCode/Support/Include -I ../UserCode/Service/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ..\UserCode\FreeModbus\modbus\ascii -I ..\UserCode\FreeModbus\modbus\include -I ..\UserCode\FreeModbus\port -I ..\UserCode\FreeModbus\modbus\rtu -I ..\UserCode\FreeModbus\modbus\tcp

-I.\RTE\_PowerGuard-L496

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.5.1\CMSIS\Core\Include

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32L4xx_DFP\2.6.0\Drivers\CMSIS\Device\ST\STM32L4xx\Include

-D__UVISION_VERSION="525" -D_RTE_ -DSTM32L496xx -DUSE_HAL_DRIVER -DSTM32L496xx

-o powerguard-l496\croutine.o --omf_browse powerguard-l496\croutine.crf --depend powerguard-l496\croutine.d)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/FreeRTOS.h)(0x658290EF)
I (D:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x5CEB79E2)
I (D:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5DC27889)
I (../Core/Inc/FreeRTOSConfig.h)(0x68593DEB)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/projdefs.h)(0x658290EF)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/portable.h)(0x658290EF)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/deprecated_definitions.h)(0x658290EF)
I (../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F/portmacro.h)(0x658290EF)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/mpu_wrappers.h)(0x658290EF)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/task.h)(0x658290EF)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/list.h)(0x658290EF)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/croutine.h)(0x658290EF)
F (../Middlewares/Third_Party/FreeRTOS/Source/event_groups.c)(0x658290EF)(--c99 -c --cpu Cortex-M4.fp -g -O1 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I ../Drivers/CMSIS/Device/ST/STM32L4xx/Include -I ../Drivers/CMSIS/Include -I ../UserCode/App/Include -I ../UserCode/Driver/Include -I ../UserCode/Support/Include -I ../UserCode/Service/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ..\UserCode\FreeModbus\modbus\ascii -I ..\UserCode\FreeModbus\modbus\include -I ..\UserCode\FreeModbus\port -I ..\UserCode\FreeModbus\modbus\rtu -I ..\UserCode\FreeModbus\modbus\tcp

-I.\RTE\_PowerGuard-L496

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.5.1\CMSIS\Core\Include

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32L4xx_DFP\2.6.0\Drivers\CMSIS\Device\ST\STM32L4xx\Include

-D__UVISION_VERSION="525" -D_RTE_ -DSTM32L496xx -DUSE_HAL_DRIVER -DSTM32L496xx

-o powerguard-l496\event_groups.o --omf_browse powerguard-l496\event_groups.crf --depend powerguard-l496\event_groups.d)
I (D:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x5CEB79E2)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/FreeRTOS.h)(0x658290EF)
I (D:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x5CEB79E2)
I (D:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5DC27889)
I (../Core/Inc/FreeRTOSConfig.h)(0x68593DEB)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/projdefs.h)(0x658290EF)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/portable.h)(0x658290EF)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/deprecated_definitions.h)(0x658290EF)
I (../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F/portmacro.h)(0x658290EF)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/mpu_wrappers.h)(0x658290EF)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/task.h)(0x658290EF)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/list.h)(0x658290EF)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/timers.h)(0x658290EF)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/event_groups.h)(0x658290EF)
F (../Middlewares/Third_Party/FreeRTOS/Source/list.c)(0x658290EF)(--c99 -c --cpu Cortex-M4.fp -g -O1 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I ../Drivers/CMSIS/Device/ST/STM32L4xx/Include -I ../Drivers/CMSIS/Include -I ../UserCode/App/Include -I ../UserCode/Driver/Include -I ../UserCode/Support/Include -I ../UserCode/Service/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ..\UserCode\FreeModbus\modbus\ascii -I ..\UserCode\FreeModbus\modbus\include -I ..\UserCode\FreeModbus\port -I ..\UserCode\FreeModbus\modbus\rtu -I ..\UserCode\FreeModbus\modbus\tcp

-I.\RTE\_PowerGuard-L496

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.5.1\CMSIS\Core\Include

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32L4xx_DFP\2.6.0\Drivers\CMSIS\Device\ST\STM32L4xx\Include

-D__UVISION_VERSION="525" -D_RTE_ -DSTM32L496xx -DUSE_HAL_DRIVER -DSTM32L496xx

-o powerguard-l496\list.o --omf_browse powerguard-l496\list.crf --depend powerguard-l496\list.d)
I (D:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x5CEB79E2)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/FreeRTOS.h)(0x658290EF)
I (D:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x5CEB79E2)
I (D:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5DC27889)
I (../Core/Inc/FreeRTOSConfig.h)(0x68593DEB)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/projdefs.h)(0x658290EF)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/portable.h)(0x658290EF)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/deprecated_definitions.h)(0x658290EF)
I (../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F/portmacro.h)(0x658290EF)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/mpu_wrappers.h)(0x658290EF)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/list.h)(0x658290EF)
F (../Middlewares/Third_Party/FreeRTOS/Source/queue.c)(0x658290EF)(--c99 -c --cpu Cortex-M4.fp -g -O1 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I ../Drivers/CMSIS/Device/ST/STM32L4xx/Include -I ../Drivers/CMSIS/Include -I ../UserCode/App/Include -I ../UserCode/Driver/Include -I ../UserCode/Support/Include -I ../UserCode/Service/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ..\UserCode\FreeModbus\modbus\ascii -I ..\UserCode\FreeModbus\modbus\include -I ..\UserCode\FreeModbus\port -I ..\UserCode\FreeModbus\modbus\rtu -I ..\UserCode\FreeModbus\modbus\tcp

-I.\RTE\_PowerGuard-L496

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.5.1\CMSIS\Core\Include

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32L4xx_DFP\2.6.0\Drivers\CMSIS\Device\ST\STM32L4xx\Include

-D__UVISION_VERSION="525" -D_RTE_ -DSTM32L496xx -DUSE_HAL_DRIVER -DSTM32L496xx

-o powerguard-l496\queue.o --omf_browse powerguard-l496\queue.crf --depend powerguard-l496\queue.d)
I (D:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x5CEB79E2)
I (D:\Keil_v5\ARM\ARMCC\include\string.h)(0x5CEB79E2)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/FreeRTOS.h)(0x658290EF)
I (D:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x5CEB79E2)
I (D:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5DC27889)
I (../Core/Inc/FreeRTOSConfig.h)(0x68593DEB)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/projdefs.h)(0x658290EF)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/portable.h)(0x658290EF)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/deprecated_definitions.h)(0x658290EF)
I (../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F/portmacro.h)(0x658290EF)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/mpu_wrappers.h)(0x658290EF)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/task.h)(0x658290EF)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/list.h)(0x658290EF)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/queue.h)(0x658290EF)
F (../Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c)(0x658290F0)(--c99 -c --cpu Cortex-M4.fp -g -O1 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I ../Drivers/CMSIS/Device/ST/STM32L4xx/Include -I ../Drivers/CMSIS/Include -I ../UserCode/App/Include -I ../UserCode/Driver/Include -I ../UserCode/Support/Include -I ../UserCode/Service/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ..\UserCode\FreeModbus\modbus\ascii -I ..\UserCode\FreeModbus\modbus\include -I ..\UserCode\FreeModbus\port -I ..\UserCode\FreeModbus\modbus\rtu -I ..\UserCode\FreeModbus\modbus\tcp

-I.\RTE\_PowerGuard-L496

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.5.1\CMSIS\Core\Include

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32L4xx_DFP\2.6.0\Drivers\CMSIS\Device\ST\STM32L4xx\Include

-D__UVISION_VERSION="525" -D_RTE_ -DSTM32L496xx -DUSE_HAL_DRIVER -DSTM32L496xx

-o powerguard-l496\stream_buffer.o --omf_browse powerguard-l496\stream_buffer.crf --depend powerguard-l496\stream_buffer.d)
I (D:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5DC27889)
I (D:\Keil_v5\ARM\ARMCC\include\string.h)(0x5CEB79E2)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/FreeRTOS.h)(0x658290EF)
I (D:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x5CEB79E2)
I (../Core/Inc/FreeRTOSConfig.h)(0x68593DEB)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/projdefs.h)(0x658290EF)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/portable.h)(0x658290EF)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/deprecated_definitions.h)(0x658290EF)
I (../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F/portmacro.h)(0x658290EF)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/mpu_wrappers.h)(0x658290EF)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/task.h)(0x658290EF)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/list.h)(0x658290EF)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/stream_buffer.h)(0x658290EF)
F (../Middlewares/Third_Party/FreeRTOS/Source/tasks.c)(0x658290F0)(--c99 -c --cpu Cortex-M4.fp -g -O1 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I ../Drivers/CMSIS/Device/ST/STM32L4xx/Include -I ../Drivers/CMSIS/Include -I ../UserCode/App/Include -I ../UserCode/Driver/Include -I ../UserCode/Support/Include -I ../UserCode/Service/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ..\UserCode\FreeModbus\modbus\ascii -I ..\UserCode\FreeModbus\modbus\include -I ..\UserCode\FreeModbus\port -I ..\UserCode\FreeModbus\modbus\rtu -I ..\UserCode\FreeModbus\modbus\tcp

-I.\RTE\_PowerGuard-L496

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.5.1\CMSIS\Core\Include

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32L4xx_DFP\2.6.0\Drivers\CMSIS\Device\ST\STM32L4xx\Include

-D__UVISION_VERSION="525" -D_RTE_ -DSTM32L496xx -DUSE_HAL_DRIVER -DSTM32L496xx

-o powerguard-l496\tasks.o --omf_browse powerguard-l496\tasks.crf --depend powerguard-l496\tasks.d)
I (D:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x5CEB79E2)
I (D:\Keil_v5\ARM\ARMCC\include\string.h)(0x5CEB79E2)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/FreeRTOS.h)(0x658290EF)
I (D:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x5CEB79E2)
I (D:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5DC27889)
I (../Core/Inc/FreeRTOSConfig.h)(0x68593DEB)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/projdefs.h)(0x658290EF)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/portable.h)(0x658290EF)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/deprecated_definitions.h)(0x658290EF)
I (../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F/portmacro.h)(0x658290EF)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/mpu_wrappers.h)(0x658290EF)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/task.h)(0x658290EF)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/list.h)(0x658290EF)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/timers.h)(0x658290EF)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/stack_macros.h)(0x658290EF)
F (../Middlewares/Third_Party/FreeRTOS/Source/timers.c)(0x658290F0)(--c99 -c --cpu Cortex-M4.fp -g -O1 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I ../Drivers/CMSIS/Device/ST/STM32L4xx/Include -I ../Drivers/CMSIS/Include -I ../UserCode/App/Include -I ../UserCode/Driver/Include -I ../UserCode/Support/Include -I ../UserCode/Service/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ..\UserCode\FreeModbus\modbus\ascii -I ..\UserCode\FreeModbus\modbus\include -I ..\UserCode\FreeModbus\port -I ..\UserCode\FreeModbus\modbus\rtu -I ..\UserCode\FreeModbus\modbus\tcp

-I.\RTE\_PowerGuard-L496

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.5.1\CMSIS\Core\Include

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32L4xx_DFP\2.6.0\Drivers\CMSIS\Device\ST\STM32L4xx\Include

-D__UVISION_VERSION="525" -D_RTE_ -DSTM32L496xx -DUSE_HAL_DRIVER -DSTM32L496xx

-o powerguard-l496\timers.o --omf_browse powerguard-l496\timers.crf --depend powerguard-l496\timers.d)
I (D:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x5CEB79E2)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/FreeRTOS.h)(0x658290EF)
I (D:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x5CEB79E2)
I (D:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5DC27889)
I (../Core/Inc/FreeRTOSConfig.h)(0x68593DEB)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/projdefs.h)(0x658290EF)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/portable.h)(0x658290EF)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/deprecated_definitions.h)(0x658290EF)
I (../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F/portmacro.h)(0x658290EF)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/mpu_wrappers.h)(0x658290EF)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/task.h)(0x658290EF)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/list.h)(0x658290EF)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/queue.h)(0x658290EF)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/timers.h)(0x658290EF)
F (../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c)(0x658290EF)(--c99 -c --cpu Cortex-M4.fp -g -O1 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I ../Drivers/CMSIS/Device/ST/STM32L4xx/Include -I ../Drivers/CMSIS/Include -I ../UserCode/App/Include -I ../UserCode/Driver/Include -I ../UserCode/Support/Include -I ../UserCode/Service/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ..\UserCode\FreeModbus\modbus\ascii -I ..\UserCode\FreeModbus\modbus\include -I ..\UserCode\FreeModbus\port -I ..\UserCode\FreeModbus\modbus\rtu -I ..\UserCode\FreeModbus\modbus\tcp

-I.\RTE\_PowerGuard-L496

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.5.1\CMSIS\Core\Include

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32L4xx_DFP\2.6.0\Drivers\CMSIS\Device\ST\STM32L4xx\Include

-D__UVISION_VERSION="525" -D_RTE_ -DSTM32L496xx -DUSE_HAL_DRIVER -DSTM32L496xx

-o powerguard-l496\cmsis_os2.o --omf_browse powerguard-l496\cmsis_os2.crf --depend powerguard-l496\cmsis_os2.d)
I (D:\Keil_v5\ARM\ARMCC\include\string.h)(0x5CEB79E2)
I (../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.h)(0x658290EF)
I (D:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5DC27889)
I (D:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x5CEB79E2)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x658290EB)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/FreeRTOS.h)(0x658290EF)
I (../Core/Inc/FreeRTOSConfig.h)(0x68593DEB)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/projdefs.h)(0x658290EF)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/portable.h)(0x658290EF)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/deprecated_definitions.h)(0x658290EF)
I (../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F/portmacro.h)(0x658290EF)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/mpu_wrappers.h)(0x658290EF)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/task.h)(0x658290EF)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/list.h)(0x658290EF)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/event_groups.h)(0x658290EF)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/timers.h)(0x658290EF)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/semphr.h)(0x658290EF)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/queue.h)(0x658290EF)
I (../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/freertos_mpool.h)(0x658290EF)
I (../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/freertos_os2.h)(0x658290EF)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l4xx.h)(0x6582914F)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l496xx.h)(0x6582914F)
I (../Drivers/CMSIS/Include/core_cm4.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x658290EB)
I (../Drivers/CMSIS/Include/mpu_armv7.h)(0x658290EB)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/system_stm32l4xx.h)(0x6582914F)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal.h)(0x65829150)
I (../Core/Inc/stm32l4xx_hal_conf.h)(0x68593DF1)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_def.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_dma.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_cortex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_can.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_exti.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ramfunc.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_iwdg.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_spi.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_spi_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart_ex.h)(0x65829150)
F (../Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c)(0x658290EF)(--c99 -c --cpu Cortex-M4.fp -g -O1 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I ../Drivers/CMSIS/Device/ST/STM32L4xx/Include -I ../Drivers/CMSIS/Include -I ../UserCode/App/Include -I ../UserCode/Driver/Include -I ../UserCode/Support/Include -I ../UserCode/Service/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ..\UserCode\FreeModbus\modbus\ascii -I ..\UserCode\FreeModbus\modbus\include -I ..\UserCode\FreeModbus\port -I ..\UserCode\FreeModbus\modbus\rtu -I ..\UserCode\FreeModbus\modbus\tcp

-I.\RTE\_PowerGuard-L496

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.5.1\CMSIS\Core\Include

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32L4xx_DFP\2.6.0\Drivers\CMSIS\Device\ST\STM32L4xx\Include

-D__UVISION_VERSION="525" -D_RTE_ -DSTM32L496xx -DUSE_HAL_DRIVER -DSTM32L496xx

-o powerguard-l496\heap_4.o --omf_browse powerguard-l496\heap_4.crf --depend powerguard-l496\heap_4.d)
I (D:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x5CEB79E2)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/FreeRTOS.h)(0x658290EF)
I (D:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x5CEB79E2)
I (D:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5DC27889)
I (../Core/Inc/FreeRTOSConfig.h)(0x68593DEB)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/projdefs.h)(0x658290EF)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/portable.h)(0x658290EF)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/deprecated_definitions.h)(0x658290EF)
I (../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F/portmacro.h)(0x658290EF)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/mpu_wrappers.h)(0x658290EF)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/task.h)(0x658290EF)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/list.h)(0x658290EF)
F (../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F/port.c)(0x658290EF)(--c99 -c --cpu Cortex-M4.fp -g -O1 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I ../Drivers/CMSIS/Device/ST/STM32L4xx/Include -I ../Drivers/CMSIS/Include -I ../UserCode/App/Include -I ../UserCode/Driver/Include -I ../UserCode/Support/Include -I ../UserCode/Service/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ..\UserCode\FreeModbus\modbus\ascii -I ..\UserCode\FreeModbus\modbus\include -I ..\UserCode\FreeModbus\port -I ..\UserCode\FreeModbus\modbus\rtu -I ..\UserCode\FreeModbus\modbus\tcp

-I.\RTE\_PowerGuard-L496

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.5.1\CMSIS\Core\Include

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32L4xx_DFP\2.6.0\Drivers\CMSIS\Device\ST\STM32L4xx\Include

-D__UVISION_VERSION="525" -D_RTE_ -DSTM32L496xx -DUSE_HAL_DRIVER -DSTM32L496xx

-o powerguard-l496\port.o --omf_browse powerguard-l496\port.crf --depend powerguard-l496\port.d)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/FreeRTOS.h)(0x658290EF)
I (D:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x5CEB79E2)
I (D:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5DC27889)
I (../Core/Inc/FreeRTOSConfig.h)(0x68593DEB)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/projdefs.h)(0x658290EF)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/portable.h)(0x658290EF)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/deprecated_definitions.h)(0x658290EF)
I (../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F/portmacro.h)(0x658290EF)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/mpu_wrappers.h)(0x658290EF)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/task.h)(0x658290EF)
I (../Middlewares/Third_Party/FreeRTOS/Source/include/list.h)(0x658290EF)
F (..\UserCode\App\Source\modbus_rtu_slave.c)(0x67E5FFB2)(--c99 -c --cpu Cortex-M4.fp -g -O1 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I ../Drivers/CMSIS/Device/ST/STM32L4xx/Include -I ../Drivers/CMSIS/Include -I ../UserCode/App/Include -I ../UserCode/Driver/Include -I ../UserCode/Support/Include -I ../UserCode/Service/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ..\UserCode\FreeModbus\modbus\ascii -I ..\UserCode\FreeModbus\modbus\include -I ..\UserCode\FreeModbus\port -I ..\UserCode\FreeModbus\modbus\rtu -I ..\UserCode\FreeModbus\modbus\tcp

-I.\RTE\_PowerGuard-L496

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.5.1\CMSIS\Core\Include

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32L4xx_DFP\2.6.0\Drivers\CMSIS\Device\ST\STM32L4xx\Include

-D__UVISION_VERSION="525" -D_RTE_ -DSTM32L496xx -DUSE_HAL_DRIVER -DSTM32L496xx

-o powerguard-l496\modbus_rtu_slave.o --omf_browse powerguard-l496\modbus_rtu_slave.crf --depend powerguard-l496\modbus_rtu_slave.d)
I (../UserCode/App/Include/modbus_rtu_slave.h)(0x67E5FCCB)
I (D:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5DC27889)
I (../UserCode/Driver/Include/rs485.h)(0x67E603BF)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal.h)(0x65829150)
I (../Core/Inc/stm32l4xx_hal_conf.h)(0x68593DF1)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_def.h)(0x65829150)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l4xx.h)(0x6582914F)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l496xx.h)(0x6582914F)
I (../Drivers/CMSIS/Include/core_cm4.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x658290EB)
I (../Drivers/CMSIS/Include/mpu_armv7.h)(0x658290EB)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/system_stm32l4xx.h)(0x6582914F)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x65829150)
I (D:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x5CEB79E2)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_dma.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_cortex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_can.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_exti.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ramfunc.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_iwdg.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_spi.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_spi_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart_ex.h)(0x65829150)
I (../Core/Inc/usart.h)(0x68593DEF)
I (../Core/Inc/main.h)(0x685A1667)
I (../Core/Inc/gpio.h)(0x68593DE6)
I (D:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x5CEB79E2)
F (..\UserCode\App\Source\modbus_test.c)(0x685A4264)(--c99 -c --cpu Cortex-M4.fp -g -O1 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I ../Drivers/CMSIS/Device/ST/STM32L4xx/Include -I ../Drivers/CMSIS/Include -I ../UserCode/App/Include -I ../UserCode/Driver/Include -I ../UserCode/Support/Include -I ../UserCode/Service/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ..\UserCode\FreeModbus\modbus\ascii -I ..\UserCode\FreeModbus\modbus\include -I ..\UserCode\FreeModbus\port -I ..\UserCode\FreeModbus\modbus\rtu -I ..\UserCode\FreeModbus\modbus\tcp

-I.\RTE\_PowerGuard-L496

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.5.1\CMSIS\Core\Include

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32L4xx_DFP\2.6.0\Drivers\CMSIS\Device\ST\STM32L4xx\Include

-D__UVISION_VERSION="525" -D_RTE_ -DSTM32L496xx -DUSE_HAL_DRIVER -DSTM32L496xx

-o powerguard-l496\modbus_test.o --omf_browse powerguard-l496\modbus_test.crf --depend powerguard-l496\modbus_test.d)
I (../UserCode/App/Include/modbus_test.h)(0x685A3DD7)
I (D:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5DC27889)
I (..\UserCode\FreeModbus\modbus\include\mb.h)(0x68590A56)
I (..\UserCode\FreeModbus\port\port.h)(0x6858FC97)
I (D:\Keil_v5\ARM\ARMCC\include\assert.h)(0x5CEB79D6)
I (D:\Keil_v5\ARM\ARMCC\include\inttypes.h)(0x5CEB79E0)
I (../Core/Inc/main.h)(0x685A1667)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal.h)(0x65829150)
I (../Core/Inc/stm32l4xx_hal_conf.h)(0x68593DF1)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_def.h)(0x65829150)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l4xx.h)(0x6582914F)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l496xx.h)(0x6582914F)
I (../Drivers/CMSIS/Include/core_cm4.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x658290EB)
I (../Drivers/CMSIS/Include/mpu_armv7.h)(0x658290EB)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/system_stm32l4xx.h)(0x6582914F)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x65829150)
I (D:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x5CEB79E2)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_dma.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_cortex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_can.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_exti.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ramfunc.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_iwdg.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_spi.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_spi_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart_ex.h)(0x65829150)
I (..\UserCode\FreeModbus\modbus\include\mbport.h)(0x6858FC97)
I (..\UserCode\FreeModbus\modbus\include\mbproto.h)(0x6840A947)
I (../Core/Inc/gpio.h)(0x68593DE6)
I (../Core/Inc/usart.h)(0x68593DEF)
I (D:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x5CEB79E2)
F (..\UserCode\Driver\Source\rs485.c)(0x685A3A29)(--c99 -c --cpu Cortex-M4.fp -g -O1 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I ../Drivers/CMSIS/Device/ST/STM32L4xx/Include -I ../Drivers/CMSIS/Include -I ../UserCode/App/Include -I ../UserCode/Driver/Include -I ../UserCode/Support/Include -I ../UserCode/Service/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ..\UserCode\FreeModbus\modbus\ascii -I ..\UserCode\FreeModbus\modbus\include -I ..\UserCode\FreeModbus\port -I ..\UserCode\FreeModbus\modbus\rtu -I ..\UserCode\FreeModbus\modbus\tcp

-I.\RTE\_PowerGuard-L496

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.5.1\CMSIS\Core\Include

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32L4xx_DFP\2.6.0\Drivers\CMSIS\Device\ST\STM32L4xx\Include

-D__UVISION_VERSION="525" -D_RTE_ -DSTM32L496xx -DUSE_HAL_DRIVER -DSTM32L496xx

-o powerguard-l496\rs485.o --omf_browse powerguard-l496\rs485.crf --depend powerguard-l496\rs485.d)
I (../Core/Inc/usart.h)(0x68593DEF)
I (../Core/Inc/main.h)(0x685A1667)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal.h)(0x65829150)
I (../Core/Inc/stm32l4xx_hal_conf.h)(0x68593DF1)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_def.h)(0x65829150)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l4xx.h)(0x6582914F)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l496xx.h)(0x6582914F)
I (../Drivers/CMSIS/Include/core_cm4.h)(0x658290EB)
I (D:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5DC27889)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x658290EB)
I (../Drivers/CMSIS/Include/mpu_armv7.h)(0x658290EB)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/system_stm32l4xx.h)(0x6582914F)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x65829150)
I (D:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x5CEB79E2)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_dma.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_cortex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_can.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_exti.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ramfunc.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_iwdg.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_spi.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_spi_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart_ex.h)(0x65829150)
I (../UserCode/Driver/Include/rs485.h)(0x67E603BF)
I (../Core/Inc/gpio.h)(0x68593DE6)
I (../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.h)(0x658290EF)
I (D:\Keil_v5\ARM\ARMCC\include\string.h)(0x5CEB79E2)
F (..\UserCode\Service\Source\rn7326.c)(0x6859486F)(--c99 -c --cpu Cortex-M4.fp -g -O1 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I ../Drivers/CMSIS/Device/ST/STM32L4xx/Include -I ../Drivers/CMSIS/Include -I ../UserCode/App/Include -I ../UserCode/Driver/Include -I ../UserCode/Support/Include -I ../UserCode/Service/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ..\UserCode\FreeModbus\modbus\ascii -I ..\UserCode\FreeModbus\modbus\include -I ..\UserCode\FreeModbus\port -I ..\UserCode\FreeModbus\modbus\rtu -I ..\UserCode\FreeModbus\modbus\tcp

-I.\RTE\_PowerGuard-L496

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.5.1\CMSIS\Core\Include

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32L4xx_DFP\2.6.0\Drivers\CMSIS\Device\ST\STM32L4xx\Include

-D__UVISION_VERSION="525" -D_RTE_ -DSTM32L496xx -DUSE_HAL_DRIVER -DSTM32L496xx

-o powerguard-l496\rn7326.o --omf_browse powerguard-l496\rn7326.crf --depend powerguard-l496\rn7326.d)
I (../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.h)(0x658290EF)
I (D:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5DC27889)
I (D:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x5CEB79E2)
I (../Core/Inc/spi.h)(0x68593DED)
I (../Core/Inc/main.h)(0x685A1667)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal.h)(0x65829150)
I (../Core/Inc/stm32l4xx_hal_conf.h)(0x68593DF1)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_def.h)(0x65829150)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l4xx.h)(0x6582914F)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l496xx.h)(0x6582914F)
I (../Drivers/CMSIS/Include/core_cm4.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x658290EB)
I (../Drivers/CMSIS/Include/mpu_armv7.h)(0x658290EB)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/system_stm32l4xx.h)(0x6582914F)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_dma.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_cortex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_can.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_exti.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ramfunc.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_iwdg.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_spi.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_spi_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart_ex.h)(0x65829150)
I (../UserCode/Service/Include/rn7326.h)(0x68594607)
I (../UserCode/Service/Include/rn7326e_regs.h)(0x68591ACC)
I (D:\Keil_v5\ARM\ARMCC\include\string.h)(0x5CEB79E2)
F (..\UserCode\FreeModbus\modbus\ascii\mbascii.c)(0x6840A947)(--c99 -c --cpu Cortex-M4.fp -g -O1 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I ../Drivers/CMSIS/Device/ST/STM32L4xx/Include -I ../Drivers/CMSIS/Include -I ../UserCode/App/Include -I ../UserCode/Driver/Include -I ../UserCode/Support/Include -I ../UserCode/Service/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ..\UserCode\FreeModbus\modbus\ascii -I ..\UserCode\FreeModbus\modbus\include -I ..\UserCode\FreeModbus\port -I ..\UserCode\FreeModbus\modbus\rtu -I ..\UserCode\FreeModbus\modbus\tcp

-I.\RTE\_PowerGuard-L496

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.5.1\CMSIS\Core\Include

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32L4xx_DFP\2.6.0\Drivers\CMSIS\Device\ST\STM32L4xx\Include

-D__UVISION_VERSION="525" -D_RTE_ -DSTM32L496xx -DUSE_HAL_DRIVER -DSTM32L496xx

-o powerguard-l496\mbascii.o --omf_browse powerguard-l496\mbascii.crf --depend powerguard-l496\mbascii.d)
I (D:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x5CEB79E2)
I (D:\Keil_v5\ARM\ARMCC\include\string.h)(0x5CEB79E2)
I (..\UserCode\FreeModbus\port\port.h)(0x6858FC97)
I (D:\Keil_v5\ARM\ARMCC\include\assert.h)(0x5CEB79D6)
I (D:\Keil_v5\ARM\ARMCC\include\inttypes.h)(0x5CEB79E0)
I (D:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5DC27889)
I (../Core/Inc/main.h)(0x685A1667)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal.h)(0x65829150)
I (../Core/Inc/stm32l4xx_hal_conf.h)(0x68593DF1)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_def.h)(0x65829150)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l4xx.h)(0x6582914F)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l496xx.h)(0x6582914F)
I (../Drivers/CMSIS/Include/core_cm4.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x658290EB)
I (../Drivers/CMSIS/Include/mpu_armv7.h)(0x658290EB)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/system_stm32l4xx.h)(0x6582914F)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x65829150)
I (D:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x5CEB79E2)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_dma.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_cortex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_can.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_exti.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ramfunc.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_iwdg.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_spi.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_spi_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart_ex.h)(0x65829150)
I (..\UserCode\FreeModbus\modbus\include\mb.h)(0x68590A56)
I (..\UserCode\FreeModbus\modbus\include\mbport.h)(0x6858FC97)
I (..\UserCode\FreeModbus\modbus\include\mbproto.h)(0x6840A947)
I (..\UserCode\FreeModbus\modbus\include\mbconfig.h)(0x68590A78)
I (..\UserCode\FreeModbus\modbus\ascii\mbascii.h)(0x6840A947)
I (..\UserCode\FreeModbus\modbus\include\mbframe.h)(0x6840A947)
I (..\UserCode\FreeModbus\modbus\rtu\mbcrc.h)(0x6840A947)
F (..\UserCode\FreeModbus\modbus\functions\mbfunccoils.c)(0x68590A63)(--c99 -c --cpu Cortex-M4.fp -g -O1 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I ../Drivers/CMSIS/Device/ST/STM32L4xx/Include -I ../Drivers/CMSIS/Include -I ../UserCode/App/Include -I ../UserCode/Driver/Include -I ../UserCode/Support/Include -I ../UserCode/Service/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ..\UserCode\FreeModbus\modbus\ascii -I ..\UserCode\FreeModbus\modbus\include -I ..\UserCode\FreeModbus\port -I ..\UserCode\FreeModbus\modbus\rtu -I ..\UserCode\FreeModbus\modbus\tcp

-I.\RTE\_PowerGuard-L496

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.5.1\CMSIS\Core\Include

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32L4xx_DFP\2.6.0\Drivers\CMSIS\Device\ST\STM32L4xx\Include

-D__UVISION_VERSION="525" -D_RTE_ -DSTM32L496xx -DUSE_HAL_DRIVER -DSTM32L496xx

-o powerguard-l496\mbfunccoils.o --omf_browse powerguard-l496\mbfunccoils.crf --depend powerguard-l496\mbfunccoils.d)
I (D:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x5CEB79E2)
I (D:\Keil_v5\ARM\ARMCC\include\string.h)(0x5CEB79E2)
I (..\UserCode\FreeModbus\port\port.h)(0x6858FC97)
I (D:\Keil_v5\ARM\ARMCC\include\assert.h)(0x5CEB79D6)
I (D:\Keil_v5\ARM\ARMCC\include\inttypes.h)(0x5CEB79E0)
I (D:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5DC27889)
I (../Core/Inc/main.h)(0x685A1667)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal.h)(0x65829150)
I (../Core/Inc/stm32l4xx_hal_conf.h)(0x68593DF1)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_def.h)(0x65829150)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l4xx.h)(0x6582914F)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l496xx.h)(0x6582914F)
I (../Drivers/CMSIS/Include/core_cm4.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x658290EB)
I (../Drivers/CMSIS/Include/mpu_armv7.h)(0x658290EB)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/system_stm32l4xx.h)(0x6582914F)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x65829150)
I (D:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x5CEB79E2)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_dma.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_cortex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_can.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_exti.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ramfunc.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_iwdg.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_spi.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_spi_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart_ex.h)(0x65829150)
I (..\UserCode\FreeModbus\modbus\include\mb.h)(0x68590A56)
I (..\UserCode\FreeModbus\modbus\include\mbport.h)(0x6858FC97)
I (..\UserCode\FreeModbus\modbus\include\mbproto.h)(0x6840A947)
I (..\UserCode\FreeModbus\modbus\include\mbframe.h)(0x6840A947)
I (..\UserCode\FreeModbus\modbus\include\mbconfig.h)(0x68590A78)
F (..\UserCode\FreeModbus\modbus\functions\mbfuncdiag.c)(0x6840A947)(--c99 -c --cpu Cortex-M4.fp -g -O1 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I ../Drivers/CMSIS/Device/ST/STM32L4xx/Include -I ../Drivers/CMSIS/Include -I ../UserCode/App/Include -I ../UserCode/Driver/Include -I ../UserCode/Support/Include -I ../UserCode/Service/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ..\UserCode\FreeModbus\modbus\ascii -I ..\UserCode\FreeModbus\modbus\include -I ..\UserCode\FreeModbus\port -I ..\UserCode\FreeModbus\modbus\rtu -I ..\UserCode\FreeModbus\modbus\tcp

-I.\RTE\_PowerGuard-L496

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.5.1\CMSIS\Core\Include

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32L4xx_DFP\2.6.0\Drivers\CMSIS\Device\ST\STM32L4xx\Include

-D__UVISION_VERSION="525" -D_RTE_ -DSTM32L496xx -DUSE_HAL_DRIVER -DSTM32L496xx

-o powerguard-l496\mbfuncdiag.o --omf_browse powerguard-l496\mbfuncdiag.crf --depend powerguard-l496\mbfuncdiag.d)
F (..\UserCode\FreeModbus\modbus\functions\mbfuncdisc.c)(0x6840A947)(--c99 -c --cpu Cortex-M4.fp -g -O1 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I ../Drivers/CMSIS/Device/ST/STM32L4xx/Include -I ../Drivers/CMSIS/Include -I ../UserCode/App/Include -I ../UserCode/Driver/Include -I ../UserCode/Support/Include -I ../UserCode/Service/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ..\UserCode\FreeModbus\modbus\ascii -I ..\UserCode\FreeModbus\modbus\include -I ..\UserCode\FreeModbus\port -I ..\UserCode\FreeModbus\modbus\rtu -I ..\UserCode\FreeModbus\modbus\tcp

-I.\RTE\_PowerGuard-L496

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.5.1\CMSIS\Core\Include

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32L4xx_DFP\2.6.0\Drivers\CMSIS\Device\ST\STM32L4xx\Include

-D__UVISION_VERSION="525" -D_RTE_ -DSTM32L496xx -DUSE_HAL_DRIVER -DSTM32L496xx

-o powerguard-l496\mbfuncdisc.o --omf_browse powerguard-l496\mbfuncdisc.crf --depend powerguard-l496\mbfuncdisc.d)
I (D:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x5CEB79E2)
I (D:\Keil_v5\ARM\ARMCC\include\string.h)(0x5CEB79E2)
I (..\UserCode\FreeModbus\port\port.h)(0x6858FC97)
I (D:\Keil_v5\ARM\ARMCC\include\assert.h)(0x5CEB79D6)
I (D:\Keil_v5\ARM\ARMCC\include\inttypes.h)(0x5CEB79E0)
I (D:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5DC27889)
I (../Core/Inc/main.h)(0x685A1667)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal.h)(0x65829150)
I (../Core/Inc/stm32l4xx_hal_conf.h)(0x68593DF1)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_def.h)(0x65829150)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l4xx.h)(0x6582914F)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l496xx.h)(0x6582914F)
I (../Drivers/CMSIS/Include/core_cm4.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x658290EB)
I (../Drivers/CMSIS/Include/mpu_armv7.h)(0x658290EB)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/system_stm32l4xx.h)(0x6582914F)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x65829150)
I (D:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x5CEB79E2)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_dma.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_cortex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_can.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_exti.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ramfunc.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_iwdg.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_spi.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_spi_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart_ex.h)(0x65829150)
I (..\UserCode\FreeModbus\modbus\include\mb.h)(0x68590A56)
I (..\UserCode\FreeModbus\modbus\include\mbport.h)(0x6858FC97)
I (..\UserCode\FreeModbus\modbus\include\mbproto.h)(0x6840A947)
I (..\UserCode\FreeModbus\modbus\include\mbframe.h)(0x6840A947)
I (..\UserCode\FreeModbus\modbus\include\mbconfig.h)(0x68590A78)
F (..\UserCode\FreeModbus\modbus\functions\mbfuncfile.c)(0x6840A947)(--c99 -c --cpu Cortex-M4.fp -g -O1 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I ../Drivers/CMSIS/Device/ST/STM32L4xx/Include -I ../Drivers/CMSIS/Include -I ../UserCode/App/Include -I ../UserCode/Driver/Include -I ../UserCode/Support/Include -I ../UserCode/Service/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ..\UserCode\FreeModbus\modbus\ascii -I ..\UserCode\FreeModbus\modbus\include -I ..\UserCode\FreeModbus\port -I ..\UserCode\FreeModbus\modbus\rtu -I ..\UserCode\FreeModbus\modbus\tcp

-I.\RTE\_PowerGuard-L496

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.5.1\CMSIS\Core\Include

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32L4xx_DFP\2.6.0\Drivers\CMSIS\Device\ST\STM32L4xx\Include

-D__UVISION_VERSION="525" -D_RTE_ -DSTM32L496xx -DUSE_HAL_DRIVER -DSTM32L496xx

-o powerguard-l496\mbfuncfile.o --omf_browse powerguard-l496\mbfuncfile.crf --depend powerguard-l496\mbfuncfile.d)
I (D:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x5CEB79E2)
I (D:\Keil_v5\ARM\ARMCC\include\string.h)(0x5CEB79E2)
I (..\UserCode\FreeModbus\port\port.h)(0x6858FC97)
I (D:\Keil_v5\ARM\ARMCC\include\assert.h)(0x5CEB79D6)
I (D:\Keil_v5\ARM\ARMCC\include\inttypes.h)(0x5CEB79E0)
I (D:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5DC27889)
I (../Core/Inc/main.h)(0x685A1667)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal.h)(0x65829150)
I (../Core/Inc/stm32l4xx_hal_conf.h)(0x68593DF1)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_def.h)(0x65829150)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l4xx.h)(0x6582914F)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l496xx.h)(0x6582914F)
I (../Drivers/CMSIS/Include/core_cm4.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x658290EB)
I (../Drivers/CMSIS/Include/mpu_armv7.h)(0x658290EB)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/system_stm32l4xx.h)(0x6582914F)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x65829150)
I (D:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x5CEB79E2)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_dma.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_cortex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_can.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_exti.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ramfunc.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_iwdg.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_spi.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_spi_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart_ex.h)(0x65829150)
I (..\UserCode\FreeModbus\modbus\include\mb.h)(0x68590A56)
I (..\UserCode\FreeModbus\modbus\include\mbport.h)(0x6858FC97)
I (..\UserCode\FreeModbus\modbus\include\mbproto.h)(0x6840A947)
I (..\UserCode\FreeModbus\modbus\include\mbframe.h)(0x6840A947)
I (..\UserCode\FreeModbus\modbus\include\mbconfig.h)(0x68590A78)
F (..\UserCode\FreeModbus\modbus\functions\mbfuncholding.c)(0x68590A78)(--c99 -c --cpu Cortex-M4.fp -g -O1 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I ../Drivers/CMSIS/Device/ST/STM32L4xx/Include -I ../Drivers/CMSIS/Include -I ../UserCode/App/Include -I ../UserCode/Driver/Include -I ../UserCode/Support/Include -I ../UserCode/Service/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ..\UserCode\FreeModbus\modbus\ascii -I ..\UserCode\FreeModbus\modbus\include -I ..\UserCode\FreeModbus\port -I ..\UserCode\FreeModbus\modbus\rtu -I ..\UserCode\FreeModbus\modbus\tcp

-I.\RTE\_PowerGuard-L496

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.5.1\CMSIS\Core\Include

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32L4xx_DFP\2.6.0\Drivers\CMSIS\Device\ST\STM32L4xx\Include

-D__UVISION_VERSION="525" -D_RTE_ -DSTM32L496xx -DUSE_HAL_DRIVER -DSTM32L496xx

-o powerguard-l496\mbfuncholding.o --omf_browse powerguard-l496\mbfuncholding.crf --depend powerguard-l496\mbfuncholding.d)
I (D:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x5CEB79E2)
I (D:\Keil_v5\ARM\ARMCC\include\string.h)(0x5CEB79E2)
I (..\UserCode\FreeModbus\port\port.h)(0x6858FC97)
I (D:\Keil_v5\ARM\ARMCC\include\assert.h)(0x5CEB79D6)
I (D:\Keil_v5\ARM\ARMCC\include\inttypes.h)(0x5CEB79E0)
I (D:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5DC27889)
I (../Core/Inc/main.h)(0x685A1667)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal.h)(0x65829150)
I (../Core/Inc/stm32l4xx_hal_conf.h)(0x68593DF1)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_def.h)(0x65829150)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l4xx.h)(0x6582914F)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l496xx.h)(0x6582914F)
I (../Drivers/CMSIS/Include/core_cm4.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x658290EB)
I (../Drivers/CMSIS/Include/mpu_armv7.h)(0x658290EB)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/system_stm32l4xx.h)(0x6582914F)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x65829150)
I (D:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x5CEB79E2)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_dma.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_cortex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_can.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_exti.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ramfunc.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_iwdg.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_spi.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_spi_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart_ex.h)(0x65829150)
I (..\UserCode\FreeModbus\modbus\include\mb.h)(0x68590A56)
I (..\UserCode\FreeModbus\modbus\include\mbport.h)(0x6858FC97)
I (..\UserCode\FreeModbus\modbus\include\mbproto.h)(0x6840A947)
I (..\UserCode\FreeModbus\modbus\include\mbframe.h)(0x6840A947)
I (..\UserCode\FreeModbus\modbus\include\mbconfig.h)(0x68590A78)
F (..\UserCode\FreeModbus\modbus\functions\mbfuncinput.c)(0x6840A947)(--c99 -c --cpu Cortex-M4.fp -g -O1 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I ../Drivers/CMSIS/Device/ST/STM32L4xx/Include -I ../Drivers/CMSIS/Include -I ../UserCode/App/Include -I ../UserCode/Driver/Include -I ../UserCode/Support/Include -I ../UserCode/Service/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ..\UserCode\FreeModbus\modbus\ascii -I ..\UserCode\FreeModbus\modbus\include -I ..\UserCode\FreeModbus\port -I ..\UserCode\FreeModbus\modbus\rtu -I ..\UserCode\FreeModbus\modbus\tcp

-I.\RTE\_PowerGuard-L496

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.5.1\CMSIS\Core\Include

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32L4xx_DFP\2.6.0\Drivers\CMSIS\Device\ST\STM32L4xx\Include

-D__UVISION_VERSION="525" -D_RTE_ -DSTM32L496xx -DUSE_HAL_DRIVER -DSTM32L496xx

-o powerguard-l496\mbfuncinput.o --omf_browse powerguard-l496\mbfuncinput.crf --depend powerguard-l496\mbfuncinput.d)
I (D:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x5CEB79E2)
I (D:\Keil_v5\ARM\ARMCC\include\string.h)(0x5CEB79E2)
I (..\UserCode\FreeModbus\port\port.h)(0x6858FC97)
I (D:\Keil_v5\ARM\ARMCC\include\assert.h)(0x5CEB79D6)
I (D:\Keil_v5\ARM\ARMCC\include\inttypes.h)(0x5CEB79E0)
I (D:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5DC27889)
I (../Core/Inc/main.h)(0x685A1667)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal.h)(0x65829150)
I (../Core/Inc/stm32l4xx_hal_conf.h)(0x68593DF1)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_def.h)(0x65829150)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l4xx.h)(0x6582914F)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l496xx.h)(0x6582914F)
I (../Drivers/CMSIS/Include/core_cm4.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x658290EB)
I (../Drivers/CMSIS/Include/mpu_armv7.h)(0x658290EB)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/system_stm32l4xx.h)(0x6582914F)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x65829150)
I (D:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x5CEB79E2)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_dma.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_cortex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_can.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_exti.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ramfunc.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_iwdg.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_spi.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_spi_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart_ex.h)(0x65829150)
I (..\UserCode\FreeModbus\modbus\include\mb.h)(0x68590A56)
I (..\UserCode\FreeModbus\modbus\include\mbport.h)(0x6858FC97)
I (..\UserCode\FreeModbus\modbus\include\mbproto.h)(0x6840A947)
I (..\UserCode\FreeModbus\modbus\include\mbframe.h)(0x6840A947)
I (..\UserCode\FreeModbus\modbus\include\mbconfig.h)(0x68590A78)
F (..\UserCode\FreeModbus\modbus\functions\mbfuncother.c)(0x6840A947)(--c99 -c --cpu Cortex-M4.fp -g -O1 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I ../Drivers/CMSIS/Device/ST/STM32L4xx/Include -I ../Drivers/CMSIS/Include -I ../UserCode/App/Include -I ../UserCode/Driver/Include -I ../UserCode/Support/Include -I ../UserCode/Service/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ..\UserCode\FreeModbus\modbus\ascii -I ..\UserCode\FreeModbus\modbus\include -I ..\UserCode\FreeModbus\port -I ..\UserCode\FreeModbus\modbus\rtu -I ..\UserCode\FreeModbus\modbus\tcp

-I.\RTE\_PowerGuard-L496

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.5.1\CMSIS\Core\Include

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32L4xx_DFP\2.6.0\Drivers\CMSIS\Device\ST\STM32L4xx\Include

-D__UVISION_VERSION="525" -D_RTE_ -DSTM32L496xx -DUSE_HAL_DRIVER -DSTM32L496xx

-o powerguard-l496\mbfuncother.o --omf_browse powerguard-l496\mbfuncother.crf --depend powerguard-l496\mbfuncother.d)
I (D:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x5CEB79E2)
I (D:\Keil_v5\ARM\ARMCC\include\string.h)(0x5CEB79E2)
I (..\UserCode\FreeModbus\port\port.h)(0x6858FC97)
I (D:\Keil_v5\ARM\ARMCC\include\assert.h)(0x5CEB79D6)
I (D:\Keil_v5\ARM\ARMCC\include\inttypes.h)(0x5CEB79E0)
I (D:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5DC27889)
I (../Core/Inc/main.h)(0x685A1667)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal.h)(0x65829150)
I (../Core/Inc/stm32l4xx_hal_conf.h)(0x68593DF1)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_def.h)(0x65829150)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l4xx.h)(0x6582914F)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l496xx.h)(0x6582914F)
I (../Drivers/CMSIS/Include/core_cm4.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x658290EB)
I (../Drivers/CMSIS/Include/mpu_armv7.h)(0x658290EB)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/system_stm32l4xx.h)(0x6582914F)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x65829150)
I (D:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x5CEB79E2)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_dma.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_cortex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_can.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_exti.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ramfunc.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_iwdg.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_spi.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_spi_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart_ex.h)(0x65829150)
I (..\UserCode\FreeModbus\modbus\include\mb.h)(0x68590A56)
I (..\UserCode\FreeModbus\modbus\include\mbport.h)(0x6858FC97)
I (..\UserCode\FreeModbus\modbus\include\mbproto.h)(0x6840A947)
I (..\UserCode\FreeModbus\modbus\include\mbframe.h)(0x6840A947)
I (..\UserCode\FreeModbus\modbus\include\mbconfig.h)(0x68590A78)
F (..\UserCode\FreeModbus\modbus\functions\mbutils.c)(0x6840A947)(--c99 -c --cpu Cortex-M4.fp -g -O1 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I ../Drivers/CMSIS/Device/ST/STM32L4xx/Include -I ../Drivers/CMSIS/Include -I ../UserCode/App/Include -I ../UserCode/Driver/Include -I ../UserCode/Support/Include -I ../UserCode/Service/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ..\UserCode\FreeModbus\modbus\ascii -I ..\UserCode\FreeModbus\modbus\include -I ..\UserCode\FreeModbus\port -I ..\UserCode\FreeModbus\modbus\rtu -I ..\UserCode\FreeModbus\modbus\tcp

-I.\RTE\_PowerGuard-L496

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.5.1\CMSIS\Core\Include

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32L4xx_DFP\2.6.0\Drivers\CMSIS\Device\ST\STM32L4xx\Include

-D__UVISION_VERSION="525" -D_RTE_ -DSTM32L496xx -DUSE_HAL_DRIVER -DSTM32L496xx

-o powerguard-l496\mbutils.o --omf_browse powerguard-l496\mbutils.crf --depend powerguard-l496\mbutils.d)
I (D:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x5CEB79E2)
I (D:\Keil_v5\ARM\ARMCC\include\string.h)(0x5CEB79E2)
I (..\UserCode\FreeModbus\port\port.h)(0x6858FC97)
I (D:\Keil_v5\ARM\ARMCC\include\assert.h)(0x5CEB79D6)
I (D:\Keil_v5\ARM\ARMCC\include\inttypes.h)(0x5CEB79E0)
I (D:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5DC27889)
I (../Core/Inc/main.h)(0x685A1667)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal.h)(0x65829150)
I (../Core/Inc/stm32l4xx_hal_conf.h)(0x68593DF1)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_def.h)(0x65829150)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l4xx.h)(0x6582914F)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l496xx.h)(0x6582914F)
I (../Drivers/CMSIS/Include/core_cm4.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x658290EB)
I (../Drivers/CMSIS/Include/mpu_armv7.h)(0x658290EB)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/system_stm32l4xx.h)(0x6582914F)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x65829150)
I (D:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x5CEB79E2)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_dma.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_cortex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_can.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_exti.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ramfunc.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_iwdg.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_spi.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_spi_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart_ex.h)(0x65829150)
I (..\UserCode\FreeModbus\modbus\include\mb.h)(0x68590A56)
I (..\UserCode\FreeModbus\modbus\include\mbport.h)(0x6858FC97)
I (..\UserCode\FreeModbus\modbus\include\mbproto.h)(0x6840A947)
F (..\UserCode\FreeModbus\modbus\rtu\mbcrc.c)(0x6840A947)(--c99 -c --cpu Cortex-M4.fp -g -O1 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I ../Drivers/CMSIS/Device/ST/STM32L4xx/Include -I ../Drivers/CMSIS/Include -I ../UserCode/App/Include -I ../UserCode/Driver/Include -I ../UserCode/Support/Include -I ../UserCode/Service/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ..\UserCode\FreeModbus\modbus\ascii -I ..\UserCode\FreeModbus\modbus\include -I ..\UserCode\FreeModbus\port -I ..\UserCode\FreeModbus\modbus\rtu -I ..\UserCode\FreeModbus\modbus\tcp

-I.\RTE\_PowerGuard-L496

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.5.1\CMSIS\Core\Include

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32L4xx_DFP\2.6.0\Drivers\CMSIS\Device\ST\STM32L4xx\Include

-D__UVISION_VERSION="525" -D_RTE_ -DSTM32L496xx -DUSE_HAL_DRIVER -DSTM32L496xx

-o powerguard-l496\mbcrc.o --omf_browse powerguard-l496\mbcrc.crf --depend powerguard-l496\mbcrc.d)
I (..\UserCode\FreeModbus\port\port.h)(0x6858FC97)
I (D:\Keil_v5\ARM\ARMCC\include\assert.h)(0x5CEB79D6)
I (D:\Keil_v5\ARM\ARMCC\include\inttypes.h)(0x5CEB79E0)
I (D:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5DC27889)
I (../Core/Inc/main.h)(0x685A1667)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal.h)(0x65829150)
I (../Core/Inc/stm32l4xx_hal_conf.h)(0x68593DF1)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_def.h)(0x65829150)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l4xx.h)(0x6582914F)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l496xx.h)(0x6582914F)
I (../Drivers/CMSIS/Include/core_cm4.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x658290EB)
I (../Drivers/CMSIS/Include/mpu_armv7.h)(0x658290EB)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/system_stm32l4xx.h)(0x6582914F)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x65829150)
I (D:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x5CEB79E2)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_dma.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_cortex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_can.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_exti.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ramfunc.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_iwdg.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_spi.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_spi_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart_ex.h)(0x65829150)
F (..\UserCode\FreeModbus\modbus\rtu\mbrtu.c)(0x685A499A)(--c99 -c --cpu Cortex-M4.fp -g -O1 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I ../Drivers/CMSIS/Device/ST/STM32L4xx/Include -I ../Drivers/CMSIS/Include -I ../UserCode/App/Include -I ../UserCode/Driver/Include -I ../UserCode/Support/Include -I ../UserCode/Service/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ..\UserCode\FreeModbus\modbus\ascii -I ..\UserCode\FreeModbus\modbus\include -I ..\UserCode\FreeModbus\port -I ..\UserCode\FreeModbus\modbus\rtu -I ..\UserCode\FreeModbus\modbus\tcp

-I.\RTE\_PowerGuard-L496

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.5.1\CMSIS\Core\Include

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32L4xx_DFP\2.6.0\Drivers\CMSIS\Device\ST\STM32L4xx\Include

-D__UVISION_VERSION="525" -D_RTE_ -DSTM32L496xx -DUSE_HAL_DRIVER -DSTM32L496xx

-o powerguard-l496\mbrtu.o --omf_browse powerguard-l496\mbrtu.crf --depend powerguard-l496\mbrtu.d)
I (D:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x5CEB79E2)
I (D:\Keil_v5\ARM\ARMCC\include\string.h)(0x5CEB79E2)
I (..\UserCode\FreeModbus\port\port.h)(0x6858FC97)
I (D:\Keil_v5\ARM\ARMCC\include\assert.h)(0x5CEB79D6)
I (D:\Keil_v5\ARM\ARMCC\include\inttypes.h)(0x5CEB79E0)
I (D:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5DC27889)
I (../Core/Inc/main.h)(0x685A1667)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal.h)(0x65829150)
I (../Core/Inc/stm32l4xx_hal_conf.h)(0x68593DF1)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_def.h)(0x65829150)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l4xx.h)(0x6582914F)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l496xx.h)(0x6582914F)
I (../Drivers/CMSIS/Include/core_cm4.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x658290EB)
I (../Drivers/CMSIS/Include/mpu_armv7.h)(0x658290EB)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/system_stm32l4xx.h)(0x6582914F)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x65829150)
I (D:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x5CEB79E2)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_dma.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_cortex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_can.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_exti.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ramfunc.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_iwdg.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_spi.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_spi_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart_ex.h)(0x65829150)
I (..\UserCode\FreeModbus\modbus\include\mb.h)(0x68590A56)
I (..\UserCode\FreeModbus\modbus\include\mbport.h)(0x6858FC97)
I (..\UserCode\FreeModbus\modbus\include\mbproto.h)(0x6840A947)
I (..\UserCode\FreeModbus\modbus\rtu\mbrtu.h)(0x6840A947)
I (..\UserCode\FreeModbus\modbus\include\mbframe.h)(0x6840A947)
I (..\UserCode\FreeModbus\modbus\rtu\mbcrc.h)(0x6840A947)
F (..\UserCode\FreeModbus\modbus\tcp\mbtcp.c)(0x6840A947)(--c99 -c --cpu Cortex-M4.fp -g -O1 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I ../Drivers/CMSIS/Device/ST/STM32L4xx/Include -I ../Drivers/CMSIS/Include -I ../UserCode/App/Include -I ../UserCode/Driver/Include -I ../UserCode/Support/Include -I ../UserCode/Service/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ..\UserCode\FreeModbus\modbus\ascii -I ..\UserCode\FreeModbus\modbus\include -I ..\UserCode\FreeModbus\port -I ..\UserCode\FreeModbus\modbus\rtu -I ..\UserCode\FreeModbus\modbus\tcp

-I.\RTE\_PowerGuard-L496

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.5.1\CMSIS\Core\Include

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32L4xx_DFP\2.6.0\Drivers\CMSIS\Device\ST\STM32L4xx\Include

-D__UVISION_VERSION="525" -D_RTE_ -DSTM32L496xx -DUSE_HAL_DRIVER -DSTM32L496xx

-o powerguard-l496\mbtcp.o --omf_browse powerguard-l496\mbtcp.crf --depend powerguard-l496\mbtcp.d)
I (D:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x5CEB79E2)
I (D:\Keil_v5\ARM\ARMCC\include\string.h)(0x5CEB79E2)
I (..\UserCode\FreeModbus\port\port.h)(0x6858FC97)
I (D:\Keil_v5\ARM\ARMCC\include\assert.h)(0x5CEB79D6)
I (D:\Keil_v5\ARM\ARMCC\include\inttypes.h)(0x5CEB79E0)
I (D:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5DC27889)
I (../Core/Inc/main.h)(0x685A1667)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal.h)(0x65829150)
I (../Core/Inc/stm32l4xx_hal_conf.h)(0x68593DF1)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_def.h)(0x65829150)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l4xx.h)(0x6582914F)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l496xx.h)(0x6582914F)
I (../Drivers/CMSIS/Include/core_cm4.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x658290EB)
I (../Drivers/CMSIS/Include/mpu_armv7.h)(0x658290EB)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/system_stm32l4xx.h)(0x6582914F)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x65829150)
I (D:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x5CEB79E2)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_dma.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_cortex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_can.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_exti.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ramfunc.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_iwdg.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_spi.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_spi_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart_ex.h)(0x65829150)
I (..\UserCode\FreeModbus\modbus\include\mb.h)(0x68590A56)
I (..\UserCode\FreeModbus\modbus\include\mbport.h)(0x6858FC97)
I (..\UserCode\FreeModbus\modbus\include\mbproto.h)(0x6840A947)
I (..\UserCode\FreeModbus\modbus\include\mbconfig.h)(0x68590A78)
I (..\UserCode\FreeModbus\modbus\tcp\mbtcp.h)(0x6840A947)
I (..\UserCode\FreeModbus\modbus\include\mbframe.h)(0x6840A947)
F (..\UserCode\FreeModbus\modbus\mb.c)(0x685A48AB)(--c99 -c --cpu Cortex-M4.fp -g -O1 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I ../Drivers/CMSIS/Device/ST/STM32L4xx/Include -I ../Drivers/CMSIS/Include -I ../UserCode/App/Include -I ../UserCode/Driver/Include -I ../UserCode/Support/Include -I ../UserCode/Service/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ..\UserCode\FreeModbus\modbus\ascii -I ..\UserCode\FreeModbus\modbus\include -I ..\UserCode\FreeModbus\port -I ..\UserCode\FreeModbus\modbus\rtu -I ..\UserCode\FreeModbus\modbus\tcp

-I.\RTE\_PowerGuard-L496

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.5.1\CMSIS\Core\Include

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32L4xx_DFP\2.6.0\Drivers\CMSIS\Device\ST\STM32L4xx\Include

-D__UVISION_VERSION="525" -D_RTE_ -DSTM32L496xx -DUSE_HAL_DRIVER -DSTM32L496xx

-o powerguard-l496\mb.o --omf_browse powerguard-l496\mb.crf --depend powerguard-l496\mb.d)
I (D:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x5CEB79E2)
I (D:\Keil_v5\ARM\ARMCC\include\string.h)(0x5CEB79E2)
I (..\UserCode\FreeModbus\port\port.h)(0x6858FC97)
I (D:\Keil_v5\ARM\ARMCC\include\assert.h)(0x5CEB79D6)
I (D:\Keil_v5\ARM\ARMCC\include\inttypes.h)(0x5CEB79E0)
I (D:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5DC27889)
I (../Core/Inc/main.h)(0x685A1667)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal.h)(0x65829150)
I (../Core/Inc/stm32l4xx_hal_conf.h)(0x68593DF1)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_def.h)(0x65829150)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l4xx.h)(0x6582914F)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l496xx.h)(0x6582914F)
I (../Drivers/CMSIS/Include/core_cm4.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x658290EB)
I (../Drivers/CMSIS/Include/mpu_armv7.h)(0x658290EB)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/system_stm32l4xx.h)(0x6582914F)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x65829150)
I (D:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x5CEB79E2)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_dma.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_cortex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_can.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_exti.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ramfunc.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_iwdg.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_spi.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_spi_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart_ex.h)(0x65829150)
I (..\UserCode\FreeModbus\modbus\include\mb.h)(0x68590A56)
I (..\UserCode\FreeModbus\modbus\include\mbport.h)(0x6858FC97)
I (..\UserCode\FreeModbus\modbus\include\mbproto.h)(0x6840A947)
I (..\UserCode\FreeModbus\modbus\include\mbconfig.h)(0x68590A78)
I (..\UserCode\FreeModbus\modbus\include\mbframe.h)(0x6840A947)
I (..\UserCode\FreeModbus\modbus\include\mbfunc.h)(0x6840A947)
I (..\UserCode\FreeModbus\modbus\rtu\mbrtu.h)(0x6840A947)
F (..\UserCode\FreeModbus\modbus\mdcb.c)(0x685A3E55)(--c99 -c --cpu Cortex-M4.fp -g -O1 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I ../Drivers/CMSIS/Device/ST/STM32L4xx/Include -I ../Drivers/CMSIS/Include -I ../UserCode/App/Include -I ../UserCode/Driver/Include -I ../UserCode/Support/Include -I ../UserCode/Service/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ..\UserCode\FreeModbus\modbus\ascii -I ..\UserCode\FreeModbus\modbus\include -I ..\UserCode\FreeModbus\port -I ..\UserCode\FreeModbus\modbus\rtu -I ..\UserCode\FreeModbus\modbus\tcp

-I.\RTE\_PowerGuard-L496

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.5.1\CMSIS\Core\Include

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32L4xx_DFP\2.6.0\Drivers\CMSIS\Device\ST\STM32L4xx\Include

-D__UVISION_VERSION="525" -D_RTE_ -DSTM32L496xx -DUSE_HAL_DRIVER -DSTM32L496xx

-o powerguard-l496\mdcb.o --omf_browse powerguard-l496\mdcb.crf --depend powerguard-l496\mdcb.d)
I (..\UserCode\FreeModbus\modbus\include\mb.h)(0x68590A56)
I (..\UserCode\FreeModbus\port\port.h)(0x6858FC97)
I (D:\Keil_v5\ARM\ARMCC\include\assert.h)(0x5CEB79D6)
I (D:\Keil_v5\ARM\ARMCC\include\inttypes.h)(0x5CEB79E0)
I (D:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5DC27889)
I (../Core/Inc/main.h)(0x685A1667)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal.h)(0x65829150)
I (../Core/Inc/stm32l4xx_hal_conf.h)(0x68593DF1)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_def.h)(0x65829150)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l4xx.h)(0x6582914F)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l496xx.h)(0x6582914F)
I (../Drivers/CMSIS/Include/core_cm4.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x658290EB)
I (../Drivers/CMSIS/Include/mpu_armv7.h)(0x658290EB)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/system_stm32l4xx.h)(0x6582914F)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x65829150)
I (D:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x5CEB79E2)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_dma.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_cortex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_can.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_exti.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ramfunc.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_iwdg.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_spi.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_spi_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart_ex.h)(0x65829150)
I (..\UserCode\FreeModbus\modbus\include\mbport.h)(0x6858FC97)
I (..\UserCode\FreeModbus\modbus\include\mbproto.h)(0x6840A947)
I (../UserCode/App/Include/modbus_test.h)(0x685A3DD7)
F (..\UserCode\FreeModbus\port\portevent.c)(0x6840A947)(--c99 -c --cpu Cortex-M4.fp -g -O1 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I ../Drivers/CMSIS/Device/ST/STM32L4xx/Include -I ../Drivers/CMSIS/Include -I ../UserCode/App/Include -I ../UserCode/Driver/Include -I ../UserCode/Support/Include -I ../UserCode/Service/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ..\UserCode\FreeModbus\modbus\ascii -I ..\UserCode\FreeModbus\modbus\include -I ..\UserCode\FreeModbus\port -I ..\UserCode\FreeModbus\modbus\rtu -I ..\UserCode\FreeModbus\modbus\tcp

-I.\RTE\_PowerGuard-L496

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.5.1\CMSIS\Core\Include

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32L4xx_DFP\2.6.0\Drivers\CMSIS\Device\ST\STM32L4xx\Include

-D__UVISION_VERSION="525" -D_RTE_ -DSTM32L496xx -DUSE_HAL_DRIVER -DSTM32L496xx

-o powerguard-l496\portevent.o --omf_browse powerguard-l496\portevent.crf --depend powerguard-l496\portevent.d)
I (..\UserCode\FreeModbus\modbus\include\mb.h)(0x68590A56)
I (..\UserCode\FreeModbus\port\port.h)(0x6858FC97)
I (D:\Keil_v5\ARM\ARMCC\include\assert.h)(0x5CEB79D6)
I (D:\Keil_v5\ARM\ARMCC\include\inttypes.h)(0x5CEB79E0)
I (D:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5DC27889)
I (../Core/Inc/main.h)(0x685A1667)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal.h)(0x65829150)
I (../Core/Inc/stm32l4xx_hal_conf.h)(0x68593DF1)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_def.h)(0x65829150)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l4xx.h)(0x6582914F)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l496xx.h)(0x6582914F)
I (../Drivers/CMSIS/Include/core_cm4.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x658290EB)
I (../Drivers/CMSIS/Include/mpu_armv7.h)(0x658290EB)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/system_stm32l4xx.h)(0x6582914F)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x65829150)
I (D:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x5CEB79E2)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_dma.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_cortex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_can.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_exti.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ramfunc.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_iwdg.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_spi.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_spi_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart_ex.h)(0x65829150)
I (..\UserCode\FreeModbus\modbus\include\mbport.h)(0x6858FC97)
I (..\UserCode\FreeModbus\modbus\include\mbproto.h)(0x6840A947)
F (..\UserCode\FreeModbus\port\portserial.c)(0x685A418A)(--c99 -c --cpu Cortex-M4.fp -g -O1 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I ../Drivers/CMSIS/Device/ST/STM32L4xx/Include -I ../Drivers/CMSIS/Include -I ../UserCode/App/Include -I ../UserCode/Driver/Include -I ../UserCode/Support/Include -I ../UserCode/Service/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ..\UserCode\FreeModbus\modbus\ascii -I ..\UserCode\FreeModbus\modbus\include -I ..\UserCode\FreeModbus\port -I ..\UserCode\FreeModbus\modbus\rtu -I ..\UserCode\FreeModbus\modbus\tcp

-I.\RTE\_PowerGuard-L496

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.5.1\CMSIS\Core\Include

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32L4xx_DFP\2.6.0\Drivers\CMSIS\Device\ST\STM32L4xx\Include

-D__UVISION_VERSION="525" -D_RTE_ -DSTM32L496xx -DUSE_HAL_DRIVER -DSTM32L496xx

-o powerguard-l496\portserial.o --omf_browse powerguard-l496\portserial.crf --depend powerguard-l496\portserial.d)
I (../Core/Inc/usart.h)(0x68593DEF)
I (../Core/Inc/main.h)(0x685A1667)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal.h)(0x65829150)
I (../Core/Inc/stm32l4xx_hal_conf.h)(0x68593DF1)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_def.h)(0x65829150)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l4xx.h)(0x6582914F)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l496xx.h)(0x6582914F)
I (../Drivers/CMSIS/Include/core_cm4.h)(0x658290EB)
I (D:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5DC27889)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x658290EB)
I (../Drivers/CMSIS/Include/mpu_armv7.h)(0x658290EB)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/system_stm32l4xx.h)(0x6582914F)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x65829150)
I (D:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x5CEB79E2)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_dma.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_cortex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_can.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_exti.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ramfunc.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_iwdg.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_spi.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_spi_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart_ex.h)(0x65829150)
I (../UserCode/Driver/Include/rs485.h)(0x67E603BF)
I (../Core/Inc/gpio.h)(0x68593DE6)
I (..\UserCode\FreeModbus\port\port.h)(0x6858FC97)
I (D:\Keil_v5\ARM\ARMCC\include\assert.h)(0x5CEB79D6)
I (D:\Keil_v5\ARM\ARMCC\include\inttypes.h)(0x5CEB79E0)
I (..\UserCode\FreeModbus\modbus\include\mb.h)(0x68590A56)
I (..\UserCode\FreeModbus\modbus\include\mbport.h)(0x6858FC97)
I (..\UserCode\FreeModbus\modbus\include\mbproto.h)(0x6840A947)
I (../UserCode/App/Include/modbus_test.h)(0x685A3DD7)
F (..\UserCode\FreeModbus\port\porttimer.c)(0x685A39A6)(--c99 -c --cpu Cortex-M4.fp -g -O1 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I ../Drivers/CMSIS/Device/ST/STM32L4xx/Include -I ../Drivers/CMSIS/Include -I ../UserCode/App/Include -I ../UserCode/Driver/Include -I ../UserCode/Support/Include -I ../UserCode/Service/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I ..\UserCode\FreeModbus\modbus\ascii -I ..\UserCode\FreeModbus\modbus\include -I ..\UserCode\FreeModbus\port -I ..\UserCode\FreeModbus\modbus\rtu -I ..\UserCode\FreeModbus\modbus\tcp

-I.\RTE\_PowerGuard-L496

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.5.1\CMSIS\Core\Include

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32L4xx_DFP\2.6.0\Drivers\CMSIS\Device\ST\STM32L4xx\Include

-D__UVISION_VERSION="525" -D_RTE_ -DSTM32L496xx -DUSE_HAL_DRIVER -DSTM32L496xx

-o powerguard-l496\porttimer.o --omf_browse powerguard-l496\porttimer.crf --depend powerguard-l496\porttimer.d)
I (../Core/Inc/tim.h)(0x68593DEE)
I (../Core/Inc/main.h)(0x685A1667)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal.h)(0x65829150)
I (../Core/Inc/stm32l4xx_hal_conf.h)(0x68593DF1)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_def.h)(0x65829150)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l4xx.h)(0x6582914F)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l496xx.h)(0x6582914F)
I (../Drivers/CMSIS/Include/core_cm4.h)(0x658290EB)
I (D:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5DC27889)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x658290EB)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x658290EB)
I (../Drivers/CMSIS/Include/mpu_armv7.h)(0x658290EB)
I (../Drivers/CMSIS/Device/ST/STM32L4xx/Include/system_stm32l4xx.h)(0x6582914F)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x65829150)
I (D:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x5CEB79E2)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_dma.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_cortex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_can.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_exti.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ramfunc.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_iwdg.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_spi.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_spi_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim_ex.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart.h)(0x65829150)
I (../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart_ex.h)(0x65829150)
I (..\UserCode\FreeModbus\port\port.h)(0x6858FC97)
I (D:\Keil_v5\ARM\ARMCC\include\assert.h)(0x5CEB79D6)
I (D:\Keil_v5\ARM\ARMCC\include\inttypes.h)(0x5CEB79E0)
I (..\UserCode\FreeModbus\modbus\include\mb.h)(0x68590A56)
I (..\UserCode\FreeModbus\modbus\include\mbport.h)(0x6858FC97)
I (..\UserCode\FreeModbus\modbus\include\mbproto.h)(0x6840A947)
I (../UserCode/App/Include/modbus_test.h)(0x685A3DD7)
