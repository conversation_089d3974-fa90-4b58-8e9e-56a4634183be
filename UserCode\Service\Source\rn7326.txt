// rn7326.c
#include "spi.h"
#include "rn7326.h"
#include "rn7326e_regs.h"
#include <string.h>

/**
 * @brief  向RN7326芯片写入数据
 * @param  cmd   写命令，bit7=1
 * @param  addr  写入的起始地址
 * @param  len   写入的数据长度
 * @param  data  指向待写入数据的指针
 * @retval status 上一次操作的状态反馈
 *
 * 步骤说明：
 * 1. 构造写命令和数据包，计算校验和
 * 2. 拉低片选，延时，读取前序状态
 * 3. 发送数据包
 * 4. 拉高片选，延时
 * 5. 返回前序状态
 */
uint8_t RN7326_Write(uint8_t cmd, uint16_t addr, uint16_t len, uint8_t *data)
{
    uint8_t buf[520 + 6]; // 最大支持512字节数据
    uint16_t i = 0;
    uint8_t checksum = 0;

    // 构造写命令字节，bit7=1 表示写操作
    buf[i++] = 0x80 | (cmd & 0x7F); // 写命令：bit7=1
    // 写入地址低8位
    buf[i++] = addr & 0xFF;
    // 写入地址高8位
    buf[i++] = (addr >> 8) & 0xFF;
    // 数据长度低8位
    buf[i++] = len & 0xFF;
    // 数据长度高8位
    buf[i++] = (len >> 8) & 0xFF;

    // 计算命令和地址、长度的校验和
    checksum = buf[0] + buf[1] + buf[2] + buf[3] + buf[4];

    // 拷贝数据并累加校验和
    for (uint16_t j = 0; j < len; j++) {
        buf[i++] = data[j];
        checksum += data[j];
    }

    // 校验和取反后作为最后一个字节
    buf[i++] = ~checksum; // 校验和

    // 片选拉低，准备通信
    RN7326_CS_LOW();
    HAL_Delay(1); // T1 > 20us，确保时序

    uint8_t status;
    // 读取前序状态反馈
    HAL_SPI_Receive(&hspi2, &status, 1, HAL_MAX_DELAY); // 前序状态反馈
    // 发送命令和数据包
    HAL_SPI_Transmit(&hspi2, buf, i, HAL_MAX_DELAY);

    // 片选拉高，结束本次通信
    RN7326_CS_HIGH();
    HAL_Delay(1); // T2 > 2us，确保时序

    return status; // 可用于判断上一次状态
}

/**
 * @brief  从RN7326芯片读取数据
 * @param  cmd   读命令，bit7=0
 * @param  addr  读取的起始地址
 * @param  len   读取的数据长度
 * @param  rxbuf 指向接收数据缓冲区的指针
 * @retval 0     读取成功
 * @retval 1     校验失败
 *
 * 步骤说明：
 * 1. 构造读命令包，计算校验和
 * 2. 拉低片选，延时，读取前序状态
 * 3. 发送命令包
 * 4. 拉高片选，延时
 * 5. 再次拉低片选，读取数据和校验和
 * 6. 校验数据完整性
 */
uint8_t RN7326_Read(uint8_t cmd, uint16_t addr, uint16_t len, uint8_t *rxbuf)
{
    uint8_t cmd_buf[6];
    uint8_t dummy[514] = {0}; // 发送的虚拟数据，用于时钟同步
    uint8_t rx_raw[514] = {0}; // 原始接收数据，包含状态和校验和
    uint8_t checksum = 0;

    // 构造读命令字节，bit7=0 表示读操作
    cmd_buf[0] = cmd & 0x7F; // 读命令：bit7=0
    // 读取地址低8位
    cmd_buf[1] = addr & 0xFF;
    // 读取地址高8位
    cmd_buf[2] = (addr >> 8) & 0xFF;
    // 数据长度低8位
    cmd_buf[3] = len & 0xFF;
    // 数据长度高8位
    cmd_buf[4] = (len >> 8) & 0xFF;

    // 计算命令和地址、长度的校验和
    for (int i = 0; i < 5; i++) checksum += cmd_buf[i];
    // 校验和取反后作为最后一个字节
    cmd_buf[5] = ~checksum;

    RN7326_CS_LOW();
    HAL_Delay(1); // T1 > 20us，确保时序

    uint8_t status;
    // 读取前序状态反馈
    HAL_SPI_Receive(&hspi2, &status, 1, HAL_MAX_DELAY); // 前序反馈
    // 发送命令包
    HAL_SPI_Transmit(&hspi2, cmd_buf, 6, HAL_MAX_DELAY);

    RN7326_CS_HIGH();
    HAL_Delay(1); // T3 > 600us，确保时序

    RN7326_CS_LOW();
    // 发送dummy数据，同时接收原始数据（第一个字节为状态，最后一个字节为校验和）
    HAL_SPI_TransmitReceive(&hspi2, dummy, rx_raw, len + 2, HAL_MAX_DELAY);
    RN7326_CS_HIGH();

    uint8_t read_status = rx_raw[0]; // 读取到的状态字节
    uint8_t rx_checksum = rx_raw[len + 1]; // 接收到的校验和
    uint8_t sum = read_status;
    // 拷贝有效数据到rxbuf，并累加校验和
    for (int i = 0; i < len; i++) {
        rxbuf[i] = rx_raw[i + 1];
        sum += rxbuf[i];
    }

    // 校验和验证，若不符则返回1
    if ((uint8_t)(~sum) != rx_checksum)
        return 1; // 校验失败

    return 0; // OK
}


