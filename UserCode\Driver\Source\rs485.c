// rs485.c
#include "usart.h"
#include "rs485.h"
#include "gpio.h"
#include "cmsis_os2.h"
#include <string.h>

static uint32_t last_rx_tick = 0;
static uint8_t rx_buf[UART_RX_BUF_SIZE];
static volatile uint16_t rx_len = 0;

void RS485_Init(void) {
    GPIO_InitTypeDef GPIO_InitStruct = {0};
    GPIO_InitStruct.Pin = RS485_DE_RE_PIN;
    GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_HIGH;
    HAL_GPIO_Init(RS485_DE_RE_GPIO, &GPIO_InitStruct);
    HAL_GPIO_WritePin(RS485_DE_RE_GPIO, RS485_DE_RE_PIN, GPIO_PIN_RESET);
}

void RS485_Enter_TX_Mode(void) {
    HAL_GPIO_WritePin(RS485_DE_RE_GPIO, RS485_DE_RE_PIN, GPIO_PIN_SET);
    osDelay(1); // 保证收发器稳定
}

void RS485_Enter_RX_Mode(void) {
    HAL_GPIO_WritePin(RS485_DE_RE_GPIO, RS485_DE_RE_PIN, GPIO_PIN_RESET);
    osDelay(1); // 保证收发器稳定
}

uint32_t RS485_Get_Line_Idle_Time(void) {
    return osKernelGetTickCount() - last_rx_tick;
}

void UART_DMA_Init(UART_HandleTypeDef *huart) {
    HAL_UART_Receive_DMA(huart, rx_buf, UART_RX_BUF_SIZE);
    __HAL_DMA_DISABLE_IT(huart->hdmarx, DMA_IT_HT); // 仅使能传输完成中断
}
// 启动DMA接收
void UART_Start_DMA_Receive(void)
{
    // 清除DMA接收缓冲区
    memset(rx_buf, 0, UART_RX_BUF_SIZE);
    
    // 启动DMA接收
    HAL_UART_Receive_DMA(&huart1, rx_buf, UART_RX_BUF_SIZE);
    
    // 清除UART错误标志
    __HAL_UART_CLEAR_FLAG(&huart1, UART_FLAG_ORE | UART_FLAG_FE | UART_FLAG_NE);
}

//// 接收完成回调
//void HAL_UART_RxCpltCallback(UART_HandleTypeDef *huart) {
//    if(huart->Instance == USART1) {
//        rx_len = UART_RX_BUF_SIZE - __HAL_DMA_GET_COUNTER(huart->hdmarx);
//        RS485_Enter_RX_Mode();
//    }
//}

uint8_t* UART_Get_Rx_Buffer(void) {
    return rx_buf;
}

uint16_t UART_Get_Rx_Length(void) {
    return rx_len;
}


