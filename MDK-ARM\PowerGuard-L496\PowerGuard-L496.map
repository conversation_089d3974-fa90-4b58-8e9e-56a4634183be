Component: ARM Compiler 5.06 update 6 (build 750) Tool: armlink [4d35ed]

==============================================================================

Section Cross References

    startup_stm32l496xx.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32l496xx.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32l496xx.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32l496xx.o(RESET) refers to startup_stm32l496xx.o(STACK) for __initial_sp
    startup_stm32l496xx.o(RESET) refers to startup_stm32l496xx.o(.text) for Reset_Handler
    startup_stm32l496xx.o(RESET) refers to stm32l4xx_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32l496xx.o(RESET) refers to stm32l4xx_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32l496xx.o(RESET) refers to stm32l4xx_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32l496xx.o(RESET) refers to stm32l4xx_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32l496xx.o(RESET) refers to stm32l4xx_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32l496xx.o(RESET) refers to port.o(.emb_text) for SVC_Handler
    startup_stm32l496xx.o(RESET) refers to stm32l4xx_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32l496xx.o(RESET) refers to cmsis_os2.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32l496xx.o(RESET) refers to stm32l4xx_it.o(i.EXTI1_IRQHandler) for EXTI1_IRQHandler
    startup_stm32l496xx.o(RESET) refers to stm32l4xx_it.o(i.EXTI2_IRQHandler) for EXTI2_IRQHandler
    startup_stm32l496xx.o(RESET) refers to stm32l4xx_it.o(i.CAN1_RX0_IRQHandler) for CAN1_RX0_IRQHandler
    startup_stm32l496xx.o(RESET) refers to stm32l4xx_it.o(i.TIM1_UP_TIM16_IRQHandler) for TIM1_UP_TIM16_IRQHandler
    startup_stm32l496xx.o(RESET) refers to stm32l4xx_it.o(i.USART1_IRQHandler) for USART1_IRQHandler
    startup_stm32l496xx.o(RESET) refers to stm32l4xx_it.o(i.TIM6_DAC_IRQHandler) for TIM6_DAC_IRQHandler
    startup_stm32l496xx.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32l496xx.o(.text) refers to system_stm32l4xx.o(i.SystemInit) for SystemInit
    startup_stm32l496xx.o(.text) refers to __main.o(!!!main) for __main
    startup_stm32l496xx.o(.text) refers to startup_stm32l496xx.o(HEAP) for Heap_Mem
    startup_stm32l496xx.o(.text) refers to startup_stm32l496xx.o(STACK) for Stack_Mem
    main.o(i.HAL_TIM_PeriodElapsedCallback) refers to stm32l4xx_hal.o(i.HAL_IncTick) for HAL_IncTick
    main.o(i.SystemClock_Config) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    main.o(i.SystemClock_Config) refers to stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling) for HAL_PWREx_ControlVoltageScaling
    main.o(i.SystemClock_Config) refers to main.o(i.Error_Handler) for Error_Handler
    main.o(i.SystemClock_Config) refers to stm32l4xx_hal_rcc.o(i.HAL_RCC_OscConfig) for HAL_RCC_OscConfig
    main.o(i.SystemClock_Config) refers to stm32l4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) for HAL_RCC_ClockConfig
    main.o(i.main) refers to stm32l4xx_hal.o(i.HAL_Init) for HAL_Init
    main.o(i.main) refers to main.o(i.SystemClock_Config) for SystemClock_Config
    main.o(i.main) refers to gpio.o(i.MX_GPIO_Init) for MX_GPIO_Init
    main.o(i.main) refers to can.o(i.MX_CAN1_Init) for MX_CAN1_Init
    main.o(i.main) refers to i2c.o(i.MX_I2C1_Init) for MX_I2C1_Init
    main.o(i.main) refers to i2c.o(i.MX_I2C2_Init) for MX_I2C2_Init
    main.o(i.main) refers to spi.o(i.MX_SPI2_Init) for MX_SPI2_Init
    main.o(i.main) refers to usart.o(i.MX_USART1_UART_Init) for MX_USART1_UART_Init
    main.o(i.main) refers to tim.o(i.MX_TIM16_Init) for MX_TIM16_Init
    main.o(i.main) refers to cmsis_os2.o(i.osKernelInitialize) for osKernelInitialize
    main.o(i.main) refers to freertos.o(i.MX_FREERTOS_Init) for MX_FREERTOS_Init
    main.o(i.main) refers to cmsis_os2.o(i.osKernelStart) for osKernelStart
    gpio.o(i.MX_GPIO_Init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    gpio.o(i.MX_GPIO_Init) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    gpio.o(i.MX_GPIO_Init) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    gpio.o(i.MX_GPIO_Init) refers to stm32l4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    gpio.o(i.MX_GPIO_Init) refers to stm32l4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    freertos.o(i.CANComTask) refers to cmsis_os2.o(i.osDelay) for osDelay
    freertos.o(i.EnergyMeterTask) refers to rn7326.o(i.test_read_LostVoltage) for test_read_LostVoltage
    freertos.o(i.EnergyMeterTask) refers to rn7326.o(i.test_read_voltage) for test_read_voltage
    freertos.o(i.EnergyMeterTask) refers to cmsis_os2.o(i.osDelay) for osDelay
    freertos.o(i.MX_FREERTOS_Init) refers to cmsis_os2.o(i.osThreadNew) for osThreadNew
    freertos.o(i.MX_FREERTOS_Init) refers to freertos.o(.constdata) for .constdata
    freertos.o(i.MX_FREERTOS_Init) refers to freertos.o(i.EnergyMeterTask) for EnergyMeterTask
    freertos.o(i.MX_FREERTOS_Init) refers to freertos.o(.data) for .data
    freertos.o(i.MX_FREERTOS_Init) refers to freertos.o(i.ModbusComTask) for ModbusComTask
    freertos.o(i.MX_FREERTOS_Init) refers to freertos.o(i.CANComTask) for CANComTask
    freertos.o(i.MX_FREERTOS_Init) refers to freertos.o(i.SensorsTask) for SensorsTask
    freertos.o(i.MX_FREERTOS_Init) refers to freertos.o(i.SysMonitorTask) for SysMonitorTask
    freertos.o(i.ModbusComTask) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    freertos.o(i.ModbusComTask) refers to cmsis_os2.o(i.osDelay) for osDelay
    freertos.o(i.ModbusComTask) refers to modbus_test.o(i.Modbus_Test_Init) for Modbus_Test_Init
    freertos.o(i.ModbusComTask) refers to rs485.o(i.RS485_Init) for RS485_Init
    freertos.o(i.ModbusComTask) refers to rs485.o(i.RS485_Enter_RX_Mode) for RS485_Enter_RX_Mode
    freertos.o(i.ModbusComTask) refers to mb.o(i.eMBInit) for eMBInit
    freertos.o(i.ModbusComTask) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_TogglePin) for HAL_GPIO_TogglePin
    freertos.o(i.ModbusComTask) refers to mb.o(i.eMBEnable) for eMBEnable
    freertos.o(i.ModbusComTask) refers to mb.o(i.eMBPoll) for eMBPoll
    freertos.o(i.ModbusComTask) refers to modbus_test.o(i.Modbus_Test_Inc_Poll_Count) for Modbus_Test_Inc_Poll_Count
    freertos.o(i.ModbusComTask) refers to modbus_test.o(i.Modbus_Test_Update_Registers) for Modbus_Test_Update_Registers
    freertos.o(i.ModbusComTask) refers to modbus_test.o(i.Modbus_Test_LED_Indicate) for Modbus_Test_LED_Indicate
    freertos.o(i.ModbusComTask) refers to freertos.o(.data) for .data
    freertos.o(i.SensorsTask) refers to cmsis_os2.o(i.osDelay) for osDelay
    freertos.o(i.SysMonitorTask) refers to stm32l4xx_hal.o(i.HAL_Delay) for HAL_Delay
    freertos.o(.constdata) refers to freertos.o(.conststring) for .conststring
    can.o(i.HAL_CAN_MspDeInit) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    can.o(i.HAL_CAN_MspDeInit) refers to stm32l4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    can.o(i.HAL_CAN_MspInit) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    can.o(i.HAL_CAN_MspInit) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    can.o(i.HAL_CAN_MspInit) refers to stm32l4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    can.o(i.HAL_CAN_MspInit) refers to stm32l4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    can.o(i.MX_CAN1_Init) refers to stm32l4xx_hal_can.o(i.HAL_CAN_Init) for HAL_CAN_Init
    can.o(i.MX_CAN1_Init) refers to main.o(i.Error_Handler) for Error_Handler
    can.o(i.MX_CAN1_Init) refers to can.o(.bss) for .bss
    i2c.o(i.HAL_I2C_MspDeInit) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    i2c.o(i.HAL_I2C_MspInit) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    i2c.o(i.HAL_I2C_MspInit) refers to stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) for HAL_RCCEx_PeriphCLKConfig
    i2c.o(i.HAL_I2C_MspInit) refers to main.o(i.Error_Handler) for Error_Handler
    i2c.o(i.HAL_I2C_MspInit) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    i2c.o(i.MX_I2C1_Init) refers to stm32l4xx_hal_i2c.o(i.HAL_I2C_Init) for HAL_I2C_Init
    i2c.o(i.MX_I2C1_Init) refers to main.o(i.Error_Handler) for Error_Handler
    i2c.o(i.MX_I2C1_Init) refers to stm32l4xx_hal_i2c_ex.o(i.HAL_I2CEx_ConfigAnalogFilter) for HAL_I2CEx_ConfigAnalogFilter
    i2c.o(i.MX_I2C1_Init) refers to stm32l4xx_hal_i2c_ex.o(i.HAL_I2CEx_ConfigDigitalFilter) for HAL_I2CEx_ConfigDigitalFilter
    i2c.o(i.MX_I2C1_Init) refers to i2c.o(.bss) for .bss
    i2c.o(i.MX_I2C2_Init) refers to stm32l4xx_hal_i2c.o(i.HAL_I2C_Init) for HAL_I2C_Init
    i2c.o(i.MX_I2C2_Init) refers to main.o(i.Error_Handler) for Error_Handler
    i2c.o(i.MX_I2C2_Init) refers to stm32l4xx_hal_i2c_ex.o(i.HAL_I2CEx_ConfigAnalogFilter) for HAL_I2CEx_ConfigAnalogFilter
    i2c.o(i.MX_I2C2_Init) refers to stm32l4xx_hal_i2c_ex.o(i.HAL_I2CEx_ConfigDigitalFilter) for HAL_I2CEx_ConfigDigitalFilter
    i2c.o(i.MX_I2C2_Init) refers to i2c.o(.bss) for .bss
    iwdg.o(i.MX_IWDG_Init) refers to stm32l4xx_hal_iwdg.o(i.HAL_IWDG_Init) for HAL_IWDG_Init
    iwdg.o(i.MX_IWDG_Init) refers to main.o(i.Error_Handler) for Error_Handler
    iwdg.o(i.MX_IWDG_Init) refers to iwdg.o(.bss) for .bss
    spi.o(i.HAL_SPI_MspDeInit) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    spi.o(i.HAL_SPI_MspInit) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    spi.o(i.HAL_SPI_MspInit) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    spi.o(i.MX_SPI2_Init) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_Init) for HAL_SPI_Init
    spi.o(i.MX_SPI2_Init) refers to main.o(i.Error_Handler) for Error_Handler
    spi.o(i.MX_SPI2_Init) refers to spi.o(.bss) for .bss
    tim.o(i.HAL_TIM_Base_MspDeInit) refers to stm32l4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    tim.o(i.HAL_TIM_Base_MspInit) refers to stm32l4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    tim.o(i.HAL_TIM_Base_MspInit) refers to stm32l4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    tim.o(i.MX_TIM16_Init) refers to stm32l4xx_hal_tim.o(i.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    tim.o(i.MX_TIM16_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM16_Init) refers to tim.o(.bss) for .bss
    usart.o(i.HAL_UART_MspDeInit) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    usart.o(i.HAL_UART_MspDeInit) refers to stm32l4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    usart.o(i.HAL_UART_MspInit) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    usart.o(i.HAL_UART_MspInit) refers to stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) for HAL_RCCEx_PeriphCLKConfig
    usart.o(i.HAL_UART_MspInit) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.HAL_UART_MspInit) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    usart.o(i.HAL_UART_MspInit) refers to stm32l4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    usart.o(i.HAL_UART_MspInit) refers to stm32l4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    usart.o(i.MX_USART1_UART_Init) refers to stm32l4xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_USART1_UART_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_USART1_UART_Init) refers to usart.o(.bss) for .bss
    stm32l4xx_it.o(i.CAN1_RX0_IRQHandler) refers to stm32l4xx_hal_can.o(i.HAL_CAN_IRQHandler) for HAL_CAN_IRQHandler
    stm32l4xx_it.o(i.CAN1_RX0_IRQHandler) refers to can.o(.bss) for hcan1
    stm32l4xx_it.o(i.EXTI1_IRQHandler) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler) for HAL_GPIO_EXTI_IRQHandler
    stm32l4xx_it.o(i.EXTI2_IRQHandler) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler) for HAL_GPIO_EXTI_IRQHandler
    stm32l4xx_it.o(i.TIM1_UP_TIM16_IRQHandler) refers to porttimer.o(i.prvvTIMERExpiredISR) for prvvTIMERExpiredISR
    stm32l4xx_it.o(i.TIM1_UP_TIM16_IRQHandler) refers to stm32l4xx_hal_tim.o(i.HAL_TIM_IRQHandler) for HAL_TIM_IRQHandler
    stm32l4xx_it.o(i.TIM1_UP_TIM16_IRQHandler) refers to tim.o(.bss) for htim16
    stm32l4xx_it.o(i.TIM6_DAC_IRQHandler) refers to stm32l4xx_hal_tim.o(i.HAL_TIM_IRQHandler) for HAL_TIM_IRQHandler
    stm32l4xx_it.o(i.TIM6_DAC_IRQHandler) refers to stm32l4xx_hal_timebase_tim.o(.bss) for htim6
    stm32l4xx_it.o(i.USART1_IRQHandler) refers to portserial.o(i.prvvUARTTxReadyISR) for prvvUARTTxReadyISR
    stm32l4xx_it.o(i.USART1_IRQHandler) refers to portserial.o(i.prvvUARTRxISR) for prvvUARTRxISR
    stm32l4xx_it.o(i.USART1_IRQHandler) refers to stm32l4xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32l4xx_it.o(i.USART1_IRQHandler) refers to stm32l4xx_hal_cortex.o(i.HAL_NVIC_ClearPendingIRQ) for HAL_NVIC_ClearPendingIRQ
    stm32l4xx_it.o(i.USART1_IRQHandler) refers to usart.o(.bss) for huart1
    stm32l4xx_it.o(i.USART1_IRQHandler) refers to portserial.o(.data) for ucRxByte
    stm32l4xx_hal_msp.o(i.HAL_MspInit) refers to stm32l4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32l4xx_hal_timebase_tim.o(i.HAL_InitTick) refers to stm32l4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32l4xx_hal_timebase_tim.o(i.HAL_InitTick) refers to stm32l4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    stm32l4xx_hal_timebase_tim.o(i.HAL_InitTick) refers to stm32l4xx_hal_rcc.o(i.HAL_RCC_GetClockConfig) for HAL_RCC_GetClockConfig
    stm32l4xx_hal_timebase_tim.o(i.HAL_InitTick) refers to stm32l4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32l4xx_hal_timebase_tim.o(i.HAL_InitTick) refers to stm32l4xx_hal_tim.o(i.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    stm32l4xx_hal_timebase_tim.o(i.HAL_InitTick) refers to stm32l4xx_hal_tim.o(i.HAL_TIM_Base_Start_IT) for HAL_TIM_Base_Start_IT
    stm32l4xx_hal_timebase_tim.o(i.HAL_InitTick) refers to stm32l4xx_hal_timebase_tim.o(.bss) for .bss
    stm32l4xx_hal_timebase_tim.o(i.HAL_ResumeTick) refers to stm32l4xx_hal_timebase_tim.o(.bss) for .bss
    stm32l4xx_hal_timebase_tim.o(i.HAL_SuspendTick) refers to stm32l4xx_hal_timebase_tim.o(.bss) for .bss
    stm32l4xx_hal_can.o(i.HAL_CAN_DeInit) refers to stm32l4xx_hal_can.o(i.HAL_CAN_Stop) for HAL_CAN_Stop
    stm32l4xx_hal_can.o(i.HAL_CAN_DeInit) refers to can.o(i.HAL_CAN_MspDeInit) for HAL_CAN_MspDeInit
    stm32l4xx_hal_can.o(i.HAL_CAN_IRQHandler) refers to stm32l4xx_hal_can.o(i.HAL_CAN_TxMailbox0CompleteCallback) for HAL_CAN_TxMailbox0CompleteCallback
    stm32l4xx_hal_can.o(i.HAL_CAN_IRQHandler) refers to stm32l4xx_hal_can.o(i.HAL_CAN_TxMailbox0AbortCallback) for HAL_CAN_TxMailbox0AbortCallback
    stm32l4xx_hal_can.o(i.HAL_CAN_IRQHandler) refers to stm32l4xx_hal_can.o(i.HAL_CAN_TxMailbox1CompleteCallback) for HAL_CAN_TxMailbox1CompleteCallback
    stm32l4xx_hal_can.o(i.HAL_CAN_IRQHandler) refers to stm32l4xx_hal_can.o(i.HAL_CAN_TxMailbox1AbortCallback) for HAL_CAN_TxMailbox1AbortCallback
    stm32l4xx_hal_can.o(i.HAL_CAN_IRQHandler) refers to stm32l4xx_hal_can.o(i.HAL_CAN_TxMailbox2CompleteCallback) for HAL_CAN_TxMailbox2CompleteCallback
    stm32l4xx_hal_can.o(i.HAL_CAN_IRQHandler) refers to stm32l4xx_hal_can.o(i.HAL_CAN_TxMailbox2AbortCallback) for HAL_CAN_TxMailbox2AbortCallback
    stm32l4xx_hal_can.o(i.HAL_CAN_IRQHandler) refers to stm32l4xx_hal_can.o(i.HAL_CAN_RxFifo0FullCallback) for HAL_CAN_RxFifo0FullCallback
    stm32l4xx_hal_can.o(i.HAL_CAN_IRQHandler) refers to stm32l4xx_hal_can.o(i.HAL_CAN_RxFifo0MsgPendingCallback) for HAL_CAN_RxFifo0MsgPendingCallback
    stm32l4xx_hal_can.o(i.HAL_CAN_IRQHandler) refers to stm32l4xx_hal_can.o(i.HAL_CAN_RxFifo1FullCallback) for HAL_CAN_RxFifo1FullCallback
    stm32l4xx_hal_can.o(i.HAL_CAN_IRQHandler) refers to stm32l4xx_hal_can.o(i.HAL_CAN_RxFifo1MsgPendingCallback) for HAL_CAN_RxFifo1MsgPendingCallback
    stm32l4xx_hal_can.o(i.HAL_CAN_IRQHandler) refers to stm32l4xx_hal_can.o(i.HAL_CAN_SleepCallback) for HAL_CAN_SleepCallback
    stm32l4xx_hal_can.o(i.HAL_CAN_IRQHandler) refers to stm32l4xx_hal_can.o(i.HAL_CAN_WakeUpFromRxMsgCallback) for HAL_CAN_WakeUpFromRxMsgCallback
    stm32l4xx_hal_can.o(i.HAL_CAN_IRQHandler) refers to stm32l4xx_hal_can.o(i.HAL_CAN_ErrorCallback) for HAL_CAN_ErrorCallback
    stm32l4xx_hal_can.o(i.HAL_CAN_Init) refers to can.o(i.HAL_CAN_MspInit) for HAL_CAN_MspInit
    stm32l4xx_hal_can.o(i.HAL_CAN_Init) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_can.o(i.HAL_CAN_Start) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_can.o(i.HAL_CAN_Stop) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal.o(i.HAL_DeInit) refers to stm32l4xx_hal.o(i.HAL_MspDeInit) for HAL_MspDeInit
    stm32l4xx_hal.o(i.HAL_Delay) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal.o(i.HAL_Delay) refers to stm32l4xx_hal.o(.data) for .data
    stm32l4xx_hal.o(i.HAL_GetTick) refers to stm32l4xx_hal.o(.data) for .data
    stm32l4xx_hal.o(i.HAL_GetTickFreq) refers to stm32l4xx_hal.o(.data) for .data
    stm32l4xx_hal.o(i.HAL_GetTickPrio) refers to stm32l4xx_hal.o(.data) for .data
    stm32l4xx_hal.o(i.HAL_IncTick) refers to stm32l4xx_hal.o(.data) for .data
    stm32l4xx_hal.o(i.HAL_Init) refers to stm32l4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping) for HAL_NVIC_SetPriorityGrouping
    stm32l4xx_hal.o(i.HAL_Init) refers to stm32l4xx_hal_timebase_tim.o(i.HAL_InitTick) for HAL_InitTick
    stm32l4xx_hal.o(i.HAL_Init) refers to stm32l4xx_hal_msp.o(i.HAL_MspInit) for HAL_MspInit
    stm32l4xx_hal.o(i.HAL_InitTick) refers to stm32l4xx_hal_cortex.o(i.HAL_SYSTICK_Config) for HAL_SYSTICK_Config
    stm32l4xx_hal.o(i.HAL_InitTick) refers to stm32l4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32l4xx_hal.o(i.HAL_InitTick) refers to stm32l4xx_hal.o(.data) for .data
    stm32l4xx_hal.o(i.HAL_InitTick) refers to system_stm32l4xx.o(.data) for SystemCoreClock
    stm32l4xx_hal.o(i.HAL_SYSCFG_EnableVREFBUF) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal.o(i.HAL_SetTickFreq) refers to stm32l4xx_hal_timebase_tim.o(i.HAL_InitTick) for HAL_InitTick
    stm32l4xx_hal.o(i.HAL_SetTickFreq) refers to stm32l4xx_hal.o(.data) for .data
    stm32l4xx_hal_i2c.o(i.HAL_I2C_DeInit) refers to i2c.o(i.HAL_I2C_MspDeInit) for HAL_I2C_MspDeInit
    stm32l4xx_hal_i2c.o(i.HAL_I2C_DisableListen_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_ER_IRQHandler) refers to stm32l4xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32l4xx_hal_i2c.o(i.HAL_I2C_EnableListen_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_EnableListen_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Slave_ISR_IT) for I2C_Slave_ISR_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Init) refers to i2c.o(i.HAL_I2C_MspInit) for HAL_I2C_MspInit
    stm32l4xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady) refers to stm32l4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Abort_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Abort_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Abort_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32l4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32l4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32l4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32l4xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) for I2C_WaitOnSTOPFlagUntilTimeout
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_DMA) for I2C_Master_ISR_DMA
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_DMAMasterReceiveCplt) for I2C_DMAMasterReceiveCplt
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_IT) for I2C_Master_ISR_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_IT) for I2C_Master_ISR_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_ConvertOtherXferOptions) for I2C_ConvertOtherXferOptions
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_DMA) for I2C_Master_ISR_DMA
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_DMAMasterReceiveCplt) for I2C_DMAMasterReceiveCplt
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_IT) for I2C_Master_ISR_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_ConvertOtherXferOptions) for I2C_ConvertOtherXferOptions
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_IT) for I2C_Master_ISR_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_ConvertOtherXferOptions) for I2C_ConvertOtherXferOptions
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_DMA) for I2C_Master_ISR_DMA
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_DMAMasterTransmitCplt) for I2C_DMAMasterTransmitCplt
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_IT) for I2C_Master_ISR_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_ConvertOtherXferOptions) for I2C_ConvertOtherXferOptions
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_IT) for I2C_Master_ISR_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32l4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32l4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32l4xx_hal_i2c.o(i.I2C_WaitOnTXISFlagUntilTimeout) for I2C_WaitOnTXISFlagUntilTimeout
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32l4xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) for I2C_WaitOnSTOPFlagUntilTimeout
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_DMA) for I2C_Master_ISR_DMA
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_DMAMasterTransmitCplt) for I2C_DMAMasterTransmitCplt
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_IT) for I2C_Master_ISR_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_IT) for I2C_Master_ISR_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32l4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32l4xx_hal_i2c.o(i.I2C_RequestMemoryRead) for I2C_RequestMemoryRead
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32l4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32l4xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) for I2C_WaitOnSTOPFlagUntilTimeout
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Mem_ISR_DMA) for I2C_Mem_ISR_DMA
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_DMAMasterReceiveCplt) for I2C_DMAMasterReceiveCplt
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Mem_ISR_IT) for I2C_Mem_ISR_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32l4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32l4xx_hal_i2c.o(i.I2C_RequestMemoryWrite) for I2C_RequestMemoryWrite
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32l4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32l4xx_hal_i2c.o(i.I2C_WaitOnTXISFlagUntilTimeout) for I2C_WaitOnTXISFlagUntilTimeout
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32l4xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) for I2C_WaitOnSTOPFlagUntilTimeout
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Mem_ISR_DMA) for I2C_Mem_ISR_DMA
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_DMAMasterTransmitCplt) for I2C_DMAMasterTransmitCplt
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Mem_ISR_IT) for I2C_Mem_ISR_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32l4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32l4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32l4xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) for I2C_WaitOnSTOPFlagUntilTimeout
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) for I2C_Slave_ISR_DMA
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_DMASlaveReceiveCplt) for I2C_DMASlaveReceiveCplt
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Slave_ISR_IT) for I2C_Slave_ISR_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) for I2C_Slave_ISR_DMA
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_DMASlaveReceiveCplt) for I2C_DMASlaveReceiveCplt
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_IT) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Slave_ISR_IT) for I2C_Slave_ISR_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) for I2C_Slave_ISR_DMA
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_DMASlaveTransmitCplt) for I2C_DMASlaveTransmitCplt
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_IT) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Slave_ISR_IT) for I2C_Slave_ISR_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32l4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32l4xx_hal_i2c.o(i.I2C_WaitOnTXISFlagUntilTimeout) for I2C_WaitOnTXISFlagUntilTimeout
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32l4xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32l4xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) for I2C_WaitOnSTOPFlagUntilTimeout
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) for I2C_Slave_ISR_DMA
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_DMASlaveTransmitCplt) for I2C_DMASlaveTransmitCplt
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Slave_ISR_IT) for I2C_Slave_ISR_IT
    stm32l4xx_hal_i2c.o(i.I2C_DMAAbort) refers to stm32l4xx_hal_i2c.o(i.I2C_TreatErrorCallback) for I2C_TreatErrorCallback
    stm32l4xx_hal_i2c.o(i.I2C_DMAError) refers to stm32l4xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32l4xx_hal_i2c.o(i.I2C_DMAMasterReceiveCplt) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.I2C_DMAMasterReceiveCplt) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l4xx_hal_i2c.o(i.I2C_DMAMasterReceiveCplt) refers to stm32l4xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32l4xx_hal_i2c.o(i.I2C_DMAMasterTransmitCplt) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.I2C_DMAMasterTransmitCplt) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l4xx_hal_i2c.o(i.I2C_DMAMasterTransmitCplt) refers to stm32l4xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32l4xx_hal_i2c.o(i.I2C_DMASlaveReceiveCplt) refers to stm32l4xx_hal_i2c.o(i.I2C_ITSlaveSeqCplt) for I2C_ITSlaveSeqCplt
    stm32l4xx_hal_i2c.o(i.I2C_DMASlaveTransmitCplt) refers to stm32l4xx_hal_i2c.o(i.I2C_ITSlaveSeqCplt) for I2C_ITSlaveSeqCplt
    stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) refers to stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_DMA) for I2C_Master_ISR_DMA
    stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) refers to stm32l4xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) for I2C_Slave_ISR_DMA
    stm32l4xx_hal_i2c.o(i.I2C_ITAddrCplt) refers to stm32l4xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32l4xx_hal_i2c.o(i.I2C_ITAddrCplt) refers to stm32l4xx_hal_i2c.o(i.HAL_I2C_AddrCallback) for HAL_I2C_AddrCallback
    stm32l4xx_hal_i2c.o(i.I2C_ITError) refers to stm32l4xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32l4xx_hal_i2c.o(i.I2C_ITError) refers to stm32l4xx_hal_i2c.o(i.I2C_TreatErrorCallback) for I2C_TreatErrorCallback
    stm32l4xx_hal_i2c.o(i.I2C_ITError) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_GetState) for HAL_DMA_GetState
    stm32l4xx_hal_i2c.o(i.I2C_ITError) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32l4xx_hal_i2c.o(i.I2C_ITError) refers to stm32l4xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32l4xx_hal_i2c.o(i.I2C_ITError) refers to stm32l4xx_hal_i2c.o(i.I2C_Slave_ISR_IT) for I2C_Slave_ISR_IT
    stm32l4xx_hal_i2c.o(i.I2C_ITListenCplt) refers to stm32l4xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32l4xx_hal_i2c.o(i.I2C_ITListenCplt) refers to stm32l4xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32l4xx_hal_i2c.o(i.I2C_ITMasterCplt) refers to stm32l4xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32l4xx_hal_i2c.o(i.I2C_ITMasterCplt) refers to stm32l4xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32l4xx_hal_i2c.o(i.I2C_ITMasterCplt) refers to stm32l4xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32l4xx_hal_i2c.o(i.I2C_ITMasterCplt) refers to stm32l4xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32l4xx_hal_i2c.o(i.I2C_ITMasterCplt) refers to stm32l4xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback) for HAL_I2C_MasterTxCpltCallback
    stm32l4xx_hal_i2c.o(i.I2C_ITMasterCplt) refers to stm32l4xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32l4xx_hal_i2c.o(i.I2C_ITMasterCplt) refers to stm32l4xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32l4xx_hal_i2c.o(i.I2C_ITMasterSeqCplt) refers to stm32l4xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32l4xx_hal_i2c.o(i.I2C_ITMasterSeqCplt) refers to stm32l4xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32l4xx_hal_i2c.o(i.I2C_ITMasterSeqCplt) refers to stm32l4xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback) for HAL_I2C_MasterTxCpltCallback
    stm32l4xx_hal_i2c.o(i.I2C_ITSlaveCplt) refers to stm32l4xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32l4xx_hal_i2c.o(i.I2C_ITSlaveCplt) refers to stm32l4xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32l4xx_hal_i2c.o(i.I2C_ITSlaveCplt) refers to stm32l4xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32l4xx_hal_i2c.o(i.I2C_ITSlaveCplt) refers to stm32l4xx_hal_i2c.o(i.I2C_ITListenCplt) for I2C_ITListenCplt
    stm32l4xx_hal_i2c.o(i.I2C_ITSlaveCplt) refers to stm32l4xx_hal_i2c.o(i.I2C_ITSlaveSeqCplt) for I2C_ITSlaveSeqCplt
    stm32l4xx_hal_i2c.o(i.I2C_ITSlaveCplt) refers to stm32l4xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32l4xx_hal_i2c.o(i.I2C_ITSlaveCplt) refers to stm32l4xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32l4xx_hal_i2c.o(i.I2C_ITSlaveCplt) refers to stm32l4xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32l4xx_hal_i2c.o(i.I2C_ITSlaveSeqCplt) refers to stm32l4xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32l4xx_hal_i2c.o(i.I2C_ITSlaveSeqCplt) refers to stm32l4xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32l4xx_hal_i2c.o(i.I2C_ITSlaveSeqCplt) refers to stm32l4xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32l4xx_hal_i2c.o(i.I2C_IsErrorOccurred) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_i2c.o(i.I2C_IsErrorOccurred) refers to stm32l4xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_ITMasterSeqCplt) for I2C_ITMasterSeqCplt
    stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_ITMasterCplt) for I2C_ITMasterCplt
    stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_ITMasterSeqCplt) for I2C_ITMasterSeqCplt
    stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_ITMasterCplt) for I2C_ITMasterCplt
    stm32l4xx_hal_i2c.o(i.I2C_Mem_ISR_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.I2C_Mem_ISR_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32l4xx_hal_i2c.o(i.I2C_Mem_ISR_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l4xx_hal_i2c.o(i.I2C_Mem_ISR_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32l4xx_hal_i2c.o(i.I2C_Mem_ISR_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_ITMasterCplt) for I2C_ITMasterCplt
    stm32l4xx_hal_i2c.o(i.I2C_Mem_ISR_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32l4xx_hal_i2c.o(i.I2C_Mem_ISR_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32l4xx_hal_i2c.o(i.I2C_Mem_ISR_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l4xx_hal_i2c.o(i.I2C_Mem_ISR_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_ITMasterCplt) for I2C_ITMasterCplt
    stm32l4xx_hal_i2c.o(i.I2C_RequestMemoryRead) refers to stm32l4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l4xx_hal_i2c.o(i.I2C_RequestMemoryRead) refers to stm32l4xx_hal_i2c.o(i.I2C_WaitOnTXISFlagUntilTimeout) for I2C_WaitOnTXISFlagUntilTimeout
    stm32l4xx_hal_i2c.o(i.I2C_RequestMemoryRead) refers to stm32l4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32l4xx_hal_i2c.o(i.I2C_RequestMemoryWrite) refers to stm32l4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l4xx_hal_i2c.o(i.I2C_RequestMemoryWrite) refers to stm32l4xx_hal_i2c.o(i.I2C_WaitOnTXISFlagUntilTimeout) for I2C_WaitOnTXISFlagUntilTimeout
    stm32l4xx_hal_i2c.o(i.I2C_RequestMemoryWrite) refers to stm32l4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32l4xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_ITSlaveCplt) for I2C_ITSlaveCplt
    stm32l4xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_ITListenCplt) for I2C_ITListenCplt
    stm32l4xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32l4xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_ITSlaveSeqCplt) for I2C_ITSlaveSeqCplt
    stm32l4xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32l4xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_ITAddrCplt) for I2C_ITAddrCplt
    stm32l4xx_hal_i2c.o(i.I2C_Slave_ISR_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_ITSlaveCplt) for I2C_ITSlaveCplt
    stm32l4xx_hal_i2c.o(i.I2C_Slave_ISR_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32l4xx_hal_i2c.o(i.I2C_Slave_ISR_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_ITListenCplt) for I2C_ITListenCplt
    stm32l4xx_hal_i2c.o(i.I2C_Slave_ISR_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32l4xx_hal_i2c.o(i.I2C_Slave_ISR_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_ITSlaveSeqCplt) for I2C_ITSlaveSeqCplt
    stm32l4xx_hal_i2c.o(i.I2C_Slave_ISR_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_ITAddrCplt) for I2C_ITAddrCplt
    stm32l4xx_hal_i2c.o(i.I2C_TreatErrorCallback) refers to stm32l4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32l4xx_hal_i2c.o(i.I2C_TreatErrorCallback) refers to stm32l4xx_hal_i2c.o(i.HAL_I2C_AbortCpltCallback) for HAL_I2C_AbortCpltCallback
    stm32l4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) refers to stm32l4xx_hal_i2c.o(i.I2C_IsErrorOccurred) for I2C_IsErrorOccurred
    stm32l4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) refers to stm32l4xx_hal_i2c.o(i.I2C_IsErrorOccurred) for I2C_IsErrorOccurred
    stm32l4xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_i2c.o(i.I2C_WaitOnTXISFlagUntilTimeout) refers to stm32l4xx_hal_i2c.o(i.I2C_IsErrorOccurred) for I2C_IsErrorOccurred
    stm32l4xx_hal_i2c.o(i.I2C_WaitOnTXISFlagUntilTimeout) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32l4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32l4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32l4xx_hal_timebase_tim.o(i.HAL_InitTick) for HAL_InitTick
    stm32l4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32l4xx.o(.constdata) for AHBPrescTable
    stm32l4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32l4xx.o(.data) for SystemCoreClock
    stm32l4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32l4xx_hal.o(.data) for uwTickPrio
    stm32l4xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to stm32l4xx_hal_timebase_tim.o(i.HAL_InitTick) for HAL_InitTick
    stm32l4xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to system_stm32l4xx.o(.data) for SystemCoreClock
    stm32l4xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to stm32l4xx_hal.o(.data) for uwTickPrio
    stm32l4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) refers to system_stm32l4xx.o(.data) for SystemCoreClock
    stm32l4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to stm32l4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) for HAL_RCC_GetHCLKFreq
    stm32l4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to system_stm32l4xx.o(.constdata) for APBPrescTable
    stm32l4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to stm32l4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) for HAL_RCC_GetHCLKFreq
    stm32l4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to system_stm32l4xx.o(.constdata) for APBPrescTable
    stm32l4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) refers to system_stm32l4xx.o(.constdata) for MSIRangeTable
    stm32l4xx_hal_rcc.o(i.HAL_RCC_MCOConfig) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32l4xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler) refers to stm32l4xx_hal_rcc.o(i.HAL_RCC_CSSCallback) for HAL_RCC_CSSCallback
    stm32l4xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32l4xx_hal_rcc.o(i.RCC_SetFlashLatencyFromMSIRange) for RCC_SetFlashLatencyFromMSIRange
    stm32l4xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32l4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32l4xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32l4xx_hal_timebase_tim.o(i.HAL_InitTick) for HAL_InitTick
    stm32l4xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to system_stm32l4xx.o(.constdata) for AHBPrescTable
    stm32l4xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to system_stm32l4xx.o(.data) for SystemCoreClock
    stm32l4xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32l4xx_hal.o(.data) for uwTickPrio
    stm32l4xx_hal_rcc.o(i.RCC_SetFlashLatencyFromMSIRange) refers to stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_GetVoltageRange) for HAL_PWREx_GetVoltageRange
    stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRSWaitSynchronization) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_IRQHandler) refers to stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_SyncOkCallback) for HAL_RCCEx_CRS_SyncOkCallback
    stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_IRQHandler) refers to stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_SyncWarnCallback) for HAL_RCCEx_CRS_SyncWarnCallback
    stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_IRQHandler) refers to stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_ExpectedSyncCallback) for HAL_RCCEx_CRS_ExpectedSyncCallback
    stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_IRQHandler) refers to stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_ErrorCallback) for HAL_RCCEx_CRS_ErrorCallback
    stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisableLSCO) refers to stm32l4xx_hal_pwr.o(i.HAL_PWR_EnableBkUpAccess) for HAL_PWR_EnableBkUpAccess
    stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisableLSCO) refers to stm32l4xx_hal_pwr.o(i.HAL_PWR_DisableBkUpAccess) for HAL_PWR_DisableBkUpAccess
    stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisablePLLSAI1) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisablePLLSAI2) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnableLSCO) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnableLSCO) refers to stm32l4xx_hal_pwr.o(i.HAL_PWR_EnableBkUpAccess) for HAL_PWR_EnableBkUpAccess
    stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnableLSCO) refers to stm32l4xx_hal_pwr.o(i.HAL_PWR_DisableBkUpAccess) for HAL_PWR_DisableBkUpAccess
    stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnablePLLSAI1) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnablePLLSAI2) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32l4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32l4xx_hal_rcc_ex.o(i.RCCEx_GetSAIxPeriphCLKFreq) for RCCEx_GetSAIxPeriphCLKFreq
    stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32l4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32l4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to system_stm32l4xx.o(.constdata) for MSIRangeTable
    stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_LSECSS_IRQHandler) refers to stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_LSECSS_Callback) for HAL_RCCEx_LSECSS_Callback
    stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) refers to stm32l4xx_hal_rcc_ex.o(i.RCCEx_PLLSAI1_Config) for RCCEx_PLLSAI1_Config
    stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) refers to stm32l4xx_hal_rcc_ex.o(i.RCCEx_PLLSAI2_Config) for RCCEx_PLLSAI2_Config
    stm32l4xx_hal_rcc_ex.o(i.RCCEx_PLLSAI1_Config) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_rcc_ex.o(i.RCCEx_PLLSAI2_Config) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32l4xx_hal_flash.o(.data) for .data
    stm32l4xx_hal_flash.o(i.HAL_FLASH_GetError) refers to stm32l4xx_hal_flash.o(.data) for .data
    stm32l4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32l4xx_hal_flash_ex.o(i.FLASH_FlushCaches) for FLASH_FlushCaches
    stm32l4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32l4xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback) for HAL_FLASH_OperationErrorCallback
    stm32l4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32l4xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback) for HAL_FLASH_EndOfOperationCallback
    stm32l4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32l4xx_hal_flash_ex.o(i.FLASH_PageErase) for FLASH_PageErase
    stm32l4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32l4xx_hal_flash.o(.data) for .data
    stm32l4xx_hal_flash.o(i.HAL_FLASH_OB_Launch) refers to stm32l4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32l4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32l4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32l4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32l4xx_hal_flash.o(i.FLASH_Program_DoubleWord) for FLASH_Program_DoubleWord
    stm32l4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32l4xx_hal_flash.o(i.FLASH_Program_Fast) for FLASH_Program_Fast
    stm32l4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32l4xx_hal_flash_ex.o(i.FLASH_FlushCaches) for FLASH_FlushCaches
    stm32l4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32l4xx_hal_flash.o(.data) for .data
    stm32l4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32l4xx_hal_flash.o(i.FLASH_Program_DoubleWord) for FLASH_Program_DoubleWord
    stm32l4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32l4xx_hal_flash.o(i.FLASH_Program_Fast) for FLASH_Program_Fast
    stm32l4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32l4xx_hal_flash.o(.data) for .data
    stm32l4xx_hal_flash_ex.o(i.FLASH_FlushCaches) refers to stm32l4xx_hal_flash.o(.data) for pFlash
    stm32l4xx_hal_flash_ex.o(i.FLASH_OB_PCROPConfig) refers to stm32l4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32l4xx_hal_flash_ex.o(i.FLASH_OB_RDPConfig) refers to stm32l4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32l4xx_hal_flash_ex.o(i.FLASH_OB_UserConfig) refers to stm32l4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32l4xx_hal_flash_ex.o(i.FLASH_OB_WRPConfig) refers to stm32l4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32l4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32l4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32l4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32l4xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32l4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32l4xx_hal_flash_ex.o(i.FLASH_PageErase) for FLASH_PageErase
    stm32l4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32l4xx_hal_flash_ex.o(i.FLASH_FlushCaches) for FLASH_FlushCaches
    stm32l4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32l4xx_hal_flash.o(.data) for pFlash
    stm32l4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32l4xx_hal_flash_ex.o(i.FLASH_PageErase) for FLASH_PageErase
    stm32l4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32l4xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32l4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32l4xx_hal_flash.o(.data) for pFlash
    stm32l4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig) refers to stm32l4xx_hal_flash_ex.o(i.FLASH_OB_GetWRP) for FLASH_OB_GetWRP
    stm32l4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig) refers to stm32l4xx_hal_flash_ex.o(i.FLASH_OB_GetRDP) for FLASH_OB_GetRDP
    stm32l4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig) refers to stm32l4xx_hal_flash_ex.o(i.FLASH_OB_GetUser) for FLASH_OB_GetUser
    stm32l4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig) refers to stm32l4xx_hal_flash_ex.o(i.FLASH_OB_GetPCROP) for FLASH_OB_GetPCROP
    stm32l4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32l4xx_hal_flash_ex.o(i.FLASH_OB_WRPConfig) for FLASH_OB_WRPConfig
    stm32l4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32l4xx_hal_flash_ex.o(i.FLASH_OB_RDPConfig) for FLASH_OB_RDPConfig
    stm32l4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32l4xx_hal_flash_ex.o(i.FLASH_OB_UserConfig) for FLASH_OB_UserConfig
    stm32l4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32l4xx_hal_flash_ex.o(i.FLASH_OB_PCROPConfig) for FLASH_OB_PCROPConfig
    stm32l4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32l4xx_hal_flash.o(.data) for pFlash
    stm32l4xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback) for HAL_GPIO_EXTI_Callback
    stm32l4xx_hal_dma.o(i.HAL_DMA_PollForTransfer) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_dma.o(i.HAL_DMA_Start) refers to stm32l4xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT) refers to stm32l4xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32l4xx_hal_pwr.o(i.HAL_PWR_EnterSLEEPMode) refers to stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableLowPowerRunMode) for HAL_PWREx_EnableLowPowerRunMode
    stm32l4xx_hal_pwr.o(i.HAL_PWR_EnterSLEEPMode) refers to stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableLowPowerRunMode) for HAL_PWREx_DisableLowPowerRunMode
    stm32l4xx_hal_pwr.o(i.HAL_PWR_EnterSTOPMode) refers to stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_EnterSTOP0Mode) for HAL_PWREx_EnterSTOP0Mode
    stm32l4xx_hal_pwr.o(i.HAL_PWR_EnterSTOPMode) refers to stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_EnterSTOP1Mode) for HAL_PWREx_EnterSTOP1Mode
    stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling) refers to system_stm32l4xx.o(.data) for SystemCoreClock
    stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableLowPowerRunMode) refers to system_stm32l4xx.o(.data) for SystemCoreClock
    stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableSRAM2ContentRetention) refers to stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_SetSRAM2ContentRetention) for HAL_PWREx_SetSRAM2ContentRetention
    stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableSRAM2ContentRetention) refers to stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_SetSRAM2ContentRetention) for HAL_PWREx_SetSRAM2ContentRetention
    stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_PVD_PVM_IRQHandler) refers to stm32l4xx_hal_pwr.o(i.HAL_PWR_PVDCallback) for HAL_PWR_PVDCallback
    stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_PVD_PVM_IRQHandler) refers to stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_PVM1Callback) for HAL_PWREx_PVM1Callback
    stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_PVD_PVM_IRQHandler) refers to stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_PVM2Callback) for HAL_PWREx_PVM2Callback
    stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_PVD_PVM_IRQHandler) refers to stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_PVM3Callback) for HAL_PWREx_PVM3Callback
    stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_PVD_PVM_IRQHandler) refers to stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_PVM4Callback) for HAL_PWREx_PVM4Callback
    stm32l4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) refers to stm32l4xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32l4xx_hal_cortex.o(i.HAL_SYSTICK_Config) refers to stm32l4xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32l4xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler) refers to stm32l4xx_hal_cortex.o(i.HAL_SYSTICK_Callback) for HAL_SYSTICK_Callback
    stm32l4xx_hal_iwdg.o(i.HAL_IWDG_Init) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_spi.o(i.HAL_SPI_Abort) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32l4xx_hal_spi.o(i.HAL_SPI_Abort) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_spi.o(i.HAL_SPI_Abort) refers to stm32l4xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32l4xx_hal_spi.o(i.HAL_SPI_Abort) refers to stm32l4xx_hal_spi.o(i.SPI_WaitFifoStateUntilTimeout) for SPI_WaitFifoStateUntilTimeout
    stm32l4xx_hal_spi.o(i.HAL_SPI_Abort) refers to stm32l4xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout) for SPI_WaitFlagStateUntilTimeout
    stm32l4xx_hal_spi.o(i.HAL_SPI_Abort) refers to system_stm32l4xx.o(.data) for SystemCoreClock
    stm32l4xx_hal_spi.o(i.HAL_SPI_Abort) refers to stm32l4xx_hal_spi.o(i.SPI_AbortTx_ISR) for SPI_AbortTx_ISR
    stm32l4xx_hal_spi.o(i.HAL_SPI_Abort) refers to stm32l4xx_hal_spi.o(i.SPI_AbortRx_ISR) for SPI_AbortRx_ISR
    stm32l4xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32l4xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_AbortCpltCallback) for HAL_SPI_AbortCpltCallback
    stm32l4xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to system_stm32l4xx.o(.data) for SystemCoreClock
    stm32l4xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to stm32l4xx_hal_spi.o(i.SPI_AbortTx_ISR) for SPI_AbortTx_ISR
    stm32l4xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to stm32l4xx_hal_spi.o(i.SPI_AbortRx_ISR) for SPI_AbortRx_ISR
    stm32l4xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to stm32l4xx_hal_spi.o(i.SPI_DMATxAbortCallback) for SPI_DMATxAbortCallback
    stm32l4xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to stm32l4xx_hal_spi.o(i.SPI_DMARxAbortCallback) for SPI_DMARxAbortCallback
    stm32l4xx_hal_spi.o(i.HAL_SPI_DMAStop) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32l4xx_hal_spi.o(i.HAL_SPI_DeInit) refers to spi.o(i.HAL_SPI_MspDeInit) for HAL_SPI_MspDeInit
    stm32l4xx_hal_spi.o(i.HAL_SPI_IRQHandler) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32l4xx_hal_spi.o(i.HAL_SPI_IRQHandler) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32l4xx_hal_spi.o(i.HAL_SPI_IRQHandler) refers to stm32l4xx_hal_spi.o(i.SPI_DMAAbortOnError) for SPI_DMAAbortOnError
    stm32l4xx_hal_spi.o(i.HAL_SPI_Init) refers to spi.o(i.HAL_SPI_MspInit) for HAL_SPI_MspInit
    stm32l4xx_hal_spi.o(i.HAL_SPI_Receive) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_spi.o(i.HAL_SPI_Receive) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_TransmitReceive) for HAL_SPI_TransmitReceive
    stm32l4xx_hal_spi.o(i.HAL_SPI_Receive) refers to stm32l4xx_hal_spi.o(i.SPI_EndRxTransaction) for SPI_EndRxTransaction
    stm32l4xx_hal_spi.o(i.HAL_SPI_Receive_DMA) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) for HAL_SPI_TransmitReceive_DMA
    stm32l4xx_hal_spi.o(i.HAL_SPI_Receive_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l4xx_hal_spi.o(i.HAL_SPI_Receive_DMA) refers to stm32l4xx_hal_spi.o(i.SPI_DMAHalfReceiveCplt) for SPI_DMAHalfReceiveCplt
    stm32l4xx_hal_spi.o(i.HAL_SPI_Receive_DMA) refers to stm32l4xx_hal_spi.o(i.SPI_DMAReceiveCplt) for SPI_DMAReceiveCplt
    stm32l4xx_hal_spi.o(i.HAL_SPI_Receive_DMA) refers to stm32l4xx_hal_spi.o(i.SPI_DMAError) for SPI_DMAError
    stm32l4xx_hal_spi.o(i.HAL_SPI_Receive_IT) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT) for HAL_SPI_TransmitReceive_IT
    stm32l4xx_hal_spi.o(i.HAL_SPI_Receive_IT) refers to stm32l4xx_hal_spi.o(i.SPI_RxISR_16BIT) for SPI_RxISR_16BIT
    stm32l4xx_hal_spi.o(i.HAL_SPI_Receive_IT) refers to stm32l4xx_hal_spi.o(i.SPI_RxISR_8BIT) for SPI_RxISR_8BIT
    stm32l4xx_hal_spi.o(i.HAL_SPI_Transmit) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_spi.o(i.HAL_SPI_Transmit) refers to stm32l4xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32l4xx_hal_spi.o(i.HAL_SPI_TransmitReceive) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_spi.o(i.HAL_SPI_TransmitReceive) refers to stm32l4xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32l4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32l4xx_hal_spi.o(i.SPI_DMAHalfTransmitReceiveCplt) for SPI_DMAHalfTransmitReceiveCplt
    stm32l4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32l4xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt) for SPI_DMATransmitReceiveCplt
    stm32l4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32l4xx_hal_spi.o(i.SPI_DMAError) for SPI_DMAError
    stm32l4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32l4xx_hal_spi.o(i.SPI_DMAHalfReceiveCplt) for SPI_DMAHalfReceiveCplt
    stm32l4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32l4xx_hal_spi.o(i.SPI_DMAReceiveCplt) for SPI_DMAReceiveCplt
    stm32l4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT) refers to stm32l4xx_hal_spi.o(i.SPI_2linesRxISR_16BIT) for SPI_2linesRxISR_16BIT
    stm32l4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT) refers to stm32l4xx_hal_spi.o(i.SPI_2linesTxISR_16BIT) for SPI_2linesTxISR_16BIT
    stm32l4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT) refers to stm32l4xx_hal_spi.o(i.SPI_2linesRxISR_8BIT) for SPI_2linesRxISR_8BIT
    stm32l4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT) refers to stm32l4xx_hal_spi.o(i.SPI_2linesTxISR_8BIT) for SPI_2linesTxISR_8BIT
    stm32l4xx_hal_spi.o(i.HAL_SPI_Transmit_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l4xx_hal_spi.o(i.HAL_SPI_Transmit_DMA) refers to stm32l4xx_hal_spi.o(i.SPI_DMAHalfTransmitCplt) for SPI_DMAHalfTransmitCplt
    stm32l4xx_hal_spi.o(i.HAL_SPI_Transmit_DMA) refers to stm32l4xx_hal_spi.o(i.SPI_DMATransmitCplt) for SPI_DMATransmitCplt
    stm32l4xx_hal_spi.o(i.HAL_SPI_Transmit_DMA) refers to stm32l4xx_hal_spi.o(i.SPI_DMAError) for SPI_DMAError
    stm32l4xx_hal_spi.o(i.HAL_SPI_Transmit_IT) refers to stm32l4xx_hal_spi.o(i.SPI_TxISR_16BIT) for SPI_TxISR_16BIT
    stm32l4xx_hal_spi.o(i.HAL_SPI_Transmit_IT) refers to stm32l4xx_hal_spi.o(i.SPI_TxISR_8BIT) for SPI_TxISR_8BIT
    stm32l4xx_hal_spi.o(i.SPI_2linesRxISR_16BIT) refers to stm32l4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) for SPI_CloseRxTx_ISR
    stm32l4xx_hal_spi.o(i.SPI_2linesRxISR_8BIT) refers to stm32l4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) for SPI_CloseRxTx_ISR
    stm32l4xx_hal_spi.o(i.SPI_2linesTxISR_16BIT) refers to stm32l4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) for SPI_CloseRxTx_ISR
    stm32l4xx_hal_spi.o(i.SPI_2linesTxISR_8BIT) refers to stm32l4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) for SPI_CloseRxTx_ISR
    stm32l4xx_hal_spi.o(i.SPI_AbortRx_ISR) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_spi.o(i.SPI_AbortRx_ISR) refers to stm32l4xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout) for SPI_WaitFlagStateUntilTimeout
    stm32l4xx_hal_spi.o(i.SPI_AbortRx_ISR) refers to stm32l4xx_hal_spi.o(i.SPI_WaitFifoStateUntilTimeout) for SPI_WaitFifoStateUntilTimeout
    stm32l4xx_hal_spi.o(i.SPI_AbortRx_ISR) refers to system_stm32l4xx.o(.data) for SystemCoreClock
    stm32l4xx_hal_spi.o(i.SPI_AbortTx_ISR) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_spi.o(i.SPI_AbortTx_ISR) refers to stm32l4xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32l4xx_hal_spi.o(i.SPI_AbortTx_ISR) refers to stm32l4xx_hal_spi.o(i.SPI_WaitFifoStateUntilTimeout) for SPI_WaitFifoStateUntilTimeout
    stm32l4xx_hal_spi.o(i.SPI_AbortTx_ISR) refers to stm32l4xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout) for SPI_WaitFlagStateUntilTimeout
    stm32l4xx_hal_spi.o(i.SPI_AbortTx_ISR) refers to system_stm32l4xx.o(.data) for SystemCoreClock
    stm32l4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) refers to stm32l4xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32l4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32l4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_TxRxCpltCallback) for HAL_SPI_TxRxCpltCallback
    stm32l4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_RxCpltCallback) for HAL_SPI_RxCpltCallback
    stm32l4xx_hal_spi.o(i.SPI_CloseRx_ISR) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_spi.o(i.SPI_CloseRx_ISR) refers to stm32l4xx_hal_spi.o(i.SPI_EndRxTransaction) for SPI_EndRxTransaction
    stm32l4xx_hal_spi.o(i.SPI_CloseRx_ISR) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32l4xx_hal_spi.o(i.SPI_CloseRx_ISR) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_RxCpltCallback) for HAL_SPI_RxCpltCallback
    stm32l4xx_hal_spi.o(i.SPI_CloseTx_ISR) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_spi.o(i.SPI_CloseTx_ISR) refers to stm32l4xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32l4xx_hal_spi.o(i.SPI_CloseTx_ISR) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32l4xx_hal_spi.o(i.SPI_CloseTx_ISR) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_TxCpltCallback) for HAL_SPI_TxCpltCallback
    stm32l4xx_hal_spi.o(i.SPI_DMAAbortOnError) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32l4xx_hal_spi.o(i.SPI_DMAError) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32l4xx_hal_spi.o(i.SPI_DMAHalfReceiveCplt) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_RxHalfCpltCallback) for HAL_SPI_RxHalfCpltCallback
    stm32l4xx_hal_spi.o(i.SPI_DMAHalfTransmitCplt) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_TxHalfCpltCallback) for HAL_SPI_TxHalfCpltCallback
    stm32l4xx_hal_spi.o(i.SPI_DMAHalfTransmitReceiveCplt) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_TxRxHalfCpltCallback) for HAL_SPI_TxRxHalfCpltCallback
    stm32l4xx_hal_spi.o(i.SPI_DMAReceiveCplt) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_spi.o(i.SPI_DMAReceiveCplt) refers to stm32l4xx_hal_spi.o(i.SPI_EndRxTransaction) for SPI_EndRxTransaction
    stm32l4xx_hal_spi.o(i.SPI_DMAReceiveCplt) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32l4xx_hal_spi.o(i.SPI_DMAReceiveCplt) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_RxCpltCallback) for HAL_SPI_RxCpltCallback
    stm32l4xx_hal_spi.o(i.SPI_DMARxAbortCallback) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_spi.o(i.SPI_DMARxAbortCallback) refers to stm32l4xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout) for SPI_WaitFlagStateUntilTimeout
    stm32l4xx_hal_spi.o(i.SPI_DMARxAbortCallback) refers to stm32l4xx_hal_spi.o(i.SPI_WaitFifoStateUntilTimeout) for SPI_WaitFifoStateUntilTimeout
    stm32l4xx_hal_spi.o(i.SPI_DMARxAbortCallback) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_AbortCpltCallback) for HAL_SPI_AbortCpltCallback
    stm32l4xx_hal_spi.o(i.SPI_DMATransmitCplt) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_spi.o(i.SPI_DMATransmitCplt) refers to stm32l4xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32l4xx_hal_spi.o(i.SPI_DMATransmitCplt) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32l4xx_hal_spi.o(i.SPI_DMATransmitCplt) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_TxCpltCallback) for HAL_SPI_TxCpltCallback
    stm32l4xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt) refers to stm32l4xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32l4xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32l4xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_TxRxCpltCallback) for HAL_SPI_TxRxCpltCallback
    stm32l4xx_hal_spi.o(i.SPI_DMATxAbortCallback) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_spi.o(i.SPI_DMATxAbortCallback) refers to stm32l4xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32l4xx_hal_spi.o(i.SPI_DMATxAbortCallback) refers to stm32l4xx_hal_spi.o(i.SPI_WaitFifoStateUntilTimeout) for SPI_WaitFifoStateUntilTimeout
    stm32l4xx_hal_spi.o(i.SPI_DMATxAbortCallback) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_AbortCpltCallback) for HAL_SPI_AbortCpltCallback
    stm32l4xx_hal_spi.o(i.SPI_EndRxTransaction) refers to stm32l4xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout) for SPI_WaitFlagStateUntilTimeout
    stm32l4xx_hal_spi.o(i.SPI_EndRxTransaction) refers to stm32l4xx_hal_spi.o(i.SPI_WaitFifoStateUntilTimeout) for SPI_WaitFifoStateUntilTimeout
    stm32l4xx_hal_spi.o(i.SPI_EndRxTxTransaction) refers to stm32l4xx_hal_spi.o(i.SPI_WaitFifoStateUntilTimeout) for SPI_WaitFifoStateUntilTimeout
    stm32l4xx_hal_spi.o(i.SPI_EndRxTxTransaction) refers to stm32l4xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout) for SPI_WaitFlagStateUntilTimeout
    stm32l4xx_hal_spi.o(i.SPI_RxISR_16BIT) refers to stm32l4xx_hal_spi.o(i.SPI_CloseRx_ISR) for SPI_CloseRx_ISR
    stm32l4xx_hal_spi.o(i.SPI_RxISR_8BIT) refers to stm32l4xx_hal_spi.o(i.SPI_CloseRx_ISR) for SPI_CloseRx_ISR
    stm32l4xx_hal_spi.o(i.SPI_TxISR_16BIT) refers to stm32l4xx_hal_spi.o(i.SPI_CloseTx_ISR) for SPI_CloseTx_ISR
    stm32l4xx_hal_spi.o(i.SPI_TxISR_8BIT) refers to stm32l4xx_hal_spi.o(i.SPI_CloseTx_ISR) for SPI_CloseTx_ISR
    stm32l4xx_hal_spi.o(i.SPI_WaitFifoStateUntilTimeout) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_spi.o(i.SPI_WaitFifoStateUntilTimeout) refers to system_stm32l4xx.o(.data) for SystemCoreClock
    stm32l4xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout) refers to system_stm32l4xx.o(.data) for SystemCoreClock
    stm32l4xx_hal_tim.o(i.HAL_TIM_Base_DeInit) refers to tim.o(i.HAL_TIM_Base_MspDeInit) for HAL_TIM_Base_MspDeInit
    stm32l4xx_hal_tim.o(i.HAL_TIM_Base_Init) refers to tim.o(i.HAL_TIM_Base_MspInit) for HAL_TIM_Base_MspInit
    stm32l4xx_hal_tim.o(i.HAL_TIM_Base_Init) refers to stm32l4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32l4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32l4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32l4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32l4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32l4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32l4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32l4xx_hal_tim.o(i.HAL_TIM_Base_Stop_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32l4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32l4xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32l4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32l4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage) for TIM_TI1_ConfigInputStage
    stm32l4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32l4xx_hal_tim.o(i.TIM_ITRx_SetConfig) for TIM_ITRx_SetConfig
    stm32l4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32l4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage) for TIM_TI2_ConfigInputStage
    stm32l4xx_hal_tim.o(i.HAL_TIM_ConfigOCrefClear) refers to stm32l4xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32l4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32l4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32l4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32l4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32l4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32l4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32l4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32l4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32l4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32l4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32l4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32l4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32l4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32l4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32l4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32l4xx_hal_tim.o(i.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32l4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32l4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32l4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32l4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32l4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32l4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32l4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32l4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32l4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32l4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32l4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32l4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32l4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32l4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32l4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32l4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32l4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32l4xx_hal_tim.o(i.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32l4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32l4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32l4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart) refers to stm32l4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) for HAL_TIM_DMABurst_MultiReadStart
    stm32l4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStop) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32l4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart) refers to stm32l4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) for HAL_TIM_DMABurst_MultiWriteStart
    stm32l4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStop) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32l4xx_hal_tim.o(i.HAL_TIM_Encoder_DeInit) refers to stm32l4xx_hal_tim.o(i.HAL_TIM_Encoder_MspDeInit) for HAL_TIM_Encoder_MspDeInit
    stm32l4xx_hal_tim.o(i.HAL_TIM_Encoder_Init) refers to stm32l4xx_hal_tim.o(i.HAL_TIM_Encoder_MspInit) for HAL_TIM_Encoder_MspInit
    stm32l4xx_hal_tim.o(i.HAL_TIM_Encoder_Init) refers to stm32l4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32l4xx_hal_tim.o(i.HAL_TIM_Encoder_Start) refers to stm32l4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32l4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32l4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32l4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32l4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32l4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32l4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32l4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_IT) refers to stm32l4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop) refers to stm32l4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA) refers to stm32l4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32l4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_IT) refers to stm32l4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32l4xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32l4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32l4xx_hal_tim.o(i.TIM_TI2_SetConfig) for TIM_TI2_SetConfig
    stm32l4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32l4xx_hal_tim.o(i.TIM_TI3_SetConfig) for TIM_TI3_SetConfig
    stm32l4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32l4xx_hal_tim.o(i.TIM_TI4_SetConfig) for TIM_TI4_SetConfig
    stm32l4xx_hal_tim.o(i.HAL_TIM_IC_DeInit) refers to stm32l4xx_hal_tim.o(i.HAL_TIM_IC_MspDeInit) for HAL_TIM_IC_MspDeInit
    stm32l4xx_hal_tim.o(i.HAL_TIM_IC_Init) refers to stm32l4xx_hal_tim.o(i.HAL_TIM_IC_MspInit) for HAL_TIM_IC_MspInit
    stm32l4xx_hal_tim.o(i.HAL_TIM_IC_Init) refers to stm32l4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32l4xx_hal_tim.o(i.HAL_TIM_IC_Start) refers to stm32l4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32l4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32l4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32l4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32l4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32l4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32l4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32l4xx_hal_tim.o(i.HAL_TIM_IC_Start_IT) refers to stm32l4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l4xx_hal_tim.o(i.HAL_TIM_IC_Stop) refers to stm32l4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l4xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA) refers to stm32l4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l4xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32l4xx_hal_tim.o(i.HAL_TIM_IC_Stop_IT) refers to stm32l4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32l4xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32l4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32l4xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback) for HAL_TIM_OC_DelayElapsedCallback
    stm32l4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32l4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to main.o(i.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32l4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback) for HAL_TIMEx_BreakCallback
    stm32l4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_Break2Callback) for HAL_TIMEx_Break2Callback
    stm32l4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32l4xx_hal_tim.o(i.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32l4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32l4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32l4xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32l4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32l4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32l4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32l4xx_hal_tim.o(i.TIM_OC3_SetConfig) for TIM_OC3_SetConfig
    stm32l4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32l4xx_hal_tim.o(i.TIM_OC4_SetConfig) for TIM_OC4_SetConfig
    stm32l4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32l4xx_hal_tim.o(i.TIM_OC5_SetConfig) for TIM_OC5_SetConfig
    stm32l4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32l4xx_hal_tim.o(i.TIM_OC6_SetConfig) for TIM_OC6_SetConfig
    stm32l4xx_hal_tim.o(i.HAL_TIM_OC_DeInit) refers to stm32l4xx_hal_tim.o(i.HAL_TIM_OC_MspDeInit) for HAL_TIM_OC_MspDeInit
    stm32l4xx_hal_tim.o(i.HAL_TIM_OC_Init) refers to stm32l4xx_hal_tim.o(i.HAL_TIM_OC_MspInit) for HAL_TIM_OC_MspInit
    stm32l4xx_hal_tim.o(i.HAL_TIM_OC_Init) refers to stm32l4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32l4xx_hal_tim.o(i.HAL_TIM_OC_Start) refers to stm32l4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32l4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32l4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32l4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32l4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32l4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32l4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32l4xx_hal_tim.o(i.HAL_TIM_OC_Start_IT) refers to stm32l4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l4xx_hal_tim.o(i.HAL_TIM_OC_Stop) refers to stm32l4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l4xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32l4xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA) refers to stm32l4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l4xx_hal_tim.o(i.HAL_TIM_OC_Stop_IT) refers to stm32l4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32l4xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32l4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32l4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32l4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32l4xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32l4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32l4xx_hal_tim.o(i.TIM_TI2_SetConfig) for TIM_TI2_SetConfig
    stm32l4xx_hal_tim.o(i.HAL_TIM_OnePulse_DeInit) refers to stm32l4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspDeInit) for HAL_TIM_OnePulse_MspDeInit
    stm32l4xx_hal_tim.o(i.HAL_TIM_OnePulse_Init) refers to stm32l4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspInit) for HAL_TIM_OnePulse_MspInit
    stm32l4xx_hal_tim.o(i.HAL_TIM_OnePulse_Init) refers to stm32l4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32l4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start) refers to stm32l4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start_IT) refers to stm32l4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop) refers to stm32l4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop_IT) refers to stm32l4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32l4xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32l4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32l4xx_hal_tim.o(i.TIM_OC3_SetConfig) for TIM_OC3_SetConfig
    stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32l4xx_hal_tim.o(i.TIM_OC4_SetConfig) for TIM_OC4_SetConfig
    stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32l4xx_hal_tim.o(i.TIM_OC5_SetConfig) for TIM_OC5_SetConfig
    stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32l4xx_hal_tim.o(i.TIM_OC6_SetConfig) for TIM_OC6_SetConfig
    stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_DeInit) refers to stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_MspDeInit) for HAL_TIM_PWM_MspDeInit
    stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_Init) refers to stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_MspInit) for HAL_TIM_PWM_MspInit
    stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_Init) refers to stm32l4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_Start) refers to stm32l4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32l4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32l4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32l4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32l4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_Start_IT) refers to stm32l4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_Stop) refers to stm32l4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA) refers to stm32l4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_IT) refers to stm32l4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro) refers to stm32l4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32l4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro_IT) refers to stm32l4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32l4xx_hal_tim.o(i.TIM_DMACaptureCplt) refers to stm32l4xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32l4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) refers to stm32l4xx_hal_tim.o(i.HAL_TIM_IC_CaptureHalfCpltCallback) for HAL_TIM_IC_CaptureHalfCpltCallback
    stm32l4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) refers to stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32l4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) refers to stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedHalfCpltCallback) for HAL_TIM_PWM_PulseFinishedHalfCpltCallback
    stm32l4xx_hal_tim.o(i.TIM_DMAError) refers to stm32l4xx_hal_tim.o(i.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32l4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) refers to main.o(i.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32l4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) refers to stm32l4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedHalfCpltCallback) for HAL_TIM_PeriodElapsedHalfCpltCallback
    stm32l4xx_hal_tim.o(i.TIM_DMATriggerCplt) refers to stm32l4xx_hal_tim.o(i.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32l4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) refers to stm32l4xx_hal_tim.o(i.HAL_TIM_TriggerHalfCpltCallback) for HAL_TIM_TriggerHalfCpltCallback
    stm32l4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32l4xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32l4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32l4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage) for TIM_TI1_ConfigInputStage
    stm32l4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32l4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage) for TIM_TI2_ConfigInputStage
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32l4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32l4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32l4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_DeInit) refers to stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspDeInit) for HAL_TIMEx_HallSensor_MspDeInit
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspInit) for HAL_TIMEx_HallSensor_MspInit
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32l4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32l4xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32l4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start) refers to stm32l4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32l4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32l4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32l4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32l4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_IT) refers to stm32l4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop) refers to stm32l4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32l4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_IT) refers to stm32l4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start) refers to stm32l4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32l4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32l4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) for TIM_DMADelayPulseNCplt
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32l4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32l4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) for TIM_DMAErrorCCxN
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_IT) refers to stm32l4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop) refers to stm32l4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA) refers to stm32l4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_IT) refers to stm32l4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start) refers to stm32l4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start) refers to stm32l4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32l4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32l4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop) refers to stm32l4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop) refers to stm32l4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32l4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32l4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start) refers to stm32l4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32l4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32l4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) for TIM_DMADelayPulseNCplt
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32l4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32l4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) for TIM_DMAErrorCCxN
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_IT) refers to stm32l4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop) refers to stm32l4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32l4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_IT) refers to stm32l4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32l4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) refers to stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32l4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) refers to stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutHalfCpltCallback) for HAL_TIMEx_CommutHalfCpltCallback
    stm32l4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) refers to stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32l4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) refers to stm32l4xx_hal_tim.o(i.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32l4xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32l4xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to stm32l4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32l4xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to stm32l4xx_hal_uart.o(i.UART_AdvFeatureConfig) for UART_AdvFeatureConfig
    stm32l4xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to stm32l4xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32l4xx_hal_uart.o(i.HAL_LIN_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32l4xx_hal_uart.o(i.HAL_LIN_Init) refers to stm32l4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32l4xx_hal_uart.o(i.HAL_LIN_Init) refers to stm32l4xx_hal_uart.o(i.UART_AdvFeatureConfig) for UART_AdvFeatureConfig
    stm32l4xx_hal_uart.o(i.HAL_LIN_Init) refers to stm32l4xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32l4xx_hal_uart.o(i.HAL_MultiProcessor_DisableMuteMode) refers to stm32l4xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32l4xx_hal_uart.o(i.HAL_MultiProcessor_EnableMuteMode) refers to stm32l4xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32l4xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32l4xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to stm32l4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32l4xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to stm32l4xx_hal_uart.o(i.UART_AdvFeatureConfig) for UART_AdvFeatureConfig
    stm32l4xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to stm32l4xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32l4xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32l4xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32l4xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32l4xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32l4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32l4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32l4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32l4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32l4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) for UART_DMARxOnlyAbortCallback
    stm32l4xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32l4xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32l4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32l4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32l4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32l4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32l4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) for UART_DMATxOnlyAbortCallback
    stm32l4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32l4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32l4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32l4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32l4xx_hal_uart.o(i.UART_DMATxAbortCallback) for UART_DMATxAbortCallback
    stm32l4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32l4xx_hal_uart.o(i.UART_DMARxAbortCallback) for UART_DMARxAbortCallback
    stm32l4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32l4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32l4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32l4xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32l4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32l4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32l4xx_hal_uart.o(i.HAL_UART_DeInit) refers to usart.o(i.HAL_UART_MspDeInit) for HAL_UART_MspDeInit
    stm32l4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32l4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32l4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32l4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to portserial.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32l4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32l4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32l4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32l4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32l4xx_hal_uart_ex.o(i.HAL_UARTEx_WakeupCallback) for HAL_UARTEx_WakeupCallback
    stm32l4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32l4xx_hal_uart.o(i.UART_EndTransmit_IT) for UART_EndTransmit_IT
    stm32l4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32l4xx_hal_uart.o(i.UART_DMAAbortOnError) for UART_DMAAbortOnError
    stm32l4xx_hal_uart.o(i.HAL_UART_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32l4xx_hal_uart.o(i.HAL_UART_Init) refers to stm32l4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32l4xx_hal_uart.o(i.HAL_UART_Init) refers to stm32l4xx_hal_uart.o(i.UART_AdvFeatureConfig) for UART_AdvFeatureConfig
    stm32l4xx_hal_uart.o(i.HAL_UART_Init) refers to stm32l4xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32l4xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32l4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32l4xx_hal_uart.o(i.HAL_UART_Receive_DMA) refers to stm32l4xx_hal_uart.o(i.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32l4xx_hal_uart.o(i.HAL_UART_Receive_IT) refers to stm32l4xx_hal_uart.o(i.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32l4xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32l4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32l4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32l4xx_hal_uart.o(i.UART_DMATransmitCplt) for UART_DMATransmitCplt
    stm32l4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32l4xx_hal_uart.o(i.UART_DMATxHalfCplt) for UART_DMATxHalfCplt
    stm32l4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32l4xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32l4xx_hal_uart.o(i.HAL_UART_Transmit_IT) refers to stm32l4xx_hal_uart.o(i.UART_TxISR_8BIT) for UART_TxISR_8BIT
    stm32l4xx_hal_uart.o(i.HAL_UART_Transmit_IT) refers to stm32l4xx_hal_uart.o(i.UART_TxISR_16BIT) for UART_TxISR_16BIT
    stm32l4xx_hal_uart.o(i.UART_CheckIdleState) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_uart.o(i.UART_CheckIdleState) refers to stm32l4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32l4xx_hal_uart.o(i.UART_DMAAbortOnError) refers to portserial.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32l4xx_hal_uart.o(i.UART_DMAError) refers to stm32l4xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32l4xx_hal_uart.o(i.UART_DMAError) refers to stm32l4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32l4xx_hal_uart.o(i.UART_DMAError) refers to portserial.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32l4xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to portserial.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32l4xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to stm32l4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32l4xx_hal_uart.o(i.UART_DMARxAbortCallback) refers to stm32l4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32l4xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to stm32l4xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback) for HAL_UART_RxHalfCpltCallback
    stm32l4xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to stm32l4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32l4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) refers to stm32l4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32l4xx_hal_uart.o(i.UART_DMATransmitCplt) refers to portserial.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32l4xx_hal_uart.o(i.UART_DMATxAbortCallback) refers to stm32l4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32l4xx_hal_uart.o(i.UART_DMATxHalfCplt) refers to stm32l4xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback) for HAL_UART_TxHalfCpltCallback
    stm32l4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) refers to stm32l4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32l4xx_hal_uart.o(i.UART_EndTransmit_IT) refers to portserial.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32l4xx_hal_uart.o(i.UART_RxISR_16BIT) refers to portserial.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32l4xx_hal_uart.o(i.UART_RxISR_16BIT) refers to stm32l4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32l4xx_hal_uart.o(i.UART_RxISR_8BIT) refers to portserial.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32l4xx_hal_uart.o(i.UART_RxISR_8BIT) refers to stm32l4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32l4xx_hal_uart.o(i.UART_SetConfig) refers to stm32l4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32l4xx_hal_uart.o(i.UART_SetConfig) refers to stm32l4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32l4xx_hal_uart.o(i.UART_SetConfig) refers to lludivv7m.o(.text) for __aeabi_uldivmod
    stm32l4xx_hal_uart.o(i.UART_SetConfig) refers to stm32l4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32l4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32l4xx_hal_uart.o(i.UART_DMAReceiveCplt) for UART_DMAReceiveCplt
    stm32l4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32l4xx_hal_uart.o(i.UART_DMARxHalfCplt) for UART_DMARxHalfCplt
    stm32l4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32l4xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32l4xx_hal_uart.o(i.UART_Start_Receive_IT) refers to stm32l4xx_hal_uart.o(i.UART_RxISR_8BIT) for UART_RxISR_8BIT
    stm32l4xx_hal_uart.o(i.UART_Start_Receive_IT) refers to stm32l4xx_hal_uart.o(i.UART_RxISR_16BIT) for UART_RxISR_16BIT
    stm32l4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_uart_ex.o(i.HAL_MultiProcessorEx_AddressLength_Set) refers to stm32l4xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32l4xx_hal_uart_ex.o(i.HAL_RS485Ex_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32l4xx_hal_uart_ex.o(i.HAL_RS485Ex_Init) refers to stm32l4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32l4xx_hal_uart_ex.o(i.HAL_RS485Ex_Init) refers to stm32l4xx_hal_uart.o(i.UART_AdvFeatureConfig) for UART_AdvFeatureConfig
    stm32l4xx_hal_uart_ex.o(i.HAL_RS485Ex_Init) refers to stm32l4xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32l4xx_hal_uart_ex.o(i.HAL_UARTEx_ReceiveToIdle) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_uart_ex.o(i.HAL_UARTEx_ReceiveToIdle_DMA) refers to stm32l4xx_hal_uart.o(i.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32l4xx_hal_uart_ex.o(i.HAL_UARTEx_ReceiveToIdle_IT) refers to stm32l4xx_hal_uart.o(i.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32l4xx_hal_uart_ex.o(i.HAL_UARTEx_StopModeWakeUpSourceConfig) refers to stm32l4xx_hal_uart_ex.o(i.UARTEx_Wakeup_AddressConfig) for UARTEx_Wakeup_AddressConfig
    stm32l4xx_hal_uart_ex.o(i.HAL_UARTEx_StopModeWakeUpSourceConfig) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_uart_ex.o(i.HAL_UARTEx_StopModeWakeUpSourceConfig) refers to stm32l4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    system_stm32l4xx.o(i.SystemCoreClockUpdate) refers to system_stm32l4xx.o(.constdata) for .constdata
    system_stm32l4xx.o(i.SystemCoreClockUpdate) refers to system_stm32l4xx.o(.data) for .data
    event_groups.o(i.vEventGroupClearBitsCallback) refers to event_groups.o(i.xEventGroupClearBits) for xEventGroupClearBits
    event_groups.o(i.vEventGroupDelete) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    event_groups.o(i.vEventGroupDelete) refers to tasks.o(i.vTaskRemoveFromUnorderedEventList) for vTaskRemoveFromUnorderedEventList
    event_groups.o(i.vEventGroupDelete) refers to heap_4.o(i.vPortFree) for vPortFree
    event_groups.o(i.vEventGroupDelete) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    event_groups.o(i.vEventGroupSetBitsCallback) refers to event_groups.o(i.xEventGroupSetBits) for xEventGroupSetBits
    event_groups.o(i.xEventGroupClearBits) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    event_groups.o(i.xEventGroupClearBits) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    event_groups.o(i.xEventGroupClearBitsFromISR) refers to timers.o(i.xTimerPendFunctionCallFromISR) for xTimerPendFunctionCallFromISR
    event_groups.o(i.xEventGroupClearBitsFromISR) refers to event_groups.o(i.vEventGroupClearBitsCallback) for vEventGroupClearBitsCallback
    event_groups.o(i.xEventGroupCreate) refers to heap_4.o(i.pvPortMalloc) for pvPortMalloc
    event_groups.o(i.xEventGroupCreate) refers to list.o(i.vListInitialise) for vListInitialise
    event_groups.o(i.xEventGroupCreateStatic) refers to list.o(i.vListInitialise) for vListInitialise
    event_groups.o(i.xEventGroupSetBits) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    event_groups.o(i.xEventGroupSetBits) refers to tasks.o(i.vTaskRemoveFromUnorderedEventList) for vTaskRemoveFromUnorderedEventList
    event_groups.o(i.xEventGroupSetBits) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    event_groups.o(i.xEventGroupSetBitsFromISR) refers to timers.o(i.xTimerPendFunctionCallFromISR) for xTimerPendFunctionCallFromISR
    event_groups.o(i.xEventGroupSetBitsFromISR) refers to event_groups.o(i.vEventGroupSetBitsCallback) for vEventGroupSetBitsCallback
    event_groups.o(i.xEventGroupSync) refers to tasks.o(i.xTaskGetSchedulerState) for xTaskGetSchedulerState
    event_groups.o(i.xEventGroupSync) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    event_groups.o(i.xEventGroupSync) refers to event_groups.o(i.xEventGroupSetBits) for xEventGroupSetBits
    event_groups.o(i.xEventGroupSync) refers to tasks.o(i.vTaskPlaceOnUnorderedEventList) for vTaskPlaceOnUnorderedEventList
    event_groups.o(i.xEventGroupSync) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    event_groups.o(i.xEventGroupSync) refers to tasks.o(i.uxTaskResetEventItemValue) for uxTaskResetEventItemValue
    event_groups.o(i.xEventGroupSync) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    event_groups.o(i.xEventGroupSync) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    event_groups.o(i.xEventGroupWaitBits) refers to tasks.o(i.xTaskGetSchedulerState) for xTaskGetSchedulerState
    event_groups.o(i.xEventGroupWaitBits) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    event_groups.o(i.xEventGroupWaitBits) refers to event_groups.o(i.prvTestWaitCondition) for prvTestWaitCondition
    event_groups.o(i.xEventGroupWaitBits) refers to tasks.o(i.vTaskPlaceOnUnorderedEventList) for vTaskPlaceOnUnorderedEventList
    event_groups.o(i.xEventGroupWaitBits) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    event_groups.o(i.xEventGroupWaitBits) refers to tasks.o(i.uxTaskResetEventItemValue) for uxTaskResetEventItemValue
    event_groups.o(i.xEventGroupWaitBits) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    event_groups.o(i.xEventGroupWaitBits) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.pcQueueGetName) refers to queue.o(.bss) for .bss
    queue.o(i.prvCopyDataFromQueue) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    queue.o(i.prvCopyDataToQueue) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    queue.o(i.prvCopyDataToQueue) refers to tasks.o(i.xTaskPriorityDisinherit) for xTaskPriorityDisinherit
    queue.o(i.prvInitialiseMutex) refers to queue.o(i.xQueueGenericSend) for xQueueGenericSend
    queue.o(i.prvInitialiseNewQueue) refers to queue.o(i.xQueueGenericReset) for xQueueGenericReset
    queue.o(i.prvIsQueueEmpty) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.prvIsQueueEmpty) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.prvIsQueueFull) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.prvIsQueueFull) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.prvUnlockQueue) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.prvUnlockQueue) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.prvUnlockQueue) refers to tasks.o(i.vTaskMissedYield) for vTaskMissedYield
    queue.o(i.prvUnlockQueue) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.uxQueueMessagesWaiting) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.uxQueueMessagesWaiting) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.uxQueueSpacesAvailable) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.uxQueueSpacesAvailable) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.vQueueAddToRegistry) refers to queue.o(.bss) for .bss
    queue.o(i.vQueueDelete) refers to queue.o(i.vQueueUnregisterQueue) for vQueueUnregisterQueue
    queue.o(i.vQueueDelete) refers to heap_4.o(i.vPortFree) for vPortFree
    queue.o(i.vQueueUnregisterQueue) refers to queue.o(.bss) for .bss
    queue.o(i.vQueueWaitForMessageRestricted) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.vQueueWaitForMessageRestricted) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.vQueueWaitForMessageRestricted) refers to tasks.o(i.vTaskPlaceOnEventListRestricted) for vTaskPlaceOnEventListRestricted
    queue.o(i.vQueueWaitForMessageRestricted) refers to queue.o(i.prvUnlockQueue) for prvUnlockQueue
    queue.o(i.xQueueCreateCountingSemaphore) refers to queue.o(i.xQueueGenericCreate) for xQueueGenericCreate
    queue.o(i.xQueueCreateCountingSemaphoreStatic) refers to queue.o(i.xQueueGenericCreateStatic) for xQueueGenericCreateStatic
    queue.o(i.xQueueCreateMutex) refers to queue.o(i.xQueueGenericCreate) for xQueueGenericCreate
    queue.o(i.xQueueCreateMutex) refers to queue.o(i.prvInitialiseMutex) for prvInitialiseMutex
    queue.o(i.xQueueCreateMutexStatic) refers to queue.o(i.xQueueGenericCreateStatic) for xQueueGenericCreateStatic
    queue.o(i.xQueueCreateMutexStatic) refers to queue.o(i.prvInitialiseMutex) for prvInitialiseMutex
    queue.o(i.xQueueGenericCreate) refers to heap_4.o(i.pvPortMalloc) for pvPortMalloc
    queue.o(i.xQueueGenericCreate) refers to queue.o(i.prvInitialiseNewQueue) for prvInitialiseNewQueue
    queue.o(i.xQueueGenericCreateStatic) refers to queue.o(i.prvInitialiseNewQueue) for prvInitialiseNewQueue
    queue.o(i.xQueueGenericReset) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.xQueueGenericReset) refers to list.o(i.vListInitialise) for vListInitialise
    queue.o(i.xQueueGenericReset) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.xQueueGenericReset) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.xQueueGenericSend) refers to tasks.o(i.xTaskGetSchedulerState) for xTaskGetSchedulerState
    queue.o(i.xQueueGenericSend) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.xQueueGenericSend) refers to queue.o(i.prvCopyDataToQueue) for prvCopyDataToQueue
    queue.o(i.xQueueGenericSend) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.xQueueGenericSend) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.xQueueGenericSend) refers to tasks.o(i.vTaskInternalSetTimeOutState) for vTaskInternalSetTimeOutState
    queue.o(i.xQueueGenericSend) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    queue.o(i.xQueueGenericSend) refers to tasks.o(i.xTaskCheckForTimeOut) for xTaskCheckForTimeOut
    queue.o(i.xQueueGenericSend) refers to queue.o(i.prvUnlockQueue) for prvUnlockQueue
    queue.o(i.xQueueGenericSend) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    queue.o(i.xQueueGenericSend) refers to queue.o(i.prvIsQueueFull) for prvIsQueueFull
    queue.o(i.xQueueGenericSend) refers to tasks.o(i.vTaskPlaceOnEventList) for vTaskPlaceOnEventList
    queue.o(i.xQueueGenericSendFromISR) refers to port.o(i.vPortValidateInterruptPriority) for vPortValidateInterruptPriority
    queue.o(i.xQueueGenericSendFromISR) refers to queue.o(i.prvCopyDataToQueue) for prvCopyDataToQueue
    queue.o(i.xQueueGenericSendFromISR) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.xQueueGetMutexHolder) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.xQueueGetMutexHolder) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.xQueueGiveFromISR) refers to port.o(i.vPortValidateInterruptPriority) for vPortValidateInterruptPriority
    queue.o(i.xQueueGiveFromISR) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.xQueueGiveMutexRecursive) refers to tasks.o(i.xTaskGetCurrentTaskHandle) for xTaskGetCurrentTaskHandle
    queue.o(i.xQueueGiveMutexRecursive) refers to queue.o(i.xQueueGenericSend) for xQueueGenericSend
    queue.o(i.xQueuePeek) refers to tasks.o(i.xTaskGetSchedulerState) for xTaskGetSchedulerState
    queue.o(i.xQueuePeek) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.xQueuePeek) refers to queue.o(i.prvCopyDataFromQueue) for prvCopyDataFromQueue
    queue.o(i.xQueuePeek) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.xQueuePeek) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.xQueuePeek) refers to tasks.o(i.vTaskInternalSetTimeOutState) for vTaskInternalSetTimeOutState
    queue.o(i.xQueuePeek) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    queue.o(i.xQueuePeek) refers to tasks.o(i.xTaskCheckForTimeOut) for xTaskCheckForTimeOut
    queue.o(i.xQueuePeek) refers to queue.o(i.prvUnlockQueue) for prvUnlockQueue
    queue.o(i.xQueuePeek) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    queue.o(i.xQueuePeek) refers to queue.o(i.prvIsQueueEmpty) for prvIsQueueEmpty
    queue.o(i.xQueuePeek) refers to tasks.o(i.vTaskPlaceOnEventList) for vTaskPlaceOnEventList
    queue.o(i.xQueuePeekFromISR) refers to port.o(i.vPortValidateInterruptPriority) for vPortValidateInterruptPriority
    queue.o(i.xQueuePeekFromISR) refers to queue.o(i.prvCopyDataFromQueue) for prvCopyDataFromQueue
    queue.o(i.xQueueReceive) refers to tasks.o(i.xTaskGetSchedulerState) for xTaskGetSchedulerState
    queue.o(i.xQueueReceive) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.xQueueReceive) refers to queue.o(i.prvCopyDataFromQueue) for prvCopyDataFromQueue
    queue.o(i.xQueueReceive) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.xQueueReceive) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.xQueueReceive) refers to tasks.o(i.vTaskInternalSetTimeOutState) for vTaskInternalSetTimeOutState
    queue.o(i.xQueueReceive) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    queue.o(i.xQueueReceive) refers to tasks.o(i.xTaskCheckForTimeOut) for xTaskCheckForTimeOut
    queue.o(i.xQueueReceive) refers to queue.o(i.prvUnlockQueue) for prvUnlockQueue
    queue.o(i.xQueueReceive) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    queue.o(i.xQueueReceive) refers to queue.o(i.prvIsQueueEmpty) for prvIsQueueEmpty
    queue.o(i.xQueueReceive) refers to tasks.o(i.vTaskPlaceOnEventList) for vTaskPlaceOnEventList
    queue.o(i.xQueueReceiveFromISR) refers to port.o(i.vPortValidateInterruptPriority) for vPortValidateInterruptPriority
    queue.o(i.xQueueReceiveFromISR) refers to queue.o(i.prvCopyDataFromQueue) for prvCopyDataFromQueue
    queue.o(i.xQueueReceiveFromISR) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.xQueueSemaphoreTake) refers to tasks.o(i.xTaskGetSchedulerState) for xTaskGetSchedulerState
    queue.o(i.xQueueSemaphoreTake) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.xQueueSemaphoreTake) refers to tasks.o(i.pvTaskIncrementMutexHeldCount) for pvTaskIncrementMutexHeldCount
    queue.o(i.xQueueSemaphoreTake) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.xQueueSemaphoreTake) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.xQueueSemaphoreTake) refers to tasks.o(i.vTaskInternalSetTimeOutState) for vTaskInternalSetTimeOutState
    queue.o(i.xQueueSemaphoreTake) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    queue.o(i.xQueueSemaphoreTake) refers to tasks.o(i.xTaskCheckForTimeOut) for xTaskCheckForTimeOut
    queue.o(i.xQueueSemaphoreTake) refers to queue.o(i.prvUnlockQueue) for prvUnlockQueue
    queue.o(i.xQueueSemaphoreTake) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    queue.o(i.xQueueSemaphoreTake) refers to queue.o(i.prvIsQueueEmpty) for prvIsQueueEmpty
    queue.o(i.xQueueSemaphoreTake) refers to queue.o(i.prvGetDisinheritPriorityAfterTimeout) for prvGetDisinheritPriorityAfterTimeout
    queue.o(i.xQueueSemaphoreTake) refers to tasks.o(i.vTaskPriorityDisinheritAfterTimeout) for vTaskPriorityDisinheritAfterTimeout
    queue.o(i.xQueueSemaphoreTake) refers to tasks.o(i.xTaskPriorityInherit) for xTaskPriorityInherit
    queue.o(i.xQueueSemaphoreTake) refers to tasks.o(i.vTaskPlaceOnEventList) for vTaskPlaceOnEventList
    queue.o(i.xQueueTakeMutexRecursive) refers to tasks.o(i.xTaskGetCurrentTaskHandle) for xTaskGetCurrentTaskHandle
    queue.o(i.xQueueTakeMutexRecursive) refers to queue.o(i.xQueueSemaphoreTake) for xQueueSemaphoreTake
    stream_buffer.o(i.prvInitialiseNewStreamBuffer) refers to memset.o(.text) for memset
    stream_buffer.o(i.prvInitialiseNewStreamBuffer) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    stream_buffer.o(i.prvReadBytesFromBuffer) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    stream_buffer.o(i.prvReadMessageFromBuffer) refers to stream_buffer.o(i.prvReadBytesFromBuffer) for prvReadBytesFromBuffer
    stream_buffer.o(i.prvWriteBytesToBuffer) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    stream_buffer.o(i.prvWriteMessageToBuffer) refers to stream_buffer.o(i.prvWriteBytesToBuffer) for prvWriteBytesToBuffer
    stream_buffer.o(i.vStreamBufferDelete) refers to heap_4.o(i.vPortFree) for vPortFree
    stream_buffer.o(i.vStreamBufferDelete) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    stream_buffer.o(i.xStreamBufferBytesAvailable) refers to stream_buffer.o(i.prvBytesInBuffer) for prvBytesInBuffer
    stream_buffer.o(i.xStreamBufferGenericCreate) refers to heap_4.o(i.pvPortMalloc) for pvPortMalloc
    stream_buffer.o(i.xStreamBufferGenericCreate) refers to stream_buffer.o(i.prvInitialiseNewStreamBuffer) for prvInitialiseNewStreamBuffer
    stream_buffer.o(i.xStreamBufferGenericCreateStatic) refers to stream_buffer.o(i.prvInitialiseNewStreamBuffer) for prvInitialiseNewStreamBuffer
    stream_buffer.o(i.xStreamBufferIsFull) refers to stream_buffer.o(i.xStreamBufferSpacesAvailable) for xStreamBufferSpacesAvailable
    stream_buffer.o(i.xStreamBufferNextMessageLengthBytes) refers to stream_buffer.o(i.prvBytesInBuffer) for prvBytesInBuffer
    stream_buffer.o(i.xStreamBufferNextMessageLengthBytes) refers to stream_buffer.o(i.prvReadBytesFromBuffer) for prvReadBytesFromBuffer
    stream_buffer.o(i.xStreamBufferReceive) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    stream_buffer.o(i.xStreamBufferReceive) refers to stream_buffer.o(i.prvBytesInBuffer) for prvBytesInBuffer
    stream_buffer.o(i.xStreamBufferReceive) refers to tasks.o(i.xTaskNotifyStateClear) for xTaskNotifyStateClear
    stream_buffer.o(i.xStreamBufferReceive) refers to tasks.o(i.xTaskGetCurrentTaskHandle) for xTaskGetCurrentTaskHandle
    stream_buffer.o(i.xStreamBufferReceive) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    stream_buffer.o(i.xStreamBufferReceive) refers to tasks.o(i.xTaskNotifyWait) for xTaskNotifyWait
    stream_buffer.o(i.xStreamBufferReceive) refers to stream_buffer.o(i.prvReadMessageFromBuffer) for prvReadMessageFromBuffer
    stream_buffer.o(i.xStreamBufferReceive) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    stream_buffer.o(i.xStreamBufferReceive) refers to tasks.o(i.xTaskGenericNotify) for xTaskGenericNotify
    stream_buffer.o(i.xStreamBufferReceive) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    stream_buffer.o(i.xStreamBufferReceiveCompletedFromISR) refers to tasks.o(i.xTaskGenericNotifyFromISR) for xTaskGenericNotifyFromISR
    stream_buffer.o(i.xStreamBufferReceiveFromISR) refers to stream_buffer.o(i.prvBytesInBuffer) for prvBytesInBuffer
    stream_buffer.o(i.xStreamBufferReceiveFromISR) refers to stream_buffer.o(i.prvReadMessageFromBuffer) for prvReadMessageFromBuffer
    stream_buffer.o(i.xStreamBufferReceiveFromISR) refers to tasks.o(i.xTaskGenericNotifyFromISR) for xTaskGenericNotifyFromISR
    stream_buffer.o(i.xStreamBufferReset) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    stream_buffer.o(i.xStreamBufferReset) refers to stream_buffer.o(i.prvInitialiseNewStreamBuffer) for prvInitialiseNewStreamBuffer
    stream_buffer.o(i.xStreamBufferReset) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    stream_buffer.o(i.xStreamBufferSend) refers to tasks.o(i.vTaskSetTimeOutState) for vTaskSetTimeOutState
    stream_buffer.o(i.xStreamBufferSend) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    stream_buffer.o(i.xStreamBufferSend) refers to stream_buffer.o(i.xStreamBufferSpacesAvailable) for xStreamBufferSpacesAvailable
    stream_buffer.o(i.xStreamBufferSend) refers to tasks.o(i.xTaskNotifyStateClear) for xTaskNotifyStateClear
    stream_buffer.o(i.xStreamBufferSend) refers to tasks.o(i.xTaskGetCurrentTaskHandle) for xTaskGetCurrentTaskHandle
    stream_buffer.o(i.xStreamBufferSend) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    stream_buffer.o(i.xStreamBufferSend) refers to tasks.o(i.xTaskNotifyWait) for xTaskNotifyWait
    stream_buffer.o(i.xStreamBufferSend) refers to tasks.o(i.xTaskCheckForTimeOut) for xTaskCheckForTimeOut
    stream_buffer.o(i.xStreamBufferSend) refers to stream_buffer.o(i.prvWriteMessageToBuffer) for prvWriteMessageToBuffer
    stream_buffer.o(i.xStreamBufferSend) refers to stream_buffer.o(i.prvBytesInBuffer) for prvBytesInBuffer
    stream_buffer.o(i.xStreamBufferSend) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    stream_buffer.o(i.xStreamBufferSend) refers to tasks.o(i.xTaskGenericNotify) for xTaskGenericNotify
    stream_buffer.o(i.xStreamBufferSend) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    stream_buffer.o(i.xStreamBufferSendCompletedFromISR) refers to tasks.o(i.xTaskGenericNotifyFromISR) for xTaskGenericNotifyFromISR
    stream_buffer.o(i.xStreamBufferSendFromISR) refers to stream_buffer.o(i.xStreamBufferSpacesAvailable) for xStreamBufferSpacesAvailable
    stream_buffer.o(i.xStreamBufferSendFromISR) refers to stream_buffer.o(i.prvWriteMessageToBuffer) for prvWriteMessageToBuffer
    stream_buffer.o(i.xStreamBufferSendFromISR) refers to stream_buffer.o(i.prvBytesInBuffer) for prvBytesInBuffer
    stream_buffer.o(i.xStreamBufferSendFromISR) refers to tasks.o(i.xTaskGenericNotifyFromISR) for xTaskGenericNotifyFromISR
    tasks.o(i.eTaskGetState) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.eTaskGetState) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.eTaskGetState) refers to tasks.o(.data) for .data
    tasks.o(i.eTaskGetState) refers to tasks.o(.bss) for .bss
    tasks.o(i.pcTaskGetName) refers to tasks.o(.data) for .data
    tasks.o(i.prvAddCurrentTaskToDelayedList) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.prvAddCurrentTaskToDelayedList) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.prvAddCurrentTaskToDelayedList) refers to list.o(i.vListInsert) for vListInsert
    tasks.o(i.prvAddCurrentTaskToDelayedList) refers to tasks.o(.data) for .data
    tasks.o(i.prvAddCurrentTaskToDelayedList) refers to tasks.o(.bss) for .bss
    tasks.o(i.prvAddNewTaskToReadyList) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.prvAddNewTaskToReadyList) refers to tasks.o(i.prvInitialiseTaskLists) for prvInitialiseTaskLists
    tasks.o(i.prvAddNewTaskToReadyList) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.prvAddNewTaskToReadyList) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.prvAddNewTaskToReadyList) refers to tasks.o(.data) for .data
    tasks.o(i.prvAddNewTaskToReadyList) refers to tasks.o(.bss) for .bss
    tasks.o(i.prvCheckTasksWaitingTermination) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.prvCheckTasksWaitingTermination) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.prvCheckTasksWaitingTermination) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.prvCheckTasksWaitingTermination) refers to tasks.o(i.prvDeleteTCB) for prvDeleteTCB
    tasks.o(i.prvCheckTasksWaitingTermination) refers to tasks.o(.data) for .data
    tasks.o(i.prvCheckTasksWaitingTermination) refers to tasks.o(.bss) for .bss
    tasks.o(i.prvDeleteTCB) refers to heap_4.o(i.vPortFree) for vPortFree
    tasks.o(i.prvIdleTask) refers to tasks.o(i.prvCheckTasksWaitingTermination) for prvCheckTasksWaitingTermination
    tasks.o(i.prvIdleTask) refers to tasks.o(.bss) for .bss
    tasks.o(i.prvInitialiseNewTask) refers to aeabi_memset.o(.text) for __aeabi_memset
    tasks.o(i.prvInitialiseNewTask) refers to list.o(i.vListInitialiseItem) for vListInitialiseItem
    tasks.o(i.prvInitialiseNewTask) refers to port.o(i.pxPortInitialiseStack) for pxPortInitialiseStack
    tasks.o(i.prvInitialiseTaskLists) refers to list.o(i.vListInitialise) for vListInitialise
    tasks.o(i.prvInitialiseTaskLists) refers to tasks.o(.bss) for .bss
    tasks.o(i.prvInitialiseTaskLists) refers to tasks.o(.data) for .data
    tasks.o(i.prvListTasksWithinSingleList) refers to tasks.o(i.vTaskGetInfo) for vTaskGetInfo
    tasks.o(i.prvResetNextTaskUnblockTime) refers to tasks.o(.data) for .data
    tasks.o(i.prvTaskIsTaskSuspended) refers to tasks.o(.bss) for .bss
    tasks.o(i.pvTaskIncrementMutexHeldCount) refers to tasks.o(.data) for .data
    tasks.o(i.ulTaskNotifyTake) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.ulTaskNotifyTake) refers to tasks.o(i.prvAddCurrentTaskToDelayedList) for prvAddCurrentTaskToDelayedList
    tasks.o(i.ulTaskNotifyTake) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.ulTaskNotifyTake) refers to tasks.o(.data) for .data
    tasks.o(i.ulTaskNotifyValueClear) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.ulTaskNotifyValueClear) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.ulTaskNotifyValueClear) refers to tasks.o(.data) for .data
    tasks.o(i.uxTaskGetNumberOfTasks) refers to tasks.o(.data) for .data
    tasks.o(i.uxTaskGetStackHighWaterMark) refers to tasks.o(i.prvTaskCheckFreeStackSpace) for prvTaskCheckFreeStackSpace
    tasks.o(i.uxTaskGetStackHighWaterMark) refers to tasks.o(.data) for .data
    tasks.o(i.uxTaskGetSystemState) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    tasks.o(i.uxTaskGetSystemState) refers to tasks.o(i.prvListTasksWithinSingleList) for prvListTasksWithinSingleList
    tasks.o(i.uxTaskGetSystemState) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    tasks.o(i.uxTaskGetSystemState) refers to tasks.o(.data) for .data
    tasks.o(i.uxTaskGetSystemState) refers to tasks.o(.bss) for .bss
    tasks.o(i.uxTaskPriorityGet) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.uxTaskPriorityGet) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.uxTaskPriorityGet) refers to tasks.o(.data) for .data
    tasks.o(i.uxTaskPriorityGetFromISR) refers to port.o(i.vPortValidateInterruptPriority) for vPortValidateInterruptPriority
    tasks.o(i.uxTaskPriorityGetFromISR) refers to tasks.o(.data) for .data
    tasks.o(i.uxTaskResetEventItemValue) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskDelay) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    tasks.o(i.vTaskDelay) refers to tasks.o(i.prvAddCurrentTaskToDelayedList) for prvAddCurrentTaskToDelayedList
    tasks.o(i.vTaskDelay) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    tasks.o(i.vTaskDelay) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskDelayUntil) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    tasks.o(i.vTaskDelayUntil) refers to tasks.o(i.prvAddCurrentTaskToDelayedList) for prvAddCurrentTaskToDelayedList
    tasks.o(i.vTaskDelayUntil) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    tasks.o(i.vTaskDelayUntil) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskDelete) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.vTaskDelete) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.vTaskDelete) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.vTaskDelete) refers to tasks.o(i.prvDeleteTCB) for prvDeleteTCB
    tasks.o(i.vTaskDelete) refers to tasks.o(i.prvResetNextTaskUnblockTime) for prvResetNextTaskUnblockTime
    tasks.o(i.vTaskDelete) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.vTaskDelete) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskDelete) refers to tasks.o(.bss) for .bss
    tasks.o(i.vTaskEndScheduler) refers to port.o(i.vPortEndScheduler) for vPortEndScheduler
    tasks.o(i.vTaskEndScheduler) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskGetInfo) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    tasks.o(i.vTaskGetInfo) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    tasks.o(i.vTaskGetInfo) refers to tasks.o(i.eTaskGetState) for eTaskGetState
    tasks.o(i.vTaskGetInfo) refers to tasks.o(i.prvTaskCheckFreeStackSpace) for prvTaskCheckFreeStackSpace
    tasks.o(i.vTaskGetInfo) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskInternalSetTimeOutState) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskMissedYield) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskNotifyGiveFromISR) refers to port.o(i.vPortValidateInterruptPriority) for vPortValidateInterruptPriority
    tasks.o(i.vTaskNotifyGiveFromISR) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.vTaskNotifyGiveFromISR) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.vTaskNotifyGiveFromISR) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskNotifyGiveFromISR) refers to tasks.o(.bss) for .bss
    tasks.o(i.vTaskPlaceOnEventList) refers to list.o(i.vListInsert) for vListInsert
    tasks.o(i.vTaskPlaceOnEventList) refers to tasks.o(i.prvAddCurrentTaskToDelayedList) for prvAddCurrentTaskToDelayedList
    tasks.o(i.vTaskPlaceOnEventList) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskPlaceOnEventListRestricted) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.vTaskPlaceOnEventListRestricted) refers to tasks.o(i.prvAddCurrentTaskToDelayedList) for prvAddCurrentTaskToDelayedList
    tasks.o(i.vTaskPlaceOnEventListRestricted) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskPlaceOnUnorderedEventList) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.vTaskPlaceOnUnorderedEventList) refers to tasks.o(i.prvAddCurrentTaskToDelayedList) for prvAddCurrentTaskToDelayedList
    tasks.o(i.vTaskPlaceOnUnorderedEventList) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskPriorityDisinheritAfterTimeout) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.vTaskPriorityDisinheritAfterTimeout) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.vTaskPriorityDisinheritAfterTimeout) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskPriorityDisinheritAfterTimeout) refers to tasks.o(.bss) for .bss
    tasks.o(i.vTaskPrioritySet) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.vTaskPrioritySet) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.vTaskPrioritySet) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.vTaskPrioritySet) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.vTaskPrioritySet) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskPrioritySet) refers to tasks.o(.bss) for .bss
    tasks.o(i.vTaskRemoveFromUnorderedEventList) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.vTaskRemoveFromUnorderedEventList) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.vTaskRemoveFromUnorderedEventList) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskRemoveFromUnorderedEventList) refers to tasks.o(.bss) for .bss
    tasks.o(i.vTaskResume) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.vTaskResume) refers to tasks.o(i.prvTaskIsTaskSuspended) for prvTaskIsTaskSuspended
    tasks.o(i.vTaskResume) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.vTaskResume) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.vTaskResume) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.vTaskResume) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskResume) refers to tasks.o(.bss) for .bss
    tasks.o(i.vTaskSetTimeOutState) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.vTaskSetTimeOutState) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.vTaskSetTimeOutState) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskStartScheduler) refers to cmsis_os2.o(i.vApplicationGetIdleTaskMemory) for vApplicationGetIdleTaskMemory
    tasks.o(i.vTaskStartScheduler) refers to tasks.o(i.xTaskCreateStatic) for xTaskCreateStatic
    tasks.o(i.vTaskStartScheduler) refers to timers.o(i.xTimerCreateTimerTask) for xTimerCreateTimerTask
    tasks.o(i.vTaskStartScheduler) refers to port.o(i.xPortStartScheduler) for xPortStartScheduler
    tasks.o(i.vTaskStartScheduler) refers to tasks.o(i.prvIdleTask) for prvIdleTask
    tasks.o(i.vTaskStartScheduler) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskSuspend) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.vTaskSuspend) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.vTaskSuspend) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.vTaskSuspend) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.vTaskSuspend) refers to tasks.o(i.prvResetNextTaskUnblockTime) for prvResetNextTaskUnblockTime
    tasks.o(i.vTaskSuspend) refers to tasks.o(i.vTaskSwitchContext) for vTaskSwitchContext
    tasks.o(i.vTaskSuspend) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskSuspend) refers to tasks.o(.bss) for .bss
    tasks.o(i.vTaskSuspendAll) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskSwitchContext) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskSwitchContext) refers to tasks.o(.bss) for .bss
    tasks.o(i.xTaskCatchUpTicks) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    tasks.o(i.xTaskCatchUpTicks) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    tasks.o(i.xTaskCatchUpTicks) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskCheckForTimeOut) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.xTaskCheckForTimeOut) refers to tasks.o(i.vTaskInternalSetTimeOutState) for vTaskInternalSetTimeOutState
    tasks.o(i.xTaskCheckForTimeOut) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.xTaskCheckForTimeOut) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskCreate) refers to heap_4.o(i.pvPortMalloc) for pvPortMalloc
    tasks.o(i.xTaskCreate) refers to heap_4.o(i.vPortFree) for vPortFree
    tasks.o(i.xTaskCreate) refers to tasks.o(i.prvInitialiseNewTask) for prvInitialiseNewTask
    tasks.o(i.xTaskCreate) refers to tasks.o(i.prvAddNewTaskToReadyList) for prvAddNewTaskToReadyList
    tasks.o(i.xTaskCreateStatic) refers to tasks.o(i.prvInitialiseNewTask) for prvInitialiseNewTask
    tasks.o(i.xTaskCreateStatic) refers to tasks.o(i.prvAddNewTaskToReadyList) for prvAddNewTaskToReadyList
    tasks.o(i.xTaskGenericNotify) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.xTaskGenericNotify) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.xTaskGenericNotify) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.xTaskGenericNotify) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.xTaskGenericNotify) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskGenericNotify) refers to tasks.o(.bss) for .bss
    tasks.o(i.xTaskGenericNotifyFromISR) refers to port.o(i.vPortValidateInterruptPriority) for vPortValidateInterruptPriority
    tasks.o(i.xTaskGenericNotifyFromISR) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.xTaskGenericNotifyFromISR) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.xTaskGenericNotifyFromISR) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskGenericNotifyFromISR) refers to tasks.o(.bss) for .bss
    tasks.o(i.xTaskGetCurrentTaskHandle) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskGetSchedulerState) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskGetTickCount) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskGetTickCountFromISR) refers to port.o(i.vPortValidateInterruptPriority) for vPortValidateInterruptPriority
    tasks.o(i.xTaskGetTickCountFromISR) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskIncrementTick) refers to tasks.o(i.prvResetNextTaskUnblockTime) for prvResetNextTaskUnblockTime
    tasks.o(i.xTaskIncrementTick) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.xTaskIncrementTick) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.xTaskIncrementTick) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskIncrementTick) refers to tasks.o(.bss) for .bss
    tasks.o(i.xTaskNotifyStateClear) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.xTaskNotifyStateClear) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.xTaskNotifyStateClear) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskNotifyWait) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.xTaskNotifyWait) refers to tasks.o(i.prvAddCurrentTaskToDelayedList) for prvAddCurrentTaskToDelayedList
    tasks.o(i.xTaskNotifyWait) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.xTaskNotifyWait) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskPriorityDisinherit) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.xTaskPriorityDisinherit) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.xTaskPriorityDisinherit) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskPriorityDisinherit) refers to tasks.o(.bss) for .bss
    tasks.o(i.xTaskPriorityInherit) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.xTaskPriorityInherit) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.xTaskPriorityInherit) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskPriorityInherit) refers to tasks.o(.bss) for .bss
    tasks.o(i.xTaskRemoveFromEventList) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.xTaskRemoveFromEventList) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.xTaskRemoveFromEventList) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskRemoveFromEventList) refers to tasks.o(.bss) for .bss
    tasks.o(i.xTaskResumeAll) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.xTaskResumeAll) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.xTaskResumeAll) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.xTaskResumeAll) refers to tasks.o(i.prvResetNextTaskUnblockTime) for prvResetNextTaskUnblockTime
    tasks.o(i.xTaskResumeAll) refers to tasks.o(i.xTaskIncrementTick) for xTaskIncrementTick
    tasks.o(i.xTaskResumeAll) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.xTaskResumeAll) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskResumeAll) refers to tasks.o(.bss) for .bss
    tasks.o(i.xTaskResumeFromISR) refers to port.o(i.vPortValidateInterruptPriority) for vPortValidateInterruptPriority
    tasks.o(i.xTaskResumeFromISR) refers to tasks.o(i.prvTaskIsTaskSuspended) for prvTaskIsTaskSuspended
    tasks.o(i.xTaskResumeFromISR) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.xTaskResumeFromISR) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.xTaskResumeFromISR) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskResumeFromISR) refers to tasks.o(.bss) for .bss
    timers.o(i.prvCheckForValidListAndQueue) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    timers.o(i.prvCheckForValidListAndQueue) refers to list.o(i.vListInitialise) for vListInitialise
    timers.o(i.prvCheckForValidListAndQueue) refers to queue.o(i.xQueueGenericCreateStatic) for xQueueGenericCreateStatic
    timers.o(i.prvCheckForValidListAndQueue) refers to queue.o(i.vQueueAddToRegistry) for vQueueAddToRegistry
    timers.o(i.prvCheckForValidListAndQueue) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    timers.o(i.prvCheckForValidListAndQueue) refers to timers.o(.data) for .data
    timers.o(i.prvCheckForValidListAndQueue) refers to timers.o(.bss) for .bss
    timers.o(i.prvGetNextExpireTime) refers to timers.o(.data) for .data
    timers.o(i.prvInitialiseNewTimer) refers to timers.o(i.prvCheckForValidListAndQueue) for prvCheckForValidListAndQueue
    timers.o(i.prvInitialiseNewTimer) refers to list.o(i.vListInitialiseItem) for vListInitialiseItem
    timers.o(i.prvInsertTimerInActiveList) refers to list.o(i.vListInsert) for vListInsert
    timers.o(i.prvInsertTimerInActiveList) refers to timers.o(.data) for .data
    timers.o(i.prvProcessExpiredTimer) refers to list.o(i.uxListRemove) for uxListRemove
    timers.o(i.prvProcessExpiredTimer) refers to timers.o(i.prvInsertTimerInActiveList) for prvInsertTimerInActiveList
    timers.o(i.prvProcessExpiredTimer) refers to timers.o(i.xTimerGenericCommand) for xTimerGenericCommand
    timers.o(i.prvProcessExpiredTimer) refers to timers.o(.data) for .data
    timers.o(i.prvProcessReceivedCommands) refers to list.o(i.uxListRemove) for uxListRemove
    timers.o(i.prvProcessReceivedCommands) refers to timers.o(i.prvSampleTimeNow) for prvSampleTimeNow
    timers.o(i.prvProcessReceivedCommands) refers to timers.o(i.prvInsertTimerInActiveList) for prvInsertTimerInActiveList
    timers.o(i.prvProcessReceivedCommands) refers to timers.o(i.xTimerGenericCommand) for xTimerGenericCommand
    timers.o(i.prvProcessReceivedCommands) refers to heap_4.o(i.vPortFree) for vPortFree
    timers.o(i.prvProcessReceivedCommands) refers to queue.o(i.xQueueReceive) for xQueueReceive
    timers.o(i.prvProcessReceivedCommands) refers to timers.o(.data) for .data
    timers.o(i.prvProcessTimerOrBlockTask) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    timers.o(i.prvProcessTimerOrBlockTask) refers to timers.o(i.prvSampleTimeNow) for prvSampleTimeNow
    timers.o(i.prvProcessTimerOrBlockTask) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    timers.o(i.prvProcessTimerOrBlockTask) refers to timers.o(i.prvProcessExpiredTimer) for prvProcessExpiredTimer
    timers.o(i.prvProcessTimerOrBlockTask) refers to queue.o(i.vQueueWaitForMessageRestricted) for vQueueWaitForMessageRestricted
    timers.o(i.prvProcessTimerOrBlockTask) refers to timers.o(.data) for .data
    timers.o(i.prvSampleTimeNow) refers to tasks.o(i.xTaskGetTickCount) for xTaskGetTickCount
    timers.o(i.prvSampleTimeNow) refers to timers.o(i.prvSwitchTimerLists) for prvSwitchTimerLists
    timers.o(i.prvSampleTimeNow) refers to timers.o(.data) for .data
    timers.o(i.prvSwitchTimerLists) refers to list.o(i.uxListRemove) for uxListRemove
    timers.o(i.prvSwitchTimerLists) refers to list.o(i.vListInsert) for vListInsert
    timers.o(i.prvSwitchTimerLists) refers to timers.o(i.xTimerGenericCommand) for xTimerGenericCommand
    timers.o(i.prvSwitchTimerLists) refers to timers.o(.data) for .data
    timers.o(i.prvTimerTask) refers to timers.o(i.prvGetNextExpireTime) for prvGetNextExpireTime
    timers.o(i.prvTimerTask) refers to timers.o(i.prvProcessTimerOrBlockTask) for prvProcessTimerOrBlockTask
    timers.o(i.prvTimerTask) refers to timers.o(i.prvProcessReceivedCommands) for prvProcessReceivedCommands
    timers.o(i.pvTimerGetTimerID) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    timers.o(i.pvTimerGetTimerID) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    timers.o(i.uxTimerGetReloadMode) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    timers.o(i.uxTimerGetReloadMode) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    timers.o(i.vTimerSetReloadMode) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    timers.o(i.vTimerSetReloadMode) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    timers.o(i.vTimerSetTimerID) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    timers.o(i.vTimerSetTimerID) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    timers.o(i.xTimerCreate) refers to heap_4.o(i.pvPortMalloc) for pvPortMalloc
    timers.o(i.xTimerCreate) refers to timers.o(i.prvInitialiseNewTimer) for prvInitialiseNewTimer
    timers.o(i.xTimerCreateStatic) refers to timers.o(i.prvInitialiseNewTimer) for prvInitialiseNewTimer
    timers.o(i.xTimerCreateTimerTask) refers to timers.o(i.prvCheckForValidListAndQueue) for prvCheckForValidListAndQueue
    timers.o(i.xTimerCreateTimerTask) refers to cmsis_os2.o(i.vApplicationGetTimerTaskMemory) for vApplicationGetTimerTaskMemory
    timers.o(i.xTimerCreateTimerTask) refers to tasks.o(i.xTaskCreateStatic) for xTaskCreateStatic
    timers.o(i.xTimerCreateTimerTask) refers to timers.o(.data) for .data
    timers.o(i.xTimerCreateTimerTask) refers to timers.o(i.prvTimerTask) for prvTimerTask
    timers.o(i.xTimerGenericCommand) refers to tasks.o(i.xTaskGetSchedulerState) for xTaskGetSchedulerState
    timers.o(i.xTimerGenericCommand) refers to queue.o(i.xQueueGenericSend) for xQueueGenericSend
    timers.o(i.xTimerGenericCommand) refers to queue.o(i.xQueueGenericSendFromISR) for xQueueGenericSendFromISR
    timers.o(i.xTimerGenericCommand) refers to timers.o(.data) for .data
    timers.o(i.xTimerGetTimerDaemonTaskHandle) refers to timers.o(.data) for .data
    timers.o(i.xTimerIsTimerActive) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    timers.o(i.xTimerIsTimerActive) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    timers.o(i.xTimerPendFunctionCall) refers to queue.o(i.xQueueGenericSend) for xQueueGenericSend
    timers.o(i.xTimerPendFunctionCall) refers to timers.o(.data) for .data
    timers.o(i.xTimerPendFunctionCallFromISR) refers to queue.o(i.xQueueGenericSendFromISR) for xQueueGenericSendFromISR
    timers.o(i.xTimerPendFunctionCallFromISR) refers to timers.o(.data) for .data
    cmsis_os2.o(i.SysTick_Handler) refers to tasks.o(i.xTaskGetSchedulerState) for xTaskGetSchedulerState
    cmsis_os2.o(i.SysTick_Handler) refers to port.o(i.xPortSysTickHandler) for xPortSysTickHandler
    cmsis_os2.o(i.TimerCallback) refers to timers.o(i.pvTimerGetTimerID) for pvTimerGetTimerID
    cmsis_os2.o(i.osDelay) refers to tasks.o(i.vTaskDelay) for vTaskDelay
    cmsis_os2.o(i.osDelayUntil) refers to tasks.o(i.xTaskGetTickCount) for xTaskGetTickCount
    cmsis_os2.o(i.osDelayUntil) refers to tasks.o(i.vTaskDelayUntil) for vTaskDelayUntil
    cmsis_os2.o(i.osEventFlagsClear) refers to event_groups.o(i.xEventGroupGetBitsFromISR) for xEventGroupGetBitsFromISR
    cmsis_os2.o(i.osEventFlagsClear) refers to event_groups.o(i.xEventGroupClearBitsFromISR) for xEventGroupClearBitsFromISR
    cmsis_os2.o(i.osEventFlagsClear) refers to event_groups.o(i.xEventGroupClearBits) for xEventGroupClearBits
    cmsis_os2.o(i.osEventFlagsDelete) refers to event_groups.o(i.vEventGroupDelete) for vEventGroupDelete
    cmsis_os2.o(i.osEventFlagsGet) refers to event_groups.o(i.xEventGroupGetBitsFromISR) for xEventGroupGetBitsFromISR
    cmsis_os2.o(i.osEventFlagsGet) refers to event_groups.o(i.xEventGroupClearBits) for xEventGroupClearBits
    cmsis_os2.o(i.osEventFlagsNew) refers to event_groups.o(i.xEventGroupCreateStatic) for xEventGroupCreateStatic
    cmsis_os2.o(i.osEventFlagsNew) refers to event_groups.o(i.xEventGroupCreate) for xEventGroupCreate
    cmsis_os2.o(i.osEventFlagsSet) refers to event_groups.o(i.xEventGroupSetBitsFromISR) for xEventGroupSetBitsFromISR
    cmsis_os2.o(i.osEventFlagsSet) refers to event_groups.o(i.xEventGroupSetBits) for xEventGroupSetBits
    cmsis_os2.o(i.osEventFlagsWait) refers to event_groups.o(i.xEventGroupWaitBits) for xEventGroupWaitBits
    cmsis_os2.o(i.osKernelGetInfo) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    cmsis_os2.o(i.osKernelGetState) refers to tasks.o(i.xTaskGetSchedulerState) for xTaskGetSchedulerState
    cmsis_os2.o(i.osKernelGetState) refers to cmsis_os2.o(.data) for .data
    cmsis_os2.o(i.osKernelGetSysTimerCount) refers to tasks.o(i.xTaskGetTickCount) for xTaskGetTickCount
    cmsis_os2.o(i.osKernelGetSysTimerCount) refers to cmsis_os2.o(i.OS_Tick_GetCount) for OS_Tick_GetCount
    cmsis_os2.o(i.osKernelGetSysTimerCount) refers to cmsis_os2.o(i.OS_Tick_GetOverflow) for OS_Tick_GetOverflow
    cmsis_os2.o(i.osKernelGetSysTimerCount) refers to cmsis_os2.o(i.OS_Tick_GetInterval) for OS_Tick_GetInterval
    cmsis_os2.o(i.osKernelGetSysTimerFreq) refers to system_stm32l4xx.o(.data) for SystemCoreClock
    cmsis_os2.o(i.osKernelGetTickCount) refers to tasks.o(i.xTaskGetTickCountFromISR) for xTaskGetTickCountFromISR
    cmsis_os2.o(i.osKernelGetTickCount) refers to tasks.o(i.xTaskGetTickCount) for xTaskGetTickCount
    cmsis_os2.o(i.osKernelInitialize) refers to cmsis_os2.o(.data) for .data
    cmsis_os2.o(i.osKernelLock) refers to tasks.o(i.xTaskGetSchedulerState) for xTaskGetSchedulerState
    cmsis_os2.o(i.osKernelLock) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    cmsis_os2.o(i.osKernelRestoreLock) refers to tasks.o(i.xTaskGetSchedulerState) for xTaskGetSchedulerState
    cmsis_os2.o(i.osKernelRestoreLock) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    cmsis_os2.o(i.osKernelRestoreLock) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    cmsis_os2.o(i.osKernelStart) refers to tasks.o(i.vTaskStartScheduler) for vTaskStartScheduler
    cmsis_os2.o(i.osKernelStart) refers to cmsis_os2.o(.data) for .data
    cmsis_os2.o(i.osKernelUnlock) refers to tasks.o(i.xTaskGetSchedulerState) for xTaskGetSchedulerState
    cmsis_os2.o(i.osKernelUnlock) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    cmsis_os2.o(i.osMemoryPoolAlloc) refers to queue.o(i.xQueueReceiveFromISR) for xQueueReceiveFromISR
    cmsis_os2.o(i.osMemoryPoolAlloc) refers to cmsis_os2.o(i.AllocBlock) for AllocBlock
    cmsis_os2.o(i.osMemoryPoolAlloc) refers to cmsis_os2.o(i.CreateBlock) for CreateBlock
    cmsis_os2.o(i.osMemoryPoolAlloc) refers to queue.o(i.xQueueSemaphoreTake) for xQueueSemaphoreTake
    cmsis_os2.o(i.osMemoryPoolAlloc) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    cmsis_os2.o(i.osMemoryPoolAlloc) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    cmsis_os2.o(i.osMemoryPoolDelete) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    cmsis_os2.o(i.osMemoryPoolDelete) refers to queue.o(i.xQueueGenericSend) for xQueueGenericSend
    cmsis_os2.o(i.osMemoryPoolDelete) refers to heap_4.o(i.vPortFree) for vPortFree
    cmsis_os2.o(i.osMemoryPoolDelete) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    cmsis_os2.o(i.osMemoryPoolFree) refers to queue.o(i.uxQueueMessagesWaitingFromISR) for uxQueueMessagesWaitingFromISR
    cmsis_os2.o(i.osMemoryPoolFree) refers to cmsis_os2.o(i.FreeBlock) for FreeBlock
    cmsis_os2.o(i.osMemoryPoolFree) refers to queue.o(i.xQueueGiveFromISR) for xQueueGiveFromISR
    cmsis_os2.o(i.osMemoryPoolFree) refers to queue.o(i.uxQueueMessagesWaiting) for uxQueueMessagesWaiting
    cmsis_os2.o(i.osMemoryPoolFree) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    cmsis_os2.o(i.osMemoryPoolFree) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    cmsis_os2.o(i.osMemoryPoolFree) refers to queue.o(i.xQueueGenericSend) for xQueueGenericSend
    cmsis_os2.o(i.osMemoryPoolGetCount) refers to queue.o(i.uxQueueMessagesWaitingFromISR) for uxQueueMessagesWaitingFromISR
    cmsis_os2.o(i.osMemoryPoolGetCount) refers to queue.o(i.uxQueueMessagesWaiting) for uxQueueMessagesWaiting
    cmsis_os2.o(i.osMemoryPoolGetSpace) refers to queue.o(i.uxQueueMessagesWaitingFromISR) for uxQueueMessagesWaitingFromISR
    cmsis_os2.o(i.osMemoryPoolGetSpace) refers to queue.o(i.uxQueueMessagesWaiting) for uxQueueMessagesWaiting
    cmsis_os2.o(i.osMemoryPoolNew) refers to queue.o(i.xQueueCreateCountingSemaphoreStatic) for xQueueCreateCountingSemaphoreStatic
    cmsis_os2.o(i.osMemoryPoolNew) refers to heap_4.o(i.pvPortMalloc) for pvPortMalloc
    cmsis_os2.o(i.osMemoryPoolNew) refers to heap_4.o(i.vPortFree) for vPortFree
    cmsis_os2.o(i.osMessageQueueDelete) refers to queue.o(i.vQueueUnregisterQueue) for vQueueUnregisterQueue
    cmsis_os2.o(i.osMessageQueueDelete) refers to queue.o(i.vQueueDelete) for vQueueDelete
    cmsis_os2.o(i.osMessageQueueGet) refers to queue.o(i.xQueueReceiveFromISR) for xQueueReceiveFromISR
    cmsis_os2.o(i.osMessageQueueGet) refers to queue.o(i.xQueueReceive) for xQueueReceive
    cmsis_os2.o(i.osMessageQueueGetCount) refers to queue.o(i.uxQueueMessagesWaitingFromISR) for uxQueueMessagesWaitingFromISR
    cmsis_os2.o(i.osMessageQueueGetCount) refers to queue.o(i.uxQueueMessagesWaiting) for uxQueueMessagesWaiting
    cmsis_os2.o(i.osMessageQueueGetSpace) refers to queue.o(i.uxQueueSpacesAvailable) for uxQueueSpacesAvailable
    cmsis_os2.o(i.osMessageQueueNew) refers to queue.o(i.xQueueGenericCreateStatic) for xQueueGenericCreateStatic
    cmsis_os2.o(i.osMessageQueueNew) refers to queue.o(i.xQueueGenericCreate) for xQueueGenericCreate
    cmsis_os2.o(i.osMessageQueueNew) refers to queue.o(i.vQueueAddToRegistry) for vQueueAddToRegistry
    cmsis_os2.o(i.osMessageQueuePut) refers to queue.o(i.xQueueGenericSendFromISR) for xQueueGenericSendFromISR
    cmsis_os2.o(i.osMessageQueuePut) refers to queue.o(i.xQueueGenericSend) for xQueueGenericSend
    cmsis_os2.o(i.osMessageQueueReset) refers to queue.o(i.xQueueGenericReset) for xQueueGenericReset
    cmsis_os2.o(i.osMutexAcquire) refers to queue.o(i.xQueueTakeMutexRecursive) for xQueueTakeMutexRecursive
    cmsis_os2.o(i.osMutexAcquire) refers to queue.o(i.xQueueSemaphoreTake) for xQueueSemaphoreTake
    cmsis_os2.o(i.osMutexDelete) refers to queue.o(i.vQueueUnregisterQueue) for vQueueUnregisterQueue
    cmsis_os2.o(i.osMutexDelete) refers to queue.o(i.vQueueDelete) for vQueueDelete
    cmsis_os2.o(i.osMutexGetOwner) refers to queue.o(i.xQueueGetMutexHolder) for xQueueGetMutexHolder
    cmsis_os2.o(i.osMutexNew) refers to queue.o(i.xQueueCreateMutexStatic) for xQueueCreateMutexStatic
    cmsis_os2.o(i.osMutexNew) refers to queue.o(i.xQueueCreateMutex) for xQueueCreateMutex
    cmsis_os2.o(i.osMutexNew) refers to queue.o(i.vQueueAddToRegistry) for vQueueAddToRegistry
    cmsis_os2.o(i.osMutexRelease) refers to queue.o(i.xQueueGiveMutexRecursive) for xQueueGiveMutexRecursive
    cmsis_os2.o(i.osMutexRelease) refers to queue.o(i.xQueueGenericSend) for xQueueGenericSend
    cmsis_os2.o(i.osSemaphoreAcquire) refers to queue.o(i.xQueueReceiveFromISR) for xQueueReceiveFromISR
    cmsis_os2.o(i.osSemaphoreAcquire) refers to queue.o(i.xQueueSemaphoreTake) for xQueueSemaphoreTake
    cmsis_os2.o(i.osSemaphoreDelete) refers to queue.o(i.vQueueUnregisterQueue) for vQueueUnregisterQueue
    cmsis_os2.o(i.osSemaphoreDelete) refers to queue.o(i.vQueueDelete) for vQueueDelete
    cmsis_os2.o(i.osSemaphoreGetCount) refers to queue.o(i.uxQueueMessagesWaitingFromISR) for uxQueueMessagesWaitingFromISR
    cmsis_os2.o(i.osSemaphoreGetCount) refers to queue.o(i.uxQueueMessagesWaiting) for uxQueueMessagesWaiting
    cmsis_os2.o(i.osSemaphoreNew) refers to queue.o(i.xQueueCreateCountingSemaphore) for xQueueCreateCountingSemaphore
    cmsis_os2.o(i.osSemaphoreNew) refers to queue.o(i.xQueueGenericCreate) for xQueueGenericCreate
    cmsis_os2.o(i.osSemaphoreNew) refers to queue.o(i.xQueueGenericSend) for xQueueGenericSend
    cmsis_os2.o(i.osSemaphoreNew) refers to queue.o(i.vQueueDelete) for vQueueDelete
    cmsis_os2.o(i.osSemaphoreNew) refers to queue.o(i.xQueueGenericCreateStatic) for xQueueGenericCreateStatic
    cmsis_os2.o(i.osSemaphoreNew) refers to queue.o(i.xQueueCreateCountingSemaphoreStatic) for xQueueCreateCountingSemaphoreStatic
    cmsis_os2.o(i.osSemaphoreNew) refers to queue.o(i.vQueueAddToRegistry) for vQueueAddToRegistry
    cmsis_os2.o(i.osSemaphoreRelease) refers to queue.o(i.xQueueGiveFromISR) for xQueueGiveFromISR
    cmsis_os2.o(i.osSemaphoreRelease) refers to queue.o(i.xQueueGenericSend) for xQueueGenericSend
    cmsis_os2.o(i.osThreadEnumerate) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    cmsis_os2.o(i.osThreadEnumerate) refers to tasks.o(i.uxTaskGetNumberOfTasks) for uxTaskGetNumberOfTasks
    cmsis_os2.o(i.osThreadEnumerate) refers to heap_4.o(i.pvPortMalloc) for pvPortMalloc
    cmsis_os2.o(i.osThreadEnumerate) refers to tasks.o(i.uxTaskGetSystemState) for uxTaskGetSystemState
    cmsis_os2.o(i.osThreadEnumerate) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    cmsis_os2.o(i.osThreadEnumerate) refers to heap_4.o(i.vPortFree) for vPortFree
    cmsis_os2.o(i.osThreadExit) refers to tasks.o(i.vTaskDelete) for vTaskDelete
    cmsis_os2.o(i.osThreadFlagsClear) refers to tasks.o(i.xTaskGetCurrentTaskHandle) for xTaskGetCurrentTaskHandle
    cmsis_os2.o(i.osThreadFlagsClear) refers to tasks.o(i.xTaskGenericNotify) for xTaskGenericNotify
    cmsis_os2.o(i.osThreadFlagsGet) refers to tasks.o(i.xTaskGetCurrentTaskHandle) for xTaskGetCurrentTaskHandle
    cmsis_os2.o(i.osThreadFlagsGet) refers to tasks.o(i.xTaskGenericNotify) for xTaskGenericNotify
    cmsis_os2.o(i.osThreadFlagsSet) refers to tasks.o(i.xTaskGenericNotifyFromISR) for xTaskGenericNotifyFromISR
    cmsis_os2.o(i.osThreadFlagsSet) refers to tasks.o(i.xTaskGenericNotify) for xTaskGenericNotify
    cmsis_os2.o(i.osThreadFlagsWait) refers to tasks.o(i.xTaskGetTickCount) for xTaskGetTickCount
    cmsis_os2.o(i.osThreadFlagsWait) refers to tasks.o(i.xTaskNotifyWait) for xTaskNotifyWait
    cmsis_os2.o(i.osThreadGetCount) refers to tasks.o(i.uxTaskGetNumberOfTasks) for uxTaskGetNumberOfTasks
    cmsis_os2.o(i.osThreadGetId) refers to tasks.o(i.xTaskGetCurrentTaskHandle) for xTaskGetCurrentTaskHandle
    cmsis_os2.o(i.osThreadGetName) refers to tasks.o(i.pcTaskGetName) for pcTaskGetName
    cmsis_os2.o(i.osThreadGetPriority) refers to tasks.o(i.uxTaskPriorityGet) for uxTaskPriorityGet
    cmsis_os2.o(i.osThreadGetStackSpace) refers to tasks.o(i.uxTaskGetStackHighWaterMark) for uxTaskGetStackHighWaterMark
    cmsis_os2.o(i.osThreadGetState) refers to tasks.o(i.eTaskGetState) for eTaskGetState
    cmsis_os2.o(i.osThreadNew) refers to tasks.o(i.xTaskCreateStatic) for xTaskCreateStatic
    cmsis_os2.o(i.osThreadNew) refers to tasks.o(i.xTaskCreate) for xTaskCreate
    cmsis_os2.o(i.osThreadResume) refers to tasks.o(i.vTaskResume) for vTaskResume
    cmsis_os2.o(i.osThreadSetPriority) refers to tasks.o(i.vTaskPrioritySet) for vTaskPrioritySet
    cmsis_os2.o(i.osThreadSuspend) refers to tasks.o(i.vTaskSuspend) for vTaskSuspend
    cmsis_os2.o(i.osThreadTerminate) refers to tasks.o(i.eTaskGetState) for eTaskGetState
    cmsis_os2.o(i.osThreadTerminate) refers to tasks.o(i.vTaskDelete) for vTaskDelete
    cmsis_os2.o(i.osTimerDelete) refers to timers.o(i.pvTimerGetTimerID) for pvTimerGetTimerID
    cmsis_os2.o(i.osTimerDelete) refers to timers.o(i.xTimerGenericCommand) for xTimerGenericCommand
    cmsis_os2.o(i.osTimerDelete) refers to heap_4.o(i.vPortFree) for vPortFree
    cmsis_os2.o(i.osTimerGetName) refers to timers.o(i.pcTimerGetName) for pcTimerGetName
    cmsis_os2.o(i.osTimerIsRunning) refers to timers.o(i.xTimerIsTimerActive) for xTimerIsTimerActive
    cmsis_os2.o(i.osTimerNew) refers to heap_4.o(i.pvPortMalloc) for pvPortMalloc
    cmsis_os2.o(i.osTimerNew) refers to timers.o(i.xTimerCreateStatic) for xTimerCreateStatic
    cmsis_os2.o(i.osTimerNew) refers to timers.o(i.xTimerCreate) for xTimerCreate
    cmsis_os2.o(i.osTimerNew) refers to heap_4.o(i.vPortFree) for vPortFree
    cmsis_os2.o(i.osTimerNew) refers to cmsis_os2.o(i.TimerCallback) for TimerCallback
    cmsis_os2.o(i.osTimerStart) refers to timers.o(i.xTimerGenericCommand) for xTimerGenericCommand
    cmsis_os2.o(i.osTimerStop) refers to timers.o(i.xTimerIsTimerActive) for xTimerIsTimerActive
    cmsis_os2.o(i.osTimerStop) refers to timers.o(i.xTimerGenericCommand) for xTimerGenericCommand
    cmsis_os2.o(i.vApplicationGetIdleTaskMemory) refers to cmsis_os2.o(.bss) for .bss
    cmsis_os2.o(i.vApplicationGetTimerTaskMemory) refers to cmsis_os2.o(.bss) for .bss
    heap_4.o(i.prvHeapInit) refers to heap_4.o(.bss) for .bss
    heap_4.o(i.prvHeapInit) refers to heap_4.o(.data) for .data
    heap_4.o(i.prvInsertBlockIntoFreeList) refers to heap_4.o(.data) for .data
    heap_4.o(i.pvPortMalloc) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    heap_4.o(i.pvPortMalloc) refers to heap_4.o(i.prvHeapInit) for prvHeapInit
    heap_4.o(i.pvPortMalloc) refers to heap_4.o(i.prvInsertBlockIntoFreeList) for prvInsertBlockIntoFreeList
    heap_4.o(i.pvPortMalloc) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    heap_4.o(i.pvPortMalloc) refers to heap_4.o(.data) for .data
    heap_4.o(i.vPortFree) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    heap_4.o(i.vPortFree) refers to heap_4.o(i.prvInsertBlockIntoFreeList) for prvInsertBlockIntoFreeList
    heap_4.o(i.vPortFree) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    heap_4.o(i.vPortFree) refers to heap_4.o(.data) for .data
    heap_4.o(i.vPortGetHeapStats) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    heap_4.o(i.vPortGetHeapStats) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    heap_4.o(i.vPortGetHeapStats) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    heap_4.o(i.vPortGetHeapStats) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    heap_4.o(i.vPortGetHeapStats) refers to heap_4.o(.data) for .data
    heap_4.o(i.xPortGetFreeHeapSize) refers to heap_4.o(.data) for .data
    heap_4.o(i.xPortGetMinimumEverFreeHeapSize) refers to heap_4.o(.data) for .data
    port.o(.emb_text) refers to tasks.o(i.vTaskSwitchContext) for vTaskSwitchContext
    port.o(.emb_text) refers to tasks.o(.data) for pxCurrentTCB
    port.o(i.prvTaskExitError) refers to port.o(.data) for .data
    port.o(i.pxPortInitialiseStack) refers to port.o(i.prvTaskExitError) for prvTaskExitError
    port.o(i.vPortEndScheduler) refers to port.o(.data) for .data
    port.o(i.vPortEnterCritical) refers to port.o(.data) for .data
    port.o(i.vPortExitCritical) refers to port.o(.data) for .data
    port.o(i.vPortSetupTimerInterrupt) refers to system_stm32l4xx.o(.data) for SystemCoreClock
    port.o(i.vPortValidateInterruptPriority) refers to port.o(.emb_text) for vPortGetIPSR
    port.o(i.vPortValidateInterruptPriority) refers to port.o(.data) for .data
    port.o(i.xPortStartScheduler) refers to port.o(i.vPortSetupTimerInterrupt) for vPortSetupTimerInterrupt
    port.o(i.xPortStartScheduler) refers to port.o(.emb_text) for __asm___6_port_c_39a90d8d__prvEnableVFP
    port.o(i.xPortStartScheduler) refers to port.o(.data) for .data
    port.o(i.xPortSysTickHandler) refers to tasks.o(i.xTaskIncrementTick) for xTaskIncrementTick
    modbus_rtu_slave.o(i.Modbus_CRC16) refers to modbus_rtu_slave.o(.constdata) for .constdata
    modbus_rtu_slave.o(i.Modbus_Init) refers to modbus_rtu_slave.o(.bss) for .bss
    modbus_rtu_slave.o(i.Modbus_Process_Request) refers to modbus_rtu_slave.o(i.Modbus_CRC16) for Modbus_CRC16
    modbus_rtu_slave.o(i.Modbus_Process_Request) refers to modbus_rtu_slave.o(.bss) for .bss
    modbus_rtu_slave.o(i.Modbus_RTU_Process) refers to rs485.o(i.RS485_Get_Line_Idle_Time) for RS485_Get_Line_Idle_Time
    modbus_rtu_slave.o(i.Modbus_RTU_Process) refers to rs485.o(i.UART_Get_Rx_Length) for UART_Get_Rx_Length
    modbus_rtu_slave.o(i.Modbus_RTU_Process) refers to rs485.o(i.UART_Get_Rx_Buffer) for UART_Get_Rx_Buffer
    modbus_rtu_slave.o(i.Modbus_RTU_Process) refers to rs485.o(i.UART_Start_DMA_Receive) for UART_Start_DMA_Receive
    modbus_rtu_slave.o(i.Modbus_RTU_Process) refers to modbus_rtu_slave.o(i.Modbus_Process_Request) for Modbus_Process_Request
    modbus_test.o(i.Modbus_Test_Communication) refers to mb.o(i.eMBPoll) for eMBPoll
    modbus_test.o(i.Modbus_Test_Communication) refers to modbus_test.o(.data) for .data
    modbus_test.o(i.Modbus_Test_Get_Holding_Regs) refers to modbus_test.o(.data) for .data
    modbus_test.o(i.Modbus_Test_Get_Stats) refers to modbus_test.o(.data) for .data
    modbus_test.o(i.Modbus_Test_Inc_Callback_Count) refers to modbus_test.o(.data) for .data
    modbus_test.o(i.Modbus_Test_Inc_Error_Count) refers to modbus_test.o(.data) for .data
    modbus_test.o(i.Modbus_Test_Inc_Poll_Count) refers to modbus_test.o(.data) for .data
    modbus_test.o(i.Modbus_Test_Inc_Rx_Count) refers to modbus_test.o(.data) for .data
    modbus_test.o(i.Modbus_Test_Inc_Timer_Count) refers to modbus_test.o(.data) for .data
    modbus_test.o(i.Modbus_Test_Inc_Tx_Count) refers to modbus_test.o(.data) for .data
    modbus_test.o(i.Modbus_Test_Init) refers to modbus_test.o(.data) for .data
    modbus_test.o(i.Modbus_Test_LED_Indicate) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_TogglePin) for HAL_GPIO_TogglePin
    modbus_test.o(i.Modbus_Test_LED_Indicate) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    modbus_test.o(i.Modbus_Test_Update_Registers) refers to modbus_test.o(.data) for .data
    rs485.o(i.RS485_Enter_RX_Mode) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    rs485.o(i.RS485_Enter_TX_Mode) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    rs485.o(i.RS485_Get_Line_Idle_Time) refers to cmsis_os2.o(i.osKernelGetTickCount) for osKernelGetTickCount
    rs485.o(i.RS485_Get_Line_Idle_Time) refers to rs485.o(.data) for .data
    rs485.o(i.RS485_Init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    rs485.o(i.RS485_Init) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    rs485.o(i.RS485_Init) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    rs485.o(i.UART_DMA_Init) refers to stm32l4xx_hal_uart.o(i.HAL_UART_Receive_DMA) for HAL_UART_Receive_DMA
    rs485.o(i.UART_DMA_Init) refers to rs485.o(.bss) for .bss
    rs485.o(i.UART_Get_Rx_Buffer) refers to rs485.o(.bss) for .bss
    rs485.o(i.UART_Get_Rx_Length) refers to rs485.o(.data) for .data
    rs485.o(i.UART_Start_DMA_Receive) refers to rt_memclr.o(.text) for __aeabi_memclr
    rs485.o(i.UART_Start_DMA_Receive) refers to stm32l4xx_hal_uart.o(i.HAL_UART_Receive_DMA) for HAL_UART_Receive_DMA
    rs485.o(i.UART_Start_DMA_Receive) refers to rs485.o(.bss) for .bss
    rs485.o(i.UART_Start_DMA_Receive) refers to usart.o(.bss) for huart1
    rn7326.o(i.RN7326_ReadReg) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    rn7326.o(i.RN7326_ReadReg) refers to cmsis_os2.o(i.osDelay) for osDelay
    rn7326.o(i.RN7326_ReadReg) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_Receive) for HAL_SPI_Receive
    rn7326.o(i.RN7326_ReadReg) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_Transmit) for HAL_SPI_Transmit
    rn7326.o(i.RN7326_ReadReg) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_TransmitReceive) for HAL_SPI_TransmitReceive
    rn7326.o(i.RN7326_ReadReg) refers to spi.o(.bss) for hspi2
    rn7326.o(i.RN7326_WriteReg) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    rn7326.o(i.RN7326_WriteReg) refers to cmsis_os2.o(i.osDelay) for osDelay
    rn7326.o(i.RN7326_WriteReg) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_Receive) for HAL_SPI_Receive
    rn7326.o(i.RN7326_WriteReg) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_Transmit) for HAL_SPI_Transmit
    rn7326.o(i.RN7326_WriteReg) refers to spi.o(.bss) for hspi2
    rn7326.o(i.test_read_LostVoltage) refers to rn7326.o(i.RN7326_ReadReg) for RN7326_ReadReg
    rn7326.o(i.test_read_LostVoltage) refers to rn7326.o(.data) for .data
    rn7326.o(i.test_read_voltage) refers to rn7326.o(i.RN7326_ReadReg) for RN7326_ReadReg
    rn7326.o(i.test_read_voltage) refers to rn7326.o(.data) for .data
    mbfunccoils.o(i.eMBFuncReadCoils) refers to mdcb.o(i.eMBRegCoilsCB) for eMBRegCoilsCB
    mbfunccoils.o(i.eMBFuncReadCoils) refers to mbutils.o(i.prveMBError2Exception) for prveMBError2Exception
    mbfunccoils.o(i.eMBFuncWriteCoil) refers to mdcb.o(i.eMBRegCoilsCB) for eMBRegCoilsCB
    mbfunccoils.o(i.eMBFuncWriteCoil) refers to mbutils.o(i.prveMBError2Exception) for prveMBError2Exception
    mbfunccoils.o(i.eMBFuncWriteMultipleCoils) refers to mdcb.o(i.eMBRegCoilsCB) for eMBRegCoilsCB
    mbfunccoils.o(i.eMBFuncWriteMultipleCoils) refers to mbutils.o(i.prveMBError2Exception) for prveMBError2Exception
    mbfuncdisc.o(i.eMBFuncReadDiscreteInputs) refers to mdcb.o(i.eMBRegDiscreteCB) for eMBRegDiscreteCB
    mbfuncdisc.o(i.eMBFuncReadDiscreteInputs) refers to mbutils.o(i.prveMBError2Exception) for prveMBError2Exception
    mbfuncholding.o(i.eMBFuncReadHoldingRegister) refers to mdcb.o(i.eMBRegHoldingCB) for eMBRegHoldingCB
    mbfuncholding.o(i.eMBFuncReadHoldingRegister) refers to mbutils.o(i.prveMBError2Exception) for prveMBError2Exception
    mbfuncholding.o(i.eMBFuncReadWriteMultipleHoldingRegister) refers to mdcb.o(i.eMBRegHoldingCB) for eMBRegHoldingCB
    mbfuncholding.o(i.eMBFuncReadWriteMultipleHoldingRegister) refers to mbutils.o(i.prveMBError2Exception) for prveMBError2Exception
    mbfuncholding.o(i.eMBFuncWriteHoldingRegister) refers to mdcb.o(i.eMBRegHoldingCB) for eMBRegHoldingCB
    mbfuncholding.o(i.eMBFuncWriteHoldingRegister) refers to mbutils.o(i.prveMBError2Exception) for prveMBError2Exception
    mbfuncholding.o(i.eMBFuncWriteMultipleHoldingRegister) refers to mdcb.o(i.eMBRegHoldingCB) for eMBRegHoldingCB
    mbfuncholding.o(i.eMBFuncWriteMultipleHoldingRegister) refers to mbutils.o(i.prveMBError2Exception) for prveMBError2Exception
    mbfuncinput.o(i.eMBFuncReadInputRegister) refers to mdcb.o(i.eMBRegInputCB) for eMBRegInputCB
    mbfuncinput.o(i.eMBFuncReadInputRegister) refers to mbutils.o(i.prveMBError2Exception) for prveMBError2Exception
    mbfuncother.o(i.eMBFuncReportSlaveID) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    mbfuncother.o(i.eMBFuncReportSlaveID) refers to mbfuncother.o(.data) for .data
    mbfuncother.o(i.eMBFuncReportSlaveID) refers to mbfuncother.o(.bss) for .bss
    mbfuncother.o(i.eMBSetSlaveID) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    mbfuncother.o(i.eMBSetSlaveID) refers to mbfuncother.o(.data) for .data
    mbfuncother.o(i.eMBSetSlaveID) refers to mbfuncother.o(.bss) for .bss
    mbcrc.o(i.usMBCRC16) refers to mbcrc.o(.constdata) for .constdata
    mbrtu.o(i.eMBRTUInit) refers to portserial.o(i.xMBPortSerialInit) for xMBPortSerialInit
    mbrtu.o(i.eMBRTUInit) refers to porttimer.o(i.xMBPortTimersInit) for xMBPortTimersInit
    mbrtu.o(i.eMBRTUReceive) refers to mbcrc.o(i.usMBCRC16) for usMBCRC16
    mbrtu.o(i.eMBRTUReceive) refers to mbrtu.o(.data) for .data
    mbrtu.o(i.eMBRTUReceive) refers to mbrtu.o(.bss) for .bss
    mbrtu.o(i.eMBRTUSend) refers to mbcrc.o(i.usMBCRC16) for usMBCRC16
    mbrtu.o(i.eMBRTUSend) refers to portserial.o(i.vMBPortSerialEnable) for vMBPortSerialEnable
    mbrtu.o(i.eMBRTUSend) refers to mbrtu.o(.data) for .data
    mbrtu.o(i.eMBRTUSend) refers to mbrtu.o(.bss) for .bss
    mbrtu.o(i.eMBRTUStart) refers to portserial.o(i.vMBPortSerialEnable) for vMBPortSerialEnable
    mbrtu.o(i.eMBRTUStart) refers to porttimer.o(i.vMBPortTimersEnable) for vMBPortTimersEnable
    mbrtu.o(i.eMBRTUStart) refers to mbrtu.o(.data) for .data
    mbrtu.o(i.eMBRTUStop) refers to portserial.o(i.vMBPortSerialEnable) for vMBPortSerialEnable
    mbrtu.o(i.eMBRTUStop) refers to porttimer.o(i.vMBPortTimersDisable) for vMBPortTimersDisable
    mbrtu.o(i.xMBRTUReceiveFSM) refers to portserial.o(i.xMBPortSerialGetByte) for xMBPortSerialGetByte
    mbrtu.o(i.xMBRTUReceiveFSM) refers to porttimer.o(i.vMBPortTimersEnable) for vMBPortTimersEnable
    mbrtu.o(i.xMBRTUReceiveFSM) refers to mbrtu.o(.data) for .data
    mbrtu.o(i.xMBRTUReceiveFSM) refers to mbrtu.o(.bss) for .bss
    mbrtu.o(i.xMBRTUTimerT35Expired) refers to portevent.o(i.xMBPortEventPost) for xMBPortEventPost
    mbrtu.o(i.xMBRTUTimerT35Expired) refers to porttimer.o(i.vMBPortTimersDisable) for vMBPortTimersDisable
    mbrtu.o(i.xMBRTUTimerT35Expired) refers to mbrtu.o(.data) for .data
    mbrtu.o(i.xMBRTUTransmitFSM) refers to portserial.o(i.vMBPortSerialEnable) for vMBPortSerialEnable
    mbrtu.o(i.xMBRTUTransmitFSM) refers to portserial.o(i.xMBPortSerialPutByte) for xMBPortSerialPutByte
    mbrtu.o(i.xMBRTUTransmitFSM) refers to portevent.o(i.xMBPortEventPost) for xMBPortEventPost
    mbrtu.o(i.xMBRTUTransmitFSM) refers to mbrtu.o(.data) for .data
    mb.o(i.eMBClose) refers to mb.o(.data) for .data
    mb.o(i.eMBDisable) refers to mb.o(.data) for .data
    mb.o(i.eMBEnable) refers to mb.o(.data) for .data
    mb.o(i.eMBInit) refers to mbrtu.o(i.eMBRTUInit) for eMBRTUInit
    mb.o(i.eMBInit) refers to portevent.o(i.xMBPortEventInit) for xMBPortEventInit
    mb.o(i.eMBInit) refers to mb.o(.data) for .data
    mb.o(i.eMBInit) refers to mbrtu.o(i.eMBRTUStart) for eMBRTUStart
    mb.o(i.eMBInit) refers to mbrtu.o(i.eMBRTUStop) for eMBRTUStop
    mb.o(i.eMBInit) refers to mbrtu.o(i.eMBRTUSend) for eMBRTUSend
    mb.o(i.eMBInit) refers to mbrtu.o(i.eMBRTUReceive) for eMBRTUReceive
    mb.o(i.eMBInit) refers to mbrtu.o(i.xMBRTUReceiveFSM) for xMBRTUReceiveFSM
    mb.o(i.eMBInit) refers to mbrtu.o(i.xMBRTUTransmitFSM) for xMBRTUTransmitFSM
    mb.o(i.eMBInit) refers to mbrtu.o(i.xMBRTUTimerT35Expired) for xMBRTUTimerT35Expired
    mb.o(i.eMBPoll) refers to portevent.o(i.xMBPortEventGet) for xMBPortEventGet
    mb.o(i.eMBPoll) refers to portevent.o(i.xMBPortEventPost) for xMBPortEventPost
    mb.o(i.eMBPoll) refers to mb.o(.data) for .data
    mb.o(i.eMBRegisterCB) refers to mb.o(.data) for .data
    mb.o(.data) refers to mbfuncother.o(i.eMBFuncReportSlaveID) for eMBFuncReportSlaveID
    mb.o(.data) refers to mbfuncinput.o(i.eMBFuncReadInputRegister) for eMBFuncReadInputRegister
    mb.o(.data) refers to mbfuncholding.o(i.eMBFuncReadHoldingRegister) for eMBFuncReadHoldingRegister
    mb.o(.data) refers to mbfuncholding.o(i.eMBFuncWriteMultipleHoldingRegister) for eMBFuncWriteMultipleHoldingRegister
    mb.o(.data) refers to mbfuncholding.o(i.eMBFuncWriteHoldingRegister) for eMBFuncWriteHoldingRegister
    mb.o(.data) refers to mbfuncholding.o(i.eMBFuncReadWriteMultipleHoldingRegister) for eMBFuncReadWriteMultipleHoldingRegister
    mb.o(.data) refers to mbfunccoils.o(i.eMBFuncReadCoils) for eMBFuncReadCoils
    mb.o(.data) refers to mbfunccoils.o(i.eMBFuncWriteCoil) for eMBFuncWriteCoil
    mb.o(.data) refers to mbfunccoils.o(i.eMBFuncWriteMultipleCoils) for eMBFuncWriteMultipleCoils
    mb.o(.data) refers to mbfuncdisc.o(i.eMBFuncReadDiscreteInputs) for eMBFuncReadDiscreteInputs
    mdcb.o(i.eMBRegCoilsCB) refers to mbutils.o(i.xMBUtilGetBits) for xMBUtilGetBits
    mdcb.o(i.eMBRegCoilsCB) refers to mbutils.o(i.xMBUtilSetBits) for xMBUtilSetBits
    mdcb.o(i.eMBRegCoilsCB) refers to mdcb.o(.data) for .data
    mdcb.o(i.eMBRegDiscreteCB) refers to mbutils.o(i.xMBUtilGetBits) for xMBUtilGetBits
    mdcb.o(i.eMBRegDiscreteCB) refers to mdcb.o(.data) for .data
    mdcb.o(i.eMBRegHoldingCB) refers to modbus_test.o(i.Modbus_Test_Inc_Callback_Count) for Modbus_Test_Inc_Callback_Count
    mdcb.o(i.eMBRegHoldingCB) refers to modbus_test.o(i.Modbus_Test_Get_Holding_Regs) for Modbus_Test_Get_Holding_Regs
    mdcb.o(i.eMBRegHoldingCB) refers to mdcb.o(.data) for .data
    mdcb.o(i.eMBRegInputCB) refers to mdcb.o(.data) for .data
    portevent.o(i.xMBPortEventGet) refers to portevent.o(.data) for .data
    portevent.o(i.xMBPortEventInit) refers to portevent.o(.data) for .data
    portevent.o(i.xMBPortEventPost) refers to portevent.o(.data) for .data
    portserial.o(i.HAL_UART_ErrorCallback) refers to usart.o(.bss) for huart1
    portserial.o(i.HAL_UART_RxCpltCallback) refers to portserial.o(i.prvvUARTRxISR) for prvvUARTRxISR
    portserial.o(i.HAL_UART_TxCpltCallback) refers to rs485.o(i.RS485_Enter_RX_Mode) for RS485_Enter_RX_Mode
    portserial.o(i.HAL_UART_TxCpltCallback) refers to portserial.o(.data) for .data
    portserial.o(i.prvvUARTRxISR) refers to modbus_test.o(i.Modbus_Test_Inc_Rx_Count) for Modbus_Test_Inc_Rx_Count
    portserial.o(i.prvvUARTRxISR) refers to mb.o(.data) for pxMBFrameCBByteReceived
    portserial.o(i.prvvUARTTxReadyISR) refers to modbus_test.o(i.Modbus_Test_Inc_Tx_Count) for Modbus_Test_Inc_Tx_Count
    portserial.o(i.prvvUARTTxReadyISR) refers to mb.o(.data) for pxMBFrameCBTransmitterEmpty
    portserial.o(i.vMBPortSerialEnable) refers to rs485.o(i.RS485_Enter_RX_Mode) for RS485_Enter_RX_Mode
    portserial.o(i.vMBPortSerialEnable) refers to rs485.o(i.RS485_Enter_TX_Mode) for RS485_Enter_TX_Mode
    portserial.o(i.vMBPortSerialEnable) refers to usart.o(.bss) for huart1
    portserial.o(i.vMBPortSerialEnable) refers to portserial.o(.data) for .data
    portserial.o(i.xMBPortSerialGetByte) refers to portserial.o(.data) for .data
    portserial.o(i.xMBPortSerialPutByte) refers to usart.o(.bss) for huart1
    porttimer.o(i.prvvTIMERExpiredISR) refers to modbus_test.o(i.Modbus_Test_Inc_Timer_Count) for Modbus_Test_Inc_Timer_Count
    porttimer.o(i.prvvTIMERExpiredISR) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_TogglePin) for HAL_GPIO_TogglePin
    porttimer.o(i.prvvTIMERExpiredISR) refers to porttimer.o(.data) for .data
    porttimer.o(i.prvvTIMERExpiredISR) refers to mb.o(.data) for pxMBPortCBTimerExpired
    porttimer.o(i.xMBPortTimersInit) refers to porttimer.o(.data) for .data
    porttimer.o(i.xMBPortTimersInit) refers to tim.o(.bss) for htim16
    rt_memcpy_v6.o(.text) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    memset.o(.text) refers to rt_memclr.o(.text) for _memset
    aeabi_memset.o(.text) refers to rt_memclr.o(.text) for _memset
    rt_memclr.o(.text) refers to rt_memclr_w.o(.text) for _memset_w
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    aeabi_ldiv0_sigfpe.o(.text) refers to rt_div0.o(.text) for __rt_div0
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(i.main) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    rt_div0.o(.text) refers to defsig_fpe_outer.o(.text) for __rt_SIGFPE
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_stm32l496xx.o(.text) for __user_initial_stackheap
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    defsig_fpe_outer.o(.text) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig_fpe_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_fpe_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002C) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000001) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000A) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000011) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000033) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_user_alloc_1
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_exit.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    libinit2.o(.ARM.Collect$$libinit$$00000001) refers to fpinit.o(x$fpl$fpinit) for _fp_init
    libinit2.o(.ARM.Collect$$libinit$$00000010) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000026) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000027) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    sys_exit.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_exit.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to sys_exit.o(.text) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig.o(CL$$defsig) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000007) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000010) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_user_alloc_1
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    porttimer.o(i.vMBPortTimersEnable) refers to tim.o(.bss) for htim16
    porttimer.o(i.vMBPortTimersDisable) refers to tim.o(.bss) for htim16


==============================================================================

Removing Unused input sections from the image.

    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing main.o(.rrx_text), (6 bytes).
    Removing gpio.o(.rev16_text), (4 bytes).
    Removing gpio.o(.revsh_text), (4 bytes).
    Removing gpio.o(.rrx_text), (6 bytes).
    Removing freertos.o(.rev16_text), (4 bytes).
    Removing freertos.o(.revsh_text), (4 bytes).
    Removing freertos.o(.rrx_text), (6 bytes).
    Removing can.o(.rev16_text), (4 bytes).
    Removing can.o(.revsh_text), (4 bytes).
    Removing can.o(.rrx_text), (6 bytes).
    Removing can.o(i.HAL_CAN_MspDeInit), (52 bytes).
    Removing i2c.o(.rev16_text), (4 bytes).
    Removing i2c.o(.revsh_text), (4 bytes).
    Removing i2c.o(.rrx_text), (6 bytes).
    Removing i2c.o(i.HAL_I2C_MspDeInit), (96 bytes).
    Removing iwdg.o(.rev16_text), (4 bytes).
    Removing iwdg.o(.revsh_text), (4 bytes).
    Removing iwdg.o(.rrx_text), (6 bytes).
    Removing iwdg.o(i.MX_IWDG_Init), (48 bytes).
    Removing iwdg.o(.bss), (16 bytes).
    Removing spi.o(.rev16_text), (4 bytes).
    Removing spi.o(.revsh_text), (4 bytes).
    Removing spi.o(.rrx_text), (6 bytes).
    Removing spi.o(i.HAL_SPI_MspDeInit), (44 bytes).
    Removing tim.o(.rev16_text), (4 bytes).
    Removing tim.o(.revsh_text), (4 bytes).
    Removing tim.o(.rrx_text), (6 bytes).
    Removing tim.o(i.HAL_TIM_Base_MspDeInit), (36 bytes).
    Removing usart.o(.rev16_text), (4 bytes).
    Removing usart.o(.revsh_text), (4 bytes).
    Removing usart.o(.rrx_text), (6 bytes).
    Removing usart.o(i.HAL_UART_MspDeInit), (52 bytes).
    Removing stm32l4xx_it.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_it.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_it.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal_msp.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal_msp.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal_msp.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal_timebase_tim.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal_timebase_tim.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal_timebase_tim.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal_timebase_tim.o(i.HAL_ResumeTick), (20 bytes).
    Removing stm32l4xx_hal_timebase_tim.o(i.HAL_SuspendTick), (20 bytes).
    Removing stm32l4xx_hal_can.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal_can.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal_can.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal_can.o(i.HAL_CAN_AbortTxRequest), (70 bytes).
    Removing stm32l4xx_hal_can.o(i.HAL_CAN_ActivateNotification), (36 bytes).
    Removing stm32l4xx_hal_can.o(i.HAL_CAN_AddTxMessage), (226 bytes).
    Removing stm32l4xx_hal_can.o(i.HAL_CAN_ConfigFilter), (272 bytes).
    Removing stm32l4xx_hal_can.o(i.HAL_CAN_DeInit), (44 bytes).
    Removing stm32l4xx_hal_can.o(i.HAL_CAN_DeactivateNotification), (36 bytes).
    Removing stm32l4xx_hal_can.o(i.HAL_CAN_GetError), (4 bytes).
    Removing stm32l4xx_hal_can.o(i.HAL_CAN_GetRxFifoFillLevel), (38 bytes).
    Removing stm32l4xx_hal_can.o(i.HAL_CAN_GetRxMessage), (262 bytes).
    Removing stm32l4xx_hal_can.o(i.HAL_CAN_GetState), (36 bytes).
    Removing stm32l4xx_hal_can.o(i.HAL_CAN_GetTxMailboxesFreeLevel), (44 bytes).
    Removing stm32l4xx_hal_can.o(i.HAL_CAN_GetTxTimestamp), (40 bytes).
    Removing stm32l4xx_hal_can.o(i.HAL_CAN_IsSleepActive), (28 bytes).
    Removing stm32l4xx_hal_can.o(i.HAL_CAN_IsTxMessagePending), (30 bytes).
    Removing stm32l4xx_hal_can.o(i.HAL_CAN_MspDeInit), (2 bytes).
    Removing stm32l4xx_hal_can.o(i.HAL_CAN_MspInit), (2 bytes).
    Removing stm32l4xx_hal_can.o(i.HAL_CAN_RequestSleep), (38 bytes).
    Removing stm32l4xx_hal_can.o(i.HAL_CAN_ResetError), (34 bytes).
    Removing stm32l4xx_hal_can.o(i.HAL_CAN_Start), (88 bytes).
    Removing stm32l4xx_hal_can.o(i.HAL_CAN_Stop), (98 bytes).
    Removing stm32l4xx_hal_can.o(i.HAL_CAN_WakeUp), (84 bytes).
    Removing stm32l4xx_hal.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal.o(i.HAL_DBGMCU_DisableDBGSleepMode), (16 bytes).
    Removing stm32l4xx_hal.o(i.HAL_DBGMCU_DisableDBGStandbyMode), (16 bytes).
    Removing stm32l4xx_hal.o(i.HAL_DBGMCU_DisableDBGStopMode), (16 bytes).
    Removing stm32l4xx_hal.o(i.HAL_DBGMCU_EnableDBGSleepMode), (16 bytes).
    Removing stm32l4xx_hal.o(i.HAL_DBGMCU_EnableDBGStandbyMode), (16 bytes).
    Removing stm32l4xx_hal.o(i.HAL_DBGMCU_EnableDBGStopMode), (16 bytes).
    Removing stm32l4xx_hal.o(i.HAL_DeInit), (48 bytes).
    Removing stm32l4xx_hal.o(i.HAL_GetDEVID), (16 bytes).
    Removing stm32l4xx_hal.o(i.HAL_GetHalVersion), (8 bytes).
    Removing stm32l4xx_hal.o(i.HAL_GetREVID), (12 bytes).
    Removing stm32l4xx_hal.o(i.HAL_GetTickFreq), (12 bytes).
    Removing stm32l4xx_hal.o(i.HAL_GetTickPrio), (12 bytes).
    Removing stm32l4xx_hal.o(i.HAL_GetUIDw0), (12 bytes).
    Removing stm32l4xx_hal.o(i.HAL_GetUIDw1), (12 bytes).
    Removing stm32l4xx_hal.o(i.HAL_GetUIDw2), (12 bytes).
    Removing stm32l4xx_hal.o(i.HAL_InitTick), (76 bytes).
    Removing stm32l4xx_hal.o(i.HAL_MspDeInit), (2 bytes).
    Removing stm32l4xx_hal.o(i.HAL_MspInit), (2 bytes).
    Removing stm32l4xx_hal.o(i.HAL_ResumeTick), (14 bytes).
    Removing stm32l4xx_hal.o(i.HAL_SYSCFG_DisableIOAnalogSwitchBooster), (16 bytes).
    Removing stm32l4xx_hal.o(i.HAL_SYSCFG_DisableMemorySwappingBank), (12 bytes).
    Removing stm32l4xx_hal.o(i.HAL_SYSCFG_DisableVREFBUF), (16 bytes).
    Removing stm32l4xx_hal.o(i.HAL_SYSCFG_EnableIOAnalogSwitchBooster), (16 bytes).
    Removing stm32l4xx_hal.o(i.HAL_SYSCFG_EnableMemorySwappingBank), (12 bytes).
    Removing stm32l4xx_hal.o(i.HAL_SYSCFG_EnableVREFBUF), (48 bytes).
    Removing stm32l4xx_hal.o(i.HAL_SYSCFG_SRAM2Erase), (28 bytes).
    Removing stm32l4xx_hal.o(i.HAL_SYSCFG_VREFBUF_HighImpedanceConfig), (20 bytes).
    Removing stm32l4xx_hal.o(i.HAL_SYSCFG_VREFBUF_TrimmingConfig), (20 bytes).
    Removing stm32l4xx_hal.o(i.HAL_SYSCFG_VREFBUF_VoltageScalingConfig), (20 bytes).
    Removing stm32l4xx_hal.o(i.HAL_SetTickFreq), (36 bytes).
    Removing stm32l4xx_hal.o(i.HAL_SuspendTick), (14 bytes).
    Removing stm32l4xx_hal_i2c.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal_i2c.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal_i2c.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_AbortCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_AddrCallback), (2 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_DeInit), (50 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_DisableListen_IT), (52 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_ER_IRQHandler), (104 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler), (16 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_EnableListen_IT), (40 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback), (2 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_GetError), (4 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_GetMode), (6 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_GetState), (6 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady), (350 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Abort_IT), (124 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Receive), (338 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA), (304 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_IT), (140 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA), (338 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_IT), (164 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA), (338 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_IT), (164 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit), (338 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA), (304 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_IT), (140 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Read), (390 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA), (288 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_IT), (160 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Write), (378 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA), (288 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_IT), (160 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_MspDeInit), (2 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_MspInit), (2 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive), (360 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA), (224 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_IT), (96 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA), (388 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_IT), (204 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA), (388 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_IT), (204 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit), (406 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA), (212 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_IT), (96 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_ConvertOtherXferOptions), (28 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_DMAAbort), (22 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_DMAError), (18 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_DMAMasterReceiveCplt), (94 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_DMAMasterTransmitCplt), (94 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_DMASlaveReceiveCplt), (36 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_DMASlaveTransmitCplt), (30 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_Disable_IRQ), (90 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ), (108 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_Flush_TXDR), (34 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_ITAddrCplt), (164 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_ITError), (264 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_ITListenCplt), (100 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_ITMasterCplt), (260 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_ITMasterSeqCplt), (80 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_ITSlaveCplt), (324 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_ITSlaveSeqCplt), (116 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_IsErrorOccurred), (276 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_DMA), (296 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_IT), (344 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_Mem_ISR_DMA), (374 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_Mem_ISR_IT), (352 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_RequestMemoryRead), (116 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_RequestMemoryWrite), (116 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_Slave_ISR_DMA), (278 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_Slave_ISR_IT), (300 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_TransferConfig), (48 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_TreatErrorCallback), (42 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout), (86 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout), (168 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout), (76 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_WaitOnTXISFlagUntilTimeout), (80 bytes).
    Removing stm32l4xx_hal_i2c_ex.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal_i2c_ex.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal_i2c_ex.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal_i2c_ex.o(i.HAL_I2CEx_DisableFastModePlus), (40 bytes).
    Removing stm32l4xx_hal_i2c_ex.o(i.HAL_I2CEx_DisableWakeUp), (82 bytes).
    Removing stm32l4xx_hal_i2c_ex.o(i.HAL_I2CEx_EnableFastModePlus), (40 bytes).
    Removing stm32l4xx_hal_i2c_ex.o(i.HAL_I2CEx_EnableWakeUp), (82 bytes).
    Removing stm32l4xx_hal_rcc.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal_rcc.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal_rcc.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal_rcc.o(i.HAL_RCC_CSSCallback), (2 bytes).
    Removing stm32l4xx_hal_rcc.o(i.HAL_RCC_DeInit), (236 bytes).
    Removing stm32l4xx_hal_rcc.o(i.HAL_RCC_EnableCSS), (16 bytes).
    Removing stm32l4xx_hal_rcc.o(i.HAL_RCC_GetOscConfig), (232 bytes).
    Removing stm32l4xx_hal_rcc.o(i.HAL_RCC_GetResetSource), (24 bytes).
    Removing stm32l4xx_hal_rcc.o(i.HAL_RCC_MCOConfig), (72 bytes).
    Removing stm32l4xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler), (24 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRSConfig), (76 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRSGetSynchronizationInfo), (36 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRSSoftwareSynchronizationGenerate), (16 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRSWaitSynchronization), (128 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_ErrorCallback), (2 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_ExpectedSyncCallback), (2 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_IRQHandler), (108 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_SyncOkCallback), (2 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_SyncWarnCallback), (2 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisableLSCO), (88 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisableLSECSS), (24 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisableMSIPLLMode), (16 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisablePLLSAI1), (80 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisablePLLSAI2), (80 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnableLSCO), (144 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnableLSECSS), (16 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnableLSECSS_IT), (52 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnableMSIPLLMode), (16 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnablePLLSAI1), (152 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnablePLLSAI2), (140 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKConfig), (296 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq), (1744 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_LSECSS_Callback), (2 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_LSECSS_IRQHandler), (24 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_StandbyMSIRangeConfig), (20 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_WakeUpStopCLKConfig), (20 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.RCCEx_GetSAIxPeriphCLKFreq), (272 bytes).
    Removing stm32l4xx_hal_flash.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal_flash.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal_flash.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal_flash.o(i.FLASH_Program_DoubleWord), (28 bytes).
    Removing stm32l4xx_hal_flash.o(i.FLASH_Program_Fast), (44 bytes).
    Removing stm32l4xx_hal_flash.o(i.FLASH_WaitForLastOperation), (108 bytes).
    Removing stm32l4xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback), (2 bytes).
    Removing stm32l4xx_hal_flash.o(i.HAL_FLASH_GetError), (12 bytes).
    Removing stm32l4xx_hal_flash.o(i.HAL_FLASH_IRQHandler), (264 bytes).
    Removing stm32l4xx_hal_flash.o(i.HAL_FLASH_Lock), (20 bytes).
    Removing stm32l4xx_hal_flash.o(i.HAL_FLASH_OB_Launch), (24 bytes).
    Removing stm32l4xx_hal_flash.o(i.HAL_FLASH_OB_Lock), (20 bytes).
    Removing stm32l4xx_hal_flash.o(i.HAL_FLASH_OB_Unlock), (36 bytes).
    Removing stm32l4xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback), (2 bytes).
    Removing stm32l4xx_hal_flash.o(i.HAL_FLASH_Program), (186 bytes).
    Removing stm32l4xx_hal_flash.o(i.HAL_FLASH_Program_IT), (124 bytes).
    Removing stm32l4xx_hal_flash.o(i.HAL_FLASH_Unlock), (40 bytes).
    Removing stm32l4xx_hal_flash.o(.data), (32 bytes).
    Removing stm32l4xx_hal_flash_ex.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal_flash_ex.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal_flash_ex.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal_flash_ex.o(i.FLASH_FlushCaches), (92 bytes).
    Removing stm32l4xx_hal_flash_ex.o(i.FLASH_MassErase), (40 bytes).
    Removing stm32l4xx_hal_flash_ex.o(i.FLASH_OB_GetPCROP), (152 bytes).
    Removing stm32l4xx_hal_flash_ex.o(i.FLASH_OB_GetRDP), (28 bytes).
    Removing stm32l4xx_hal_flash_ex.o(i.FLASH_OB_GetUser), (16 bytes).
    Removing stm32l4xx_hal_flash_ex.o(i.FLASH_OB_GetWRP), (88 bytes).
    Removing stm32l4xx_hal_flash_ex.o(i.FLASH_OB_PCROPConfig), (204 bytes).
    Removing stm32l4xx_hal_flash_ex.o(i.FLASH_OB_RDPConfig), (60 bytes).
    Removing stm32l4xx_hal_flash_ex.o(i.FLASH_OB_UserConfig), (276 bytes).
    Removing stm32l4xx_hal_flash_ex.o(i.FLASH_OB_WRPConfig), (124 bytes).
    Removing stm32l4xx_hal_flash_ex.o(i.FLASH_PageErase), (56 bytes).
    Removing stm32l4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase), (254 bytes).
    Removing stm32l4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT), (144 bytes).
    Removing stm32l4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig), (88 bytes).
    Removing stm32l4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram), (116 bytes).
    Removing stm32l4xx_hal_flash_ramfunc.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal_flash_ramfunc.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal_flash_ramfunc.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal_flash_ramfunc.o(i.HAL_FLASHEx_DisableRunPowerDown), (36 bytes).
    Removing stm32l4xx_hal_flash_ramfunc.o(i.HAL_FLASHEx_EnableRunPowerDown), (36 bytes).
    Removing stm32l4xx_hal_gpio.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal_gpio.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal_gpio.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal_gpio.o(i.HAL_GPIO_DeInit), (318 bytes).
    Removing stm32l4xx_hal_gpio.o(i.HAL_GPIO_LockPin), (36 bytes).
    Removing stm32l4xx_hal_gpio.o(i.HAL_GPIO_ReadPin), (14 bytes).
    Removing stm32l4xx_hal_dma.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal_dma.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal_dma.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal_dma.o(i.DMA_SetConfig), (48 bytes).
    Removing stm32l4xx_hal_dma.o(i.HAL_DMA_DeInit), (168 bytes).
    Removing stm32l4xx_hal_dma.o(i.HAL_DMA_GetError), (4 bytes).
    Removing stm32l4xx_hal_dma.o(i.HAL_DMA_GetState), (6 bytes).
    Removing stm32l4xx_hal_dma.o(i.HAL_DMA_IRQHandler), (204 bytes).
    Removing stm32l4xx_hal_dma.o(i.HAL_DMA_Init), (224 bytes).
    Removing stm32l4xx_hal_dma.o(i.HAL_DMA_PollForTransfer), (252 bytes).
    Removing stm32l4xx_hal_dma.o(i.HAL_DMA_RegisterCallback), (76 bytes).
    Removing stm32l4xx_hal_dma.o(i.HAL_DMA_Start), (80 bytes).
    Removing stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT), (116 bytes).
    Removing stm32l4xx_hal_dma.o(i.HAL_DMA_UnRegisterCallback), (86 bytes).
    Removing stm32l4xx_hal_dma_ex.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal_dma_ex.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal_dma_ex.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal_pwr.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal_pwr.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal_pwr.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal_pwr.o(i.HAL_PWR_ConfigPVD), (128 bytes).
    Removing stm32l4xx_hal_pwr.o(i.HAL_PWR_DeInit), (24 bytes).
    Removing stm32l4xx_hal_pwr.o(i.HAL_PWR_DisableBkUpAccess), (16 bytes).
    Removing stm32l4xx_hal_pwr.o(i.HAL_PWR_DisablePVD), (16 bytes).
    Removing stm32l4xx_hal_pwr.o(i.HAL_PWR_DisableSEVOnPend), (16 bytes).
    Removing stm32l4xx_hal_pwr.o(i.HAL_PWR_DisableSleepOnExit), (16 bytes).
    Removing stm32l4xx_hal_pwr.o(i.HAL_PWR_DisableWakeUpPin), (20 bytes).
    Removing stm32l4xx_hal_pwr.o(i.HAL_PWR_EnableBkUpAccess), (16 bytes).
    Removing stm32l4xx_hal_pwr.o(i.HAL_PWR_EnablePVD), (16 bytes).
    Removing stm32l4xx_hal_pwr.o(i.HAL_PWR_EnableSEVOnPend), (16 bytes).
    Removing stm32l4xx_hal_pwr.o(i.HAL_PWR_EnableSleepOnExit), (16 bytes).
    Removing stm32l4xx_hal_pwr.o(i.HAL_PWR_EnableWakeUpPin), (28 bytes).
    Removing stm32l4xx_hal_pwr.o(i.HAL_PWR_EnterSLEEPMode), (72 bytes).
    Removing stm32l4xx_hal_pwr.o(i.HAL_PWR_EnterSTANDBYMode), (36 bytes).
    Removing stm32l4xx_hal_pwr.o(i.HAL_PWR_EnterSTOPMode), (18 bytes).
    Removing stm32l4xx_hal_pwr.o(i.HAL_PWR_PVDCallback), (2 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_ConfigPVM), (408 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableBatteryCharging), (16 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableGPIOPullDown), (124 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableGPIOPullUp), (116 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableInternalWakeUpLine), (16 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableLowPowerRunMode), (68 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_DisablePVM1), (16 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_DisablePVM2), (16 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_DisablePVM3), (16 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_DisablePVM4), (16 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_DisablePullUpPullDownConfig), (16 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableSRAM2ContentRetention), (6 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableVddIO2), (16 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableVddUSB), (16 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableBatteryCharging), (28 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableGPIOPullDown), (184 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableGPIOPullUp), (184 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableInternalWakeUpLine), (16 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableLowPowerRunMode), (16 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_EnablePVM1), (16 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_EnablePVM2), (16 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_EnablePVM3), (16 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_EnablePVM4), (16 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_EnablePullUpPullDownConfig), (16 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableSRAM2ContentRetention), (8 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableVddIO2), (16 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableVddUSB), (16 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_EnterSHUTDOWNMode), (36 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_EnterSTOP0Mode), (52 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_EnterSTOP1Mode), (56 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_EnterSTOP2Mode), (56 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_PVD_PVM_IRQHandler), (88 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_PVM1Callback), (2 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_PVM2Callback), (2 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_PVM3Callback), (2 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_PVM4Callback), (2 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_SetSRAM2ContentRetention), (44 bytes).
    Removing stm32l4xx_hal_cortex.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal_cortex.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal_cortex.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal_cortex.o(i.HAL_MPU_ConfigRegion), (88 bytes).
    Removing stm32l4xx_hal_cortex.o(i.HAL_MPU_Disable), (16 bytes).
    Removing stm32l4xx_hal_cortex.o(i.HAL_MPU_Enable), (24 bytes).
    Removing stm32l4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ), (34 bytes).
    Removing stm32l4xx_hal_cortex.o(i.HAL_NVIC_GetActive), (40 bytes).
    Removing stm32l4xx_hal_cortex.o(i.HAL_NVIC_GetPendingIRQ), (40 bytes).
    Removing stm32l4xx_hal_cortex.o(i.HAL_NVIC_GetPriority), (86 bytes).
    Removing stm32l4xx_hal_cortex.o(i.HAL_NVIC_GetPriorityGrouping), (16 bytes).
    Removing stm32l4xx_hal_cortex.o(i.HAL_NVIC_SetPendingIRQ), (26 bytes).
    Removing stm32l4xx_hal_cortex.o(i.HAL_NVIC_SystemReset), (36 bytes).
    Removing stm32l4xx_hal_cortex.o(i.HAL_SYSTICK_CLKSourceConfig), (28 bytes).
    Removing stm32l4xx_hal_cortex.o(i.HAL_SYSTICK_Callback), (2 bytes).
    Removing stm32l4xx_hal_cortex.o(i.HAL_SYSTICK_Config), (40 bytes).
    Removing stm32l4xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler), (8 bytes).
    Removing stm32l4xx_hal_exti.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal_exti.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal_exti.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal_exti.o(i.HAL_EXTI_ClearConfigLine), (132 bytes).
    Removing stm32l4xx_hal_exti.o(i.HAL_EXTI_ClearPending), (28 bytes).
    Removing stm32l4xx_hal_exti.o(i.HAL_EXTI_GenerateSWI), (28 bytes).
    Removing stm32l4xx_hal_exti.o(i.HAL_EXTI_GetConfigLine), (168 bytes).
    Removing stm32l4xx_hal_exti.o(i.HAL_EXTI_GetHandle), (14 bytes).
    Removing stm32l4xx_hal_exti.o(i.HAL_EXTI_GetPending), (32 bytes).
    Removing stm32l4xx_hal_exti.o(i.HAL_EXTI_IRQHandler), (44 bytes).
    Removing stm32l4xx_hal_exti.o(i.HAL_EXTI_RegisterCallback), (16 bytes).
    Removing stm32l4xx_hal_exti.o(i.HAL_EXTI_SetConfigLine), (180 bytes).
    Removing stm32l4xx_hal_iwdg.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal_iwdg.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal_iwdg.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal_iwdg.o(i.HAL_IWDG_Init), (102 bytes).
    Removing stm32l4xx_hal_iwdg.o(i.HAL_IWDG_Refresh), (12 bytes).
    Removing stm32l4xx_hal_spi.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal_spi.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal_spi.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal_spi.o(i.HAL_SPI_Abort), (378 bytes).
    Removing stm32l4xx_hal_spi.o(i.HAL_SPI_AbortCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_spi.o(i.HAL_SPI_Abort_IT), (310 bytes).
    Removing stm32l4xx_hal_spi.o(i.HAL_SPI_DMAPause), (38 bytes).
    Removing stm32l4xx_hal_spi.o(i.HAL_SPI_DMAResume), (38 bytes).
    Removing stm32l4xx_hal_spi.o(i.HAL_SPI_DMAStop), (68 bytes).
    Removing stm32l4xx_hal_spi.o(i.HAL_SPI_DeInit), (46 bytes).
    Removing stm32l4xx_hal_spi.o(i.HAL_SPI_ErrorCallback), (2 bytes).
    Removing stm32l4xx_hal_spi.o(i.HAL_SPI_GetError), (4 bytes).
    Removing stm32l4xx_hal_spi.o(i.HAL_SPI_GetState), (6 bytes).
    Removing stm32l4xx_hal_spi.o(i.HAL_SPI_IRQHandler), (260 bytes).
    Removing stm32l4xx_hal_spi.o(i.HAL_SPI_MspDeInit), (2 bytes).
    Removing stm32l4xx_hal_spi.o(i.HAL_SPI_MspInit), (2 bytes).
    Removing stm32l4xx_hal_spi.o(i.HAL_SPI_Receive_DMA), (364 bytes).
    Removing stm32l4xx_hal_spi.o(i.HAL_SPI_Receive_IT), (208 bytes).
    Removing stm32l4xx_hal_spi.o(i.HAL_SPI_RxCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_spi.o(i.HAL_SPI_RxHalfCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA), (480 bytes).
    Removing stm32l4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT), (204 bytes).
    Removing stm32l4xx_hal_spi.o(i.HAL_SPI_Transmit_DMA), (288 bytes).
    Removing stm32l4xx_hal_spi.o(i.HAL_SPI_Transmit_IT), (160 bytes).
    Removing stm32l4xx_hal_spi.o(i.HAL_SPI_TxCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_spi.o(i.HAL_SPI_TxHalfCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_spi.o(i.HAL_SPI_TxRxCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_spi.o(i.HAL_SPI_TxRxHalfCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_spi.o(i.SPI_2linesRxISR_16BIT), (54 bytes).
    Removing stm32l4xx_hal_spi.o(i.SPI_2linesRxISR_8BIT), (100 bytes).
    Removing stm32l4xx_hal_spi.o(i.SPI_2linesTxISR_16BIT), (50 bytes).
    Removing stm32l4xx_hal_spi.o(i.SPI_2linesTxISR_8BIT), (74 bytes).
    Removing stm32l4xx_hal_spi.o(i.SPI_AbortRx_ISR), (132 bytes).
    Removing stm32l4xx_hal_spi.o(i.SPI_AbortTx_ISR), (220 bytes).
    Removing stm32l4xx_hal_spi.o(i.SPI_CloseRxTx_ISR), (88 bytes).
    Removing stm32l4xx_hal_spi.o(i.SPI_CloseRx_ISR), (64 bytes).
    Removing stm32l4xx_hal_spi.o(i.SPI_CloseTx_ISR), (76 bytes).
    Removing stm32l4xx_hal_spi.o(i.SPI_DMAAbortOnError), (18 bytes).
    Removing stm32l4xx_hal_spi.o(i.SPI_DMAError), (34 bytes).
    Removing stm32l4xx_hal_spi.o(i.SPI_DMAHalfReceiveCplt), (10 bytes).
    Removing stm32l4xx_hal_spi.o(i.SPI_DMAHalfTransmitCplt), (10 bytes).
    Removing stm32l4xx_hal_spi.o(i.SPI_DMAHalfTransmitReceiveCplt), (10 bytes).
    Removing stm32l4xx_hal_spi.o(i.SPI_DMAReceiveCplt), (110 bytes).
    Removing stm32l4xx_hal_spi.o(i.SPI_DMARxAbortCallback), (128 bytes).
    Removing stm32l4xx_hal_spi.o(i.SPI_DMATransmitCplt), (100 bytes).
    Removing stm32l4xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt), (92 bytes).
    Removing stm32l4xx_hal_spi.o(i.SPI_DMATxAbortCallback), (124 bytes).
    Removing stm32l4xx_hal_spi.o(i.SPI_RxISR_16BIT), (38 bytes).
    Removing stm32l4xx_hal_spi.o(i.SPI_RxISR_8BIT), (38 bytes).
    Removing stm32l4xx_hal_spi.o(i.SPI_TxISR_16BIT), (32 bytes).
    Removing stm32l4xx_hal_spi.o(i.SPI_TxISR_8BIT), (32 bytes).
    Removing stm32l4xx_hal_spi_ex.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal_spi_ex.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal_spi_ex.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal_spi_ex.o(i.HAL_SPIEx_FlushRxFifo), (36 bytes).
    Removing stm32l4xx_hal_tim.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal_tim.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal_tim.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_Base_DeInit), (76 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_Base_GetState), (6 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_Base_MspDeInit), (2 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_Base_MspInit), (2 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_Base_Start), (128 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA), (212 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_Base_Stop), (38 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_Base_Stop_DMA), (58 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_Base_Stop_IT), (48 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource), (236 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_ConfigOCrefClear), (330 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_ConfigTI1Input), (16 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_DMABurstState), (6 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart), (452 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart), (452 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart), (18 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStop), (134 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart), (18 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStop), (134 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_Encoder_DeInit), (64 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_Encoder_GetState), (6 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_Encoder_Init), (184 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_Encoder_MspDeInit), (2 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_Encoder_MspInit), (2 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_Encoder_Start), (168 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA), (496 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_IT), (208 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop), (190 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA), (254 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_IT), (230 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_ErrorCallback), (2 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_GenerateEvent), (38 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_GetActiveChannel), (4 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_GetChannelState), (56 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_IC_CaptureHalfCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel), (192 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_IC_DeInit), (76 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_IC_GetState), (6 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_IC_Init), (78 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_IC_MspDeInit), (2 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_IC_MspInit), (2 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_IC_Start), (312 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA), (556 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_IC_Start_IT), (380 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_IC_Stop), (134 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA), (228 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_IC_Stop_IT), (206 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel), (110 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_OC_DeInit), (76 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_OC_GetState), (6 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_OC_Init), (78 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_OC_MspDeInit), (2 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_OC_MspInit), (2 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_OC_Start), (284 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA), (576 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_OC_Start_IT), (352 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_OC_Stop), (176 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA), (272 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_OC_Stop_IT), (248 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel), (268 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_OnePulse_DeInit), (64 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_OnePulse_GetState), (6 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_OnePulse_Init), (90 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspDeInit), (2 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspInit), (2 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start), (140 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start_IT), (160 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop), (140 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop_IT), (160 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel), (310 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_DeInit), (76 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_GetState), (6 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_Init), (78 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_MspDeInit), (2 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_MspInit), (2 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedHalfCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_Start), (284 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA), (576 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_Start_IT), (352 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_Stop), (176 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA), (272 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_IT), (248 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedCallback), (2 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedHalfCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_ReadCapturedValue), (44 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro), (80 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro_IT), (80 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_TriggerHalfCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_tim.o(i.TIM_CCxChannelCmd), (26 bytes).
    Removing stm32l4xx_hal_tim.o(i.TIM_DMACaptureCplt), (110 bytes).
    Removing stm32l4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt), (62 bytes).
    Removing stm32l4xx_hal_tim.o(i.TIM_DMADelayPulseCplt), (94 bytes).
    Removing stm32l4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt), (62 bytes).
    Removing stm32l4xx_hal_tim.o(i.TIM_DMAError), (84 bytes).
    Removing stm32l4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt), (24 bytes).
    Removing stm32l4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt), (10 bytes).
    Removing stm32l4xx_hal_tim.o(i.TIM_DMATriggerCplt), (24 bytes).
    Removing stm32l4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt), (10 bytes).
    Removing stm32l4xx_hal_tim.o(i.TIM_ETR_SetConfig), (20 bytes).
    Removing stm32l4xx_hal_tim.o(i.TIM_ITRx_SetConfig), (16 bytes).
    Removing stm32l4xx_hal_tim.o(i.TIM_OC1_SetConfig), (160 bytes).
    Removing stm32l4xx_hal_tim.o(i.TIM_OC2_SetConfig), (148 bytes).
    Removing stm32l4xx_hal_tim.o(i.TIM_OC3_SetConfig), (148 bytes).
    Removing stm32l4xx_hal_tim.o(i.TIM_OC4_SetConfig), (116 bytes).
    Removing stm32l4xx_hal_tim.o(i.TIM_OC5_SetConfig), (112 bytes).
    Removing stm32l4xx_hal_tim.o(i.TIM_OC6_SetConfig), (112 bytes).
    Removing stm32l4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig), (144 bytes).
    Removing stm32l4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage), (34 bytes).
    Removing stm32l4xx_hal_tim.o(i.TIM_TI1_SetConfig), (120 bytes).
    Removing stm32l4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage), (36 bytes).
    Removing stm32l4xx_hal_tim.o(i.TIM_TI2_SetConfig), (54 bytes).
    Removing stm32l4xx_hal_tim.o(i.TIM_TI3_SetConfig), (50 bytes).
    Removing stm32l4xx_hal_tim.o(i.TIM_TI4_SetConfig), (56 bytes).
    Removing stm32l4xx_hal_tim_ex.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal_tim_ex.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal_tim_ex.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutHalfCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigBreakDeadTime), (140 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigBreakInput), (204 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent), (112 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA), (144 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_IT), (112 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_GetChannelNState), (36 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_GroupChannel5), (56 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_DeInit), (64 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_GetState), (6 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init), (216 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspDeInit), (2 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspInit), (2 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start), (172 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA), (240 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_IT), (184 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop), (58 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA), (70 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_IT), (68 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization), (152 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start), (212 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA), (436 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_IT), (272 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop), (106 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA), (180 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_IT), (178 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start), (98 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT), (118 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop), (100 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT), (120 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start), (212 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA), (436 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_IT), (272 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop), (106 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA), (180 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_IT), (178 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_RemapConfig), (92 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt), (16 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt), (16 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd), (26 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt), (94 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN), (62 bytes).
    Removing stm32l4xx_hal_uart.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal_uart.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal_uart.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_HalfDuplex_EnableReceiver), (72 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_HalfDuplex_EnableTransmitter), (72 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_HalfDuplex_Init), (118 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_LIN_Init), (150 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_LIN_SendBreak), (46 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_MultiProcessor_DisableMuteMode), (48 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_MultiProcessor_EnableMuteMode), (48 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_MultiProcessor_EnterMuteMode), (12 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_MultiProcessor_Init), (142 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_UART_Abort), (234 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_UART_AbortReceive), (162 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT), (188 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_UART_AbortTransmit), (98 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT), (120 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_UART_Abort_IT), (268 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_UART_DMAPause), (138 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_UART_DMAResume), (130 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_UART_DMAStop), (150 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_UART_DeInit), (62 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_UART_DisableReceiverTimeout), (68 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_UART_EnableReceiverTimeout), (68 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_UART_ErrorCallback), (2 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_UART_GetError), (6 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_UART_GetState), (10 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_UART_MspDeInit), (2 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_UART_MspInit), (2 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_UART_Receive), (264 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_UART_Receive_DMA), (88 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_UART_Receive_IT), (88 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_UART_ReceiverTimeout_Config), (24 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_UART_RxCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_UART_Transmit), (194 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_UART_Transmit_DMA), (164 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_UART_Transmit_IT), (116 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_UART_TxCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_uart.o(i.UART_DMAError), (76 bytes).
    Removing stm32l4xx_hal_uart.o(i.UART_DMAReceiveCplt), (128 bytes).
    Removing stm32l4xx_hal_uart.o(i.UART_DMARxAbortCallback), (64 bytes).
    Removing stm32l4xx_hal_uart.o(i.UART_DMARxHalfCplt), (28 bytes).
    Removing stm32l4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback), (38 bytes).
    Removing stm32l4xx_hal_uart.o(i.UART_DMATransmitCplt), (66 bytes).
    Removing stm32l4xx_hal_uart.o(i.UART_DMATxAbortCallback), (54 bytes).
    Removing stm32l4xx_hal_uart.o(i.UART_DMATxHalfCplt), (10 bytes).
    Removing stm32l4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback), (20 bytes).
    Removing stm32l4xx_hal_uart.o(i.UART_EndTxTransfer), (24 bytes).
    Removing stm32l4xx_hal_uart.o(i.UART_RxISR_16BIT), (156 bytes).
    Removing stm32l4xx_hal_uart.o(i.UART_RxISR_8BIT), (156 bytes).
    Removing stm32l4xx_hal_uart.o(i.UART_Start_Receive_DMA), (164 bytes).
    Removing stm32l4xx_hal_uart.o(i.UART_Start_Receive_IT), (204 bytes).
    Removing stm32l4xx_hal_uart.o(i.UART_TxISR_16BIT), (80 bytes).
    Removing stm32l4xx_hal_uart.o(i.UART_TxISR_8BIT), (76 bytes).
    Removing stm32l4xx_hal_uart_ex.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal_uart_ex.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal_uart_ex.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal_uart_ex.o(i.HAL_MultiProcessorEx_AddressLength_Set), (48 bytes).
    Removing stm32l4xx_hal_uart_ex.o(i.HAL_RS485Ex_Init), (144 bytes).
    Removing stm32l4xx_hal_uart_ex.o(i.HAL_UARTEx_DisableClockStopMode), (48 bytes).
    Removing stm32l4xx_hal_uart_ex.o(i.HAL_UARTEx_DisableStopMode), (46 bytes).
    Removing stm32l4xx_hal_uart_ex.o(i.HAL_UARTEx_EnableClockStopMode), (48 bytes).
    Removing stm32l4xx_hal_uart_ex.o(i.HAL_UARTEx_EnableStopMode), (46 bytes).
    Removing stm32l4xx_hal_uart_ex.o(i.HAL_UARTEx_ReceiveToIdle), (332 bytes).
    Removing stm32l4xx_hal_uart_ex.o(i.HAL_UARTEx_ReceiveToIdle_DMA), (92 bytes).
    Removing stm32l4xx_hal_uart_ex.o(i.HAL_UARTEx_ReceiveToIdle_IT), (92 bytes).
    Removing stm32l4xx_hal_uart_ex.o(i.HAL_UARTEx_StopModeWakeUpSourceConfig), (126 bytes).
    Removing stm32l4xx_hal_uart_ex.o(i.UARTEx_Wakeup_AddressConfig), (34 bytes).
    Removing system_stm32l4xx.o(.rev16_text), (4 bytes).
    Removing system_stm32l4xx.o(.revsh_text), (4 bytes).
    Removing system_stm32l4xx.o(.rrx_text), (6 bytes).
    Removing system_stm32l4xx.o(i.SystemCoreClockUpdate), (172 bytes).
    Removing event_groups.o(i.prvTestWaitCondition), (24 bytes).
    Removing event_groups.o(i.uxEventGroupGetNumber), (12 bytes).
    Removing event_groups.o(i.vEventGroupClearBitsCallback), (4 bytes).
    Removing event_groups.o(i.vEventGroupDelete), (74 bytes).
    Removing event_groups.o(i.vEventGroupSetBitsCallback), (4 bytes).
    Removing event_groups.o(i.vEventGroupSetNumber), (4 bytes).
    Removing event_groups.o(i.xEventGroupClearBits), (66 bytes).
    Removing event_groups.o(i.xEventGroupClearBitsFromISR), (16 bytes).
    Removing event_groups.o(i.xEventGroupCreate), (28 bytes).
    Removing event_groups.o(i.xEventGroupCreateStatic), (46 bytes).
    Removing event_groups.o(i.xEventGroupGetBitsFromISR), (26 bytes).
    Removing event_groups.o(i.xEventGroupSetBits), (160 bytes).
    Removing event_groups.o(i.xEventGroupSetBitsFromISR), (16 bytes).
    Removing event_groups.o(i.xEventGroupSync), (200 bytes).
    Removing event_groups.o(i.xEventGroupWaitBits), (264 bytes).
    Removing queue.o(i.pcQueueGetName), (40 bytes).
    Removing queue.o(i.prvGetDisinheritPriorityAfterTimeout), (20 bytes).
    Removing queue.o(i.prvInitialiseMutex), (24 bytes).
    Removing queue.o(i.ucQueueGetQueueType), (6 bytes).
    Removing queue.o(i.uxQueueGetQueueNumber), (4 bytes).
    Removing queue.o(i.uxQueueMessagesWaiting), (38 bytes).
    Removing queue.o(i.uxQueueMessagesWaitingFromISR), (24 bytes).
    Removing queue.o(i.uxQueueSpacesAvailable), (42 bytes).
    Removing queue.o(i.vQueueDelete), (50 bytes).
    Removing queue.o(i.vQueueSetQueueNumber), (4 bytes).
    Removing queue.o(i.vQueueUnregisterQueue), (40 bytes).
    Removing queue.o(i.xQueueCreateCountingSemaphore), (60 bytes).
    Removing queue.o(i.xQueueCreateCountingSemaphoreStatic), (66 bytes).
    Removing queue.o(i.xQueueCreateMutex), (24 bytes).
    Removing queue.o(i.xQueueCreateMutexStatic), (28 bytes).
    Removing queue.o(i.xQueueGenericCreate), (68 bytes).
    Removing queue.o(i.xQueueGetMutexHolder), (26 bytes).
    Removing queue.o(i.xQueueGetMutexHolderFromISR), (32 bytes).
    Removing queue.o(i.xQueueGiveFromISR), (158 bytes).
    Removing queue.o(i.xQueueGiveMutexRecursive), (64 bytes).
    Removing queue.o(i.xQueueIsQueueEmptyFromISR), (32 bytes).
    Removing queue.o(i.xQueueIsQueueFullFromISR), (36 bytes).
    Removing queue.o(i.xQueuePeek), (316 bytes).
    Removing queue.o(i.xQueuePeekFromISR), (124 bytes).
    Removing queue.o(i.xQueueReceiveFromISR), (156 bytes).
    Removing queue.o(i.xQueueSemaphoreTake), (380 bytes).
    Removing queue.o(i.xQueueTakeMutexRecursive), (66 bytes).
    Removing stream_buffer.o(i.prvBytesInBuffer), (18 bytes).
    Removing stream_buffer.o(i.prvInitialiseNewStreamBuffer), (68 bytes).
    Removing stream_buffer.o(i.prvReadBytesFromBuffer), (142 bytes).
    Removing stream_buffer.o(i.prvReadMessageFromBuffer), (66 bytes).
    Removing stream_buffer.o(i.prvWriteBytesToBuffer), (132 bytes).
    Removing stream_buffer.o(i.prvWriteMessageToBuffer), (76 bytes).
    Removing stream_buffer.o(i.ucStreamBufferGetStreamBufferType), (8 bytes).
    Removing stream_buffer.o(i.uxStreamBufferGetStreamBufferNumber), (4 bytes).
    Removing stream_buffer.o(i.vStreamBufferDelete), (36 bytes).
    Removing stream_buffer.o(i.vStreamBufferSetStreamBufferNumber), (4 bytes).
    Removing stream_buffer.o(i.xStreamBufferBytesAvailable), (24 bytes).
    Removing stream_buffer.o(i.xStreamBufferGenericCreate), (110 bytes).
    Removing stream_buffer.o(i.xStreamBufferGenericCreateStatic), (130 bytes).
    Removing stream_buffer.o(i.xStreamBufferIsEmpty), (36 bytes).
    Removing stream_buffer.o(i.xStreamBufferIsFull), (50 bytes).
    Removing stream_buffer.o(i.xStreamBufferNextMessageLengthBytes), (86 bytes).
    Removing stream_buffer.o(i.xStreamBufferReceive), (230 bytes).
    Removing stream_buffer.o(i.xStreamBufferReceiveCompletedFromISR), (76 bytes).
    Removing stream_buffer.o(i.xStreamBufferReceiveFromISR), (146 bytes).
    Removing stream_buffer.o(i.xStreamBufferReset), (70 bytes).
    Removing stream_buffer.o(i.xStreamBufferSend), (260 bytes).
    Removing stream_buffer.o(i.xStreamBufferSendCompletedFromISR), (76 bytes).
    Removing stream_buffer.o(i.xStreamBufferSendFromISR), (152 bytes).
    Removing stream_buffer.o(i.xStreamBufferSetTriggerLevel), (42 bytes).
    Removing stream_buffer.o(i.xStreamBufferSpacesAvailable), (40 bytes).
    Removing tasks.o(i.eTaskGetState), (124 bytes).
    Removing tasks.o(i.pcTaskGetName), (36 bytes).
    Removing tasks.o(i.prvListTasksWithinSingleList), (92 bytes).
    Removing tasks.o(i.prvTaskCheckFreeStackSpace), (20 bytes).
    Removing tasks.o(i.prvTaskIsTaskSuspended), (56 bytes).
    Removing tasks.o(i.pvTaskIncrementMutexHeldCount), (24 bytes).
    Removing tasks.o(i.ulTaskNotifyTake), (104 bytes).
    Removing tasks.o(i.ulTaskNotifyValueClear), (44 bytes).
    Removing tasks.o(i.uxTaskGetNumberOfTasks), (12 bytes).
    Removing tasks.o(i.uxTaskGetStackHighWaterMark), (20 bytes).
    Removing tasks.o(i.uxTaskGetSystemState), (172 bytes).
    Removing tasks.o(i.uxTaskGetTaskNumber), (12 bytes).
    Removing tasks.o(i.uxTaskPriorityGet), (28 bytes).
    Removing tasks.o(i.uxTaskPriorityGetFromISR), (44 bytes).
    Removing tasks.o(i.uxTaskResetEventItemValue), (24 bytes).
    Removing tasks.o(i.vTaskDelayUntil), (152 bytes).
    Removing tasks.o(i.vTaskDelete), (144 bytes).
    Removing tasks.o(i.vTaskEndScheduler), (28 bytes).
    Removing tasks.o(i.vTaskGetInfo), (116 bytes).
    Removing tasks.o(i.vTaskNotifyGiveFromISR), (180 bytes).
    Removing tasks.o(i.vTaskPlaceOnUnorderedEventList), (80 bytes).
    Removing tasks.o(i.vTaskPriorityDisinheritAfterTimeout), (148 bytes).
    Removing tasks.o(i.vTaskPrioritySet), (184 bytes).
    Removing tasks.o(i.vTaskRemoveFromUnorderedEventList), (112 bytes).
    Removing tasks.o(i.vTaskResume), (124 bytes).
    Removing tasks.o(i.vTaskSetTaskNumber), (8 bytes).
    Removing tasks.o(i.vTaskSetTimeOutState), (52 bytes).
    Removing tasks.o(i.vTaskSuspend), (156 bytes).
    Removing tasks.o(i.xTaskCatchUpTicks), (52 bytes).
    Removing tasks.o(i.xTaskGenericNotify), (232 bytes).
    Removing tasks.o(i.xTaskGenericNotifyFromISR), (272 bytes).
    Removing tasks.o(i.xTaskGetCurrentTaskHandle), (12 bytes).
    Removing tasks.o(i.xTaskGetTickCountFromISR), (16 bytes).
    Removing tasks.o(i.xTaskNotifyStateClear), (52 bytes).
    Removing tasks.o(i.xTaskNotifyWait), (144 bytes).
    Removing tasks.o(i.xTaskPriorityInherit), (124 bytes).
    Removing tasks.o(i.xTaskResumeFromISR), (144 bytes).
    Removing timers.o(i.pcTimerGetName), (24 bytes).
    Removing timers.o(i.prvInitialiseNewTimer), (80 bytes).
    Removing timers.o(i.pvTimerGetTimerID), (38 bytes).
    Removing timers.o(i.uxTimerGetReloadMode), (50 bytes).
    Removing timers.o(i.uxTimerGetTimerNumber), (4 bytes).
    Removing timers.o(i.vTimerSetReloadMode), (62 bytes).
    Removing timers.o(i.vTimerSetTimerID), (40 bytes).
    Removing timers.o(i.vTimerSetTimerNumber), (4 bytes).
    Removing timers.o(i.xTimerCreate), (58 bytes).
    Removing timers.o(i.xTimerCreateStatic), (52 bytes).
    Removing timers.o(i.xTimerGetExpiryTime), (24 bytes).
    Removing timers.o(i.xTimerGetPeriod), (24 bytes).
    Removing timers.o(i.xTimerGetTimerDaemonTaskHandle), (32 bytes).
    Removing timers.o(i.xTimerIsTimerActive), (50 bytes).
    Removing timers.o(i.xTimerPendFunctionCall), (60 bytes).
    Removing timers.o(i.xTimerPendFunctionCallFromISR), (40 bytes).
    Removing cmsis_os2.o(.rev16_text), (4 bytes).
    Removing cmsis_os2.o(.revsh_text), (4 bytes).
    Removing cmsis_os2.o(.rrx_text), (6 bytes).
    Removing cmsis_os2.o(i.AllocBlock), (18 bytes).
    Removing cmsis_os2.o(i.CreateBlock), (26 bytes).
    Removing cmsis_os2.o(i.FreeBlock), (8 bytes).
    Removing cmsis_os2.o(i.OS_Tick_GetCount), (12 bytes).
    Removing cmsis_os2.o(i.OS_Tick_GetInterval), (10 bytes).
    Removing cmsis_os2.o(i.OS_Tick_GetOverflow), (12 bytes).
    Removing cmsis_os2.o(i.TimerCallback), (22 bytes).
    Removing cmsis_os2.o(i.osDelayUntil), (46 bytes).
    Removing cmsis_os2.o(i.osEventFlagsClear), (66 bytes).
    Removing cmsis_os2.o(i.osEventFlagsDelete), (34 bytes).
    Removing cmsis_os2.o(i.osEventFlagsGet), (24 bytes).
    Removing cmsis_os2.o(i.osEventFlagsNew), (72 bytes).
    Removing cmsis_os2.o(i.osEventFlagsSet), (84 bytes).
    Removing cmsis_os2.o(i.osEventFlagsWait), (104 bytes).
    Removing cmsis_os2.o(i.osKernelGetInfo), (60 bytes).
    Removing cmsis_os2.o(i.osKernelGetState), (40 bytes).
    Removing cmsis_os2.o(i.osKernelGetSysTimerCount), (66 bytes).
    Removing cmsis_os2.o(i.osKernelGetSysTimerFreq), (12 bytes).
    Removing cmsis_os2.o(i.osKernelGetTickCount), (16 bytes).
    Removing cmsis_os2.o(i.osKernelGetTickFreq), (6 bytes).
    Removing cmsis_os2.o(i.osKernelLock), (44 bytes).
    Removing cmsis_os2.o(i.osKernelRestoreLock), (72 bytes).
    Removing cmsis_os2.o(i.osKernelUnlock), (62 bytes).
    Removing cmsis_os2.o(i.osMemoryPoolAlloc), (152 bytes).
    Removing cmsis_os2.o(i.osMemoryPoolDelete), (94 bytes).
    Removing cmsis_os2.o(i.osMemoryPoolFree), (196 bytes).
    Removing cmsis_os2.o(i.osMemoryPoolGetBlockSize), (28 bytes).
    Removing cmsis_os2.o(i.osMemoryPoolGetCapacity), (28 bytes).
    Removing cmsis_os2.o(i.osMemoryPoolGetCount), (56 bytes).
    Removing cmsis_os2.o(i.osMemoryPoolGetName), (24 bytes).
    Removing cmsis_os2.o(i.osMemoryPoolGetSpace), (44 bytes).
    Removing cmsis_os2.o(i.osMemoryPoolNew), (258 bytes).
    Removing cmsis_os2.o(i.osMessageQueueDelete), (42 bytes).
    Removing cmsis_os2.o(i.osMessageQueueGet), (108 bytes).
    Removing cmsis_os2.o(i.osMessageQueueGetCapacity), (12 bytes).
    Removing cmsis_os2.o(i.osMessageQueueGetCount), (22 bytes).
    Removing cmsis_os2.o(i.osMessageQueueGetMsgSize), (12 bytes).
    Removing cmsis_os2.o(i.osMessageQueueGetSpace), (48 bytes).
    Removing cmsis_os2.o(i.osMessageQueueNew), (124 bytes).
    Removing cmsis_os2.o(i.osMessageQueuePut), (112 bytes).
    Removing cmsis_os2.o(i.osMessageQueueReset), (36 bytes).
    Removing cmsis_os2.o(i.osMutexAcquire), (92 bytes).
    Removing cmsis_os2.o(i.osMutexDelete), (46 bytes).
    Removing cmsis_os2.o(i.osMutexGetOwner), (22 bytes).
    Removing cmsis_os2.o(i.osMutexNew), (152 bytes).
    Removing cmsis_os2.o(i.osMutexRelease), (72 bytes).
    Removing cmsis_os2.o(i.osSemaphoreAcquire), (100 bytes).
    Removing cmsis_os2.o(i.osSemaphoreDelete), (42 bytes).
    Removing cmsis_os2.o(i.osSemaphoreGetCount), (22 bytes).
    Removing cmsis_os2.o(i.osSemaphoreNew), (178 bytes).
    Removing cmsis_os2.o(i.osSemaphoreRelease), (88 bytes).
    Removing cmsis_os2.o(i.osThreadEnumerate), (98 bytes).
    Removing cmsis_os2.o(i.osThreadExit), (8 bytes).
    Removing cmsis_os2.o(i.osThreadFlagsClear), (84 bytes).
    Removing cmsis_os2.o(i.osThreadFlagsGet), (46 bytes).
    Removing cmsis_os2.o(i.osThreadFlagsSet), (116 bytes).
    Removing cmsis_os2.o(i.osThreadFlagsWait), (162 bytes).
    Removing cmsis_os2.o(i.osThreadGetCount), (16 bytes).
    Removing cmsis_os2.o(i.osThreadGetId), (4 bytes).
    Removing cmsis_os2.o(i.osThreadGetName), (18 bytes).
    Removing cmsis_os2.o(i.osThreadGetPriority), (20 bytes).
    Removing cmsis_os2.o(i.osThreadGetStackSpace), (24 bytes).
    Removing cmsis_os2.o(i.osThreadGetState), (56 bytes).
    Removing cmsis_os2.o(i.osThreadResume), (34 bytes).
    Removing cmsis_os2.o(i.osThreadSetPriority), (42 bytes).
    Removing cmsis_os2.o(i.osThreadSuspend), (34 bytes).
    Removing cmsis_os2.o(i.osThreadTerminate), (52 bytes).
    Removing cmsis_os2.o(i.osThreadYield), (40 bytes).
    Removing cmsis_os2.o(i.osTimerDelete), (68 bytes).
    Removing cmsis_os2.o(i.osTimerGetName), (18 bytes).
    Removing cmsis_os2.o(i.osTimerIsRunning), (18 bytes).
    Removing cmsis_os2.o(i.osTimerNew), (162 bytes).
    Removing cmsis_os2.o(i.osTimerStart), (50 bytes).
    Removing cmsis_os2.o(i.osTimerStop), (68 bytes).
    Removing heap_4.o(i.vPortGetHeapStats), (108 bytes).
    Removing heap_4.o(i.vPortInitialiseBlocks), (2 bytes).
    Removing heap_4.o(i.xPortGetFreeHeapSize), (12 bytes).
    Removing heap_4.o(i.xPortGetMinimumEverFreeHeapSize), (12 bytes).
    Removing port.o(i.vPortEndScheduler), (32 bytes).
    Removing modbus_rtu_slave.o(.rev16_text), (4 bytes).
    Removing modbus_rtu_slave.o(.revsh_text), (4 bytes).
    Removing modbus_rtu_slave.o(.rrx_text), (6 bytes).
    Removing modbus_rtu_slave.o(i.Modbus_CRC16), (40 bytes).
    Removing modbus_rtu_slave.o(i.Modbus_Init), (20 bytes).
    Removing modbus_rtu_slave.o(i.Modbus_Process_Request), (152 bytes).
    Removing modbus_rtu_slave.o(i.Modbus_RTU_Process), (68 bytes).
    Removing modbus_rtu_slave.o(.bss), (12 bytes).
    Removing modbus_rtu_slave.o(.constdata), (512 bytes).
    Removing modbus_test.o(.rev16_text), (4 bytes).
    Removing modbus_test.o(.revsh_text), (4 bytes).
    Removing modbus_test.o(.rrx_text), (6 bytes).
    Removing modbus_test.o(i.Modbus_Test_Communication), (32 bytes).
    Removing modbus_test.o(i.Modbus_Test_Get_Stats), (28 bytes).
    Removing modbus_test.o(i.Modbus_Test_Inc_Error_Count), (16 bytes).
    Removing modbus_test.o(i.Modbus_Test_Print_Stats), (2 bytes).
    Removing rs485.o(.rev16_text), (4 bytes).
    Removing rs485.o(.revsh_text), (4 bytes).
    Removing rs485.o(.rrx_text), (6 bytes).
    Removing rs485.o(i.RS485_Get_Line_Idle_Time), (20 bytes).
    Removing rs485.o(i.UART_DMA_Init), (36 bytes).
    Removing rs485.o(i.UART_Get_Rx_Buffer), (8 bytes).
    Removing rs485.o(i.UART_Get_Rx_Length), (12 bytes).
    Removing rs485.o(i.UART_Start_DMA_Receive), (44 bytes).
    Removing rs485.o(.bss), (256 bytes).
    Removing rs485.o(.data), (8 bytes).
    Removing rn7326.o(.rev16_text), (4 bytes).
    Removing rn7326.o(.revsh_text), (4 bytes).
    Removing rn7326.o(.rrx_text), (6 bytes).
    Removing rn7326.o(i.RN7326_WriteReg), (156 bytes).
    Removing mbascii.o(.rev16_text), (4 bytes).
    Removing mbascii.o(.revsh_text), (4 bytes).
    Removing mbascii.o(.rrx_text), (6 bytes).
    Removing mbfunccoils.o(.rev16_text), (4 bytes).
    Removing mbfunccoils.o(.revsh_text), (4 bytes).
    Removing mbfunccoils.o(.rrx_text), (6 bytes).
    Removing mbfuncdisc.o(.rev16_text), (4 bytes).
    Removing mbfuncdisc.o(.revsh_text), (4 bytes).
    Removing mbfuncdisc.o(.rrx_text), (6 bytes).
    Removing mbfuncfile.o(.rev16_text), (4 bytes).
    Removing mbfuncfile.o(.revsh_text), (4 bytes).
    Removing mbfuncfile.o(.rrx_text), (6 bytes).
    Removing mbfuncholding.o(.rev16_text), (4 bytes).
    Removing mbfuncholding.o(.revsh_text), (4 bytes).
    Removing mbfuncholding.o(.rrx_text), (6 bytes).
    Removing mbfuncinput.o(.rev16_text), (4 bytes).
    Removing mbfuncinput.o(.revsh_text), (4 bytes).
    Removing mbfuncinput.o(.rrx_text), (6 bytes).
    Removing mbfuncother.o(.rev16_text), (4 bytes).
    Removing mbfuncother.o(.revsh_text), (4 bytes).
    Removing mbfuncother.o(.rrx_text), (6 bytes).
    Removing mbfuncother.o(i.eMBSetSlaveID), (84 bytes).
    Removing mbutils.o(.rev16_text), (4 bytes).
    Removing mbutils.o(.revsh_text), (4 bytes).
    Removing mbutils.o(.rrx_text), (6 bytes).
    Removing mbcrc.o(.rev16_text), (4 bytes).
    Removing mbcrc.o(.revsh_text), (4 bytes).
    Removing mbcrc.o(.rrx_text), (6 bytes).
    Removing mbrtu.o(.rev16_text), (4 bytes).
    Removing mbrtu.o(.revsh_text), (4 bytes).
    Removing mbrtu.o(.rrx_text), (6 bytes).
    Removing mbtcp.o(.rev16_text), (4 bytes).
    Removing mbtcp.o(.revsh_text), (4 bytes).
    Removing mbtcp.o(.rrx_text), (6 bytes).
    Removing mb.o(.rev16_text), (4 bytes).
    Removing mb.o(.revsh_text), (4 bytes).
    Removing mb.o(.rrx_text), (6 bytes).
    Removing mb.o(i.eMBClose), (32 bytes).
    Removing mb.o(i.eMBDisable), (40 bytes).
    Removing mb.o(i.eMBRegisterCB), (104 bytes).
    Removing mb.o(.data), (4 bytes).
    Removing mb.o(.data), (4 bytes).
    Removing mdcb.o(.rev16_text), (4 bytes).
    Removing mdcb.o(.revsh_text), (4 bytes).
    Removing mdcb.o(.rrx_text), (6 bytes).
    Removing portevent.o(.rev16_text), (4 bytes).
    Removing portevent.o(.revsh_text), (4 bytes).
    Removing portevent.o(.rrx_text), (6 bytes).
    Removing portserial.o(.rev16_text), (4 bytes).
    Removing portserial.o(.revsh_text), (4 bytes).
    Removing portserial.o(.rrx_text), (6 bytes).
    Removing portserial.o(i.HAL_UART_RxCpltCallback), (20 bytes).
    Removing porttimer.o(.rev16_text), (4 bytes).
    Removing porttimer.o(.revsh_text), (4 bytes).
    Removing porttimer.o(.rrx_text), (6 bytes).

970 unused section(s) (total 73224 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../Core/Src/can.c                        0x00000000   Number         0  can.o ABSOLUTE
    ../Core/Src/freertos.c                   0x00000000   Number         0  freertos.o ABSOLUTE
    ../Core/Src/gpio.c                       0x00000000   Number         0  gpio.o ABSOLUTE
    ../Core/Src/i2c.c                        0x00000000   Number         0  i2c.o ABSOLUTE
    ../Core/Src/iwdg.c                       0x00000000   Number         0  iwdg.o ABSOLUTE
    ../Core/Src/main.c                       0x00000000   Number         0  main.o ABSOLUTE
    ../Core/Src/spi.c                        0x00000000   Number         0  spi.o ABSOLUTE
    ../Core/Src/stm32l4xx_hal_msp.c          0x00000000   Number         0  stm32l4xx_hal_msp.o ABSOLUTE
    ../Core/Src/stm32l4xx_hal_timebase_tim.c 0x00000000   Number         0  stm32l4xx_hal_timebase_tim.o ABSOLUTE
    ../Core/Src/stm32l4xx_it.c               0x00000000   Number         0  stm32l4xx_it.o ABSOLUTE
    ../Core/Src/system_stm32l4xx.c           0x00000000   Number         0  system_stm32l4xx.o ABSOLUTE
    ../Core/Src/tim.c                        0x00000000   Number         0  tim.o ABSOLUTE
    ../Core/Src/usart.c                      0x00000000   Number         0  usart.o ABSOLUTE
    ../Drivers/STM32L4xx_HAL_Driver/Src/stm32l4xx_hal.c 0x00000000   Number         0  stm32l4xx_hal.o ABSOLUTE
    ../Drivers/STM32L4xx_HAL_Driver/Src/stm32l4xx_hal_can.c 0x00000000   Number         0  stm32l4xx_hal_can.o ABSOLUTE
    ../Drivers/STM32L4xx_HAL_Driver/Src/stm32l4xx_hal_cortex.c 0x00000000   Number         0  stm32l4xx_hal_cortex.o ABSOLUTE
    ../Drivers/STM32L4xx_HAL_Driver/Src/stm32l4xx_hal_dma.c 0x00000000   Number         0  stm32l4xx_hal_dma.o ABSOLUTE
    ../Drivers/STM32L4xx_HAL_Driver/Src/stm32l4xx_hal_dma_ex.c 0x00000000   Number         0  stm32l4xx_hal_dma_ex.o ABSOLUTE
    ../Drivers/STM32L4xx_HAL_Driver/Src/stm32l4xx_hal_exti.c 0x00000000   Number         0  stm32l4xx_hal_exti.o ABSOLUTE
    ../Drivers/STM32L4xx_HAL_Driver/Src/stm32l4xx_hal_flash.c 0x00000000   Number         0  stm32l4xx_hal_flash.o ABSOLUTE
    ../Drivers/STM32L4xx_HAL_Driver/Src/stm32l4xx_hal_flash_ex.c 0x00000000   Number         0  stm32l4xx_hal_flash_ex.o ABSOLUTE
    ../Drivers/STM32L4xx_HAL_Driver/Src/stm32l4xx_hal_flash_ramfunc.c 0x00000000   Number         0  stm32l4xx_hal_flash_ramfunc.o ABSOLUTE
    ../Drivers/STM32L4xx_HAL_Driver/Src/stm32l4xx_hal_gpio.c 0x00000000   Number         0  stm32l4xx_hal_gpio.o ABSOLUTE
    ../Drivers/STM32L4xx_HAL_Driver/Src/stm32l4xx_hal_i2c.c 0x00000000   Number         0  stm32l4xx_hal_i2c.o ABSOLUTE
    ../Drivers/STM32L4xx_HAL_Driver/Src/stm32l4xx_hal_i2c_ex.c 0x00000000   Number         0  stm32l4xx_hal_i2c_ex.o ABSOLUTE
    ../Drivers/STM32L4xx_HAL_Driver/Src/stm32l4xx_hal_iwdg.c 0x00000000   Number         0  stm32l4xx_hal_iwdg.o ABSOLUTE
    ../Drivers/STM32L4xx_HAL_Driver/Src/stm32l4xx_hal_pwr.c 0x00000000   Number         0  stm32l4xx_hal_pwr.o ABSOLUTE
    ../Drivers/STM32L4xx_HAL_Driver/Src/stm32l4xx_hal_pwr_ex.c 0x00000000   Number         0  stm32l4xx_hal_pwr_ex.o ABSOLUTE
    ../Drivers/STM32L4xx_HAL_Driver/Src/stm32l4xx_hal_rcc.c 0x00000000   Number         0  stm32l4xx_hal_rcc.o ABSOLUTE
    ../Drivers/STM32L4xx_HAL_Driver/Src/stm32l4xx_hal_rcc_ex.c 0x00000000   Number         0  stm32l4xx_hal_rcc_ex.o ABSOLUTE
    ../Drivers/STM32L4xx_HAL_Driver/Src/stm32l4xx_hal_spi.c 0x00000000   Number         0  stm32l4xx_hal_spi.o ABSOLUTE
    ../Drivers/STM32L4xx_HAL_Driver/Src/stm32l4xx_hal_spi_ex.c 0x00000000   Number         0  stm32l4xx_hal_spi_ex.o ABSOLUTE
    ../Drivers/STM32L4xx_HAL_Driver/Src/stm32l4xx_hal_tim.c 0x00000000   Number         0  stm32l4xx_hal_tim.o ABSOLUTE
    ../Drivers/STM32L4xx_HAL_Driver/Src/stm32l4xx_hal_tim_ex.c 0x00000000   Number         0  stm32l4xx_hal_tim_ex.o ABSOLUTE
    ../Drivers/STM32L4xx_HAL_Driver/Src/stm32l4xx_hal_uart.c 0x00000000   Number         0  stm32l4xx_hal_uart.o ABSOLUTE
    ../Drivers/STM32L4xx_HAL_Driver/Src/stm32l4xx_hal_uart_ex.c 0x00000000   Number         0  stm32l4xx_hal_uart_ex.o ABSOLUTE
    ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c 0x00000000   Number         0  cmsis_os2.o ABSOLUTE
    ../Middlewares/Third_Party/FreeRTOS/Source/croutine.c 0x00000000   Number         0  croutine.o ABSOLUTE
    ../Middlewares/Third_Party/FreeRTOS/Source/event_groups.c 0x00000000   Number         0  event_groups.o ABSOLUTE
    ../Middlewares/Third_Party/FreeRTOS/Source/list.c 0x00000000   Number         0  list.o ABSOLUTE
    ../Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c 0x00000000   Number         0  heap_4.o ABSOLUTE
    ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F/port.c 0x00000000   Number         0  port.o ABSOLUTE
    ../Middlewares/Third_Party/FreeRTOS/Source/queue.c 0x00000000   Number         0  queue.o ABSOLUTE
    ../Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c 0x00000000   Number         0  stream_buffer.o ABSOLUTE
    ../Middlewares/Third_Party/FreeRTOS/Source/tasks.c 0x00000000   Number         0  tasks.o ABSOLUTE
    ../Middlewares/Third_Party/FreeRTOS/Source/timers.c 0x00000000   Number         0  timers.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/dczerorl2.s                0x00000000   Number         0  __dczerorl2.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_ldiv0.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_ldiv0_sigfpe.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_div0.o ABSOLUTE
    ../clib/angel/scatter.s                  0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_exit.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  lludivv7m.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memcpy_w.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memclr_w.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memcpy_v6.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  memset.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  aeabi_memset.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memclr.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_outer.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../fplib/fpinit.s                        0x00000000   Number         0  fpinit.o ABSOLUTE
    ..\Core\Src\can.c                        0x00000000   Number         0  can.o ABSOLUTE
    ..\Core\Src\freertos.c                   0x00000000   Number         0  freertos.o ABSOLUTE
    ..\Core\Src\gpio.c                       0x00000000   Number         0  gpio.o ABSOLUTE
    ..\Core\Src\i2c.c                        0x00000000   Number         0  i2c.o ABSOLUTE
    ..\Core\Src\iwdg.c                       0x00000000   Number         0  iwdg.o ABSOLUTE
    ..\Core\Src\main.c                       0x00000000   Number         0  main.o ABSOLUTE
    ..\Core\Src\spi.c                        0x00000000   Number         0  spi.o ABSOLUTE
    ..\Core\Src\stm32l4xx_hal_msp.c          0x00000000   Number         0  stm32l4xx_hal_msp.o ABSOLUTE
    ..\Core\Src\stm32l4xx_hal_timebase_tim.c 0x00000000   Number         0  stm32l4xx_hal_timebase_tim.o ABSOLUTE
    ..\Core\Src\stm32l4xx_it.c               0x00000000   Number         0  stm32l4xx_it.o ABSOLUTE
    ..\Core\Src\system_stm32l4xx.c           0x00000000   Number         0  system_stm32l4xx.o ABSOLUTE
    ..\Core\Src\tim.c                        0x00000000   Number         0  tim.o ABSOLUTE
    ..\Core\Src\usart.c                      0x00000000   Number         0  usart.o ABSOLUTE
    ..\Drivers\STM32L4xx_HAL_Driver\Src\stm32l4xx_hal.c 0x00000000   Number         0  stm32l4xx_hal.o ABSOLUTE
    ..\Drivers\STM32L4xx_HAL_Driver\Src\stm32l4xx_hal_can.c 0x00000000   Number         0  stm32l4xx_hal_can.o ABSOLUTE
    ..\Drivers\STM32L4xx_HAL_Driver\Src\stm32l4xx_hal_cortex.c 0x00000000   Number         0  stm32l4xx_hal_cortex.o ABSOLUTE
    ..\Drivers\STM32L4xx_HAL_Driver\Src\stm32l4xx_hal_dma.c 0x00000000   Number         0  stm32l4xx_hal_dma.o ABSOLUTE
    ..\Drivers\STM32L4xx_HAL_Driver\Src\stm32l4xx_hal_dma_ex.c 0x00000000   Number         0  stm32l4xx_hal_dma_ex.o ABSOLUTE
    ..\Drivers\STM32L4xx_HAL_Driver\Src\stm32l4xx_hal_exti.c 0x00000000   Number         0  stm32l4xx_hal_exti.o ABSOLUTE
    ..\Drivers\STM32L4xx_HAL_Driver\Src\stm32l4xx_hal_flash.c 0x00000000   Number         0  stm32l4xx_hal_flash.o ABSOLUTE
    ..\Drivers\STM32L4xx_HAL_Driver\Src\stm32l4xx_hal_flash_ex.c 0x00000000   Number         0  stm32l4xx_hal_flash_ex.o ABSOLUTE
    ..\Drivers\STM32L4xx_HAL_Driver\Src\stm32l4xx_hal_flash_ramfunc.c 0x00000000   Number         0  stm32l4xx_hal_flash_ramfunc.o ABSOLUTE
    ..\Drivers\STM32L4xx_HAL_Driver\Src\stm32l4xx_hal_gpio.c 0x00000000   Number         0  stm32l4xx_hal_gpio.o ABSOLUTE
    ..\Drivers\STM32L4xx_HAL_Driver\Src\stm32l4xx_hal_i2c.c 0x00000000   Number         0  stm32l4xx_hal_i2c.o ABSOLUTE
    ..\Drivers\STM32L4xx_HAL_Driver\Src\stm32l4xx_hal_i2c_ex.c 0x00000000   Number         0  stm32l4xx_hal_i2c_ex.o ABSOLUTE
    ..\Drivers\STM32L4xx_HAL_Driver\Src\stm32l4xx_hal_iwdg.c 0x00000000   Number         0  stm32l4xx_hal_iwdg.o ABSOLUTE
    ..\Drivers\STM32L4xx_HAL_Driver\Src\stm32l4xx_hal_pwr.c 0x00000000   Number         0  stm32l4xx_hal_pwr.o ABSOLUTE
    ..\Drivers\STM32L4xx_HAL_Driver\Src\stm32l4xx_hal_pwr_ex.c 0x00000000   Number         0  stm32l4xx_hal_pwr_ex.o ABSOLUTE
    ..\Drivers\STM32L4xx_HAL_Driver\Src\stm32l4xx_hal_rcc.c 0x00000000   Number         0  stm32l4xx_hal_rcc.o ABSOLUTE
    ..\Drivers\STM32L4xx_HAL_Driver\Src\stm32l4xx_hal_rcc_ex.c 0x00000000   Number         0  stm32l4xx_hal_rcc_ex.o ABSOLUTE
    ..\Drivers\STM32L4xx_HAL_Driver\Src\stm32l4xx_hal_spi.c 0x00000000   Number         0  stm32l4xx_hal_spi.o ABSOLUTE
    ..\Drivers\STM32L4xx_HAL_Driver\Src\stm32l4xx_hal_spi_ex.c 0x00000000   Number         0  stm32l4xx_hal_spi_ex.o ABSOLUTE
    ..\Drivers\STM32L4xx_HAL_Driver\Src\stm32l4xx_hal_tim.c 0x00000000   Number         0  stm32l4xx_hal_tim.o ABSOLUTE
    ..\Drivers\STM32L4xx_HAL_Driver\Src\stm32l4xx_hal_tim_ex.c 0x00000000   Number         0  stm32l4xx_hal_tim_ex.o ABSOLUTE
    ..\Drivers\STM32L4xx_HAL_Driver\Src\stm32l4xx_hal_uart.c 0x00000000   Number         0  stm32l4xx_hal_uart.o ABSOLUTE
    ..\Drivers\STM32L4xx_HAL_Driver\Src\stm32l4xx_hal_uart_ex.c 0x00000000   Number         0  stm32l4xx_hal_uart_ex.o ABSOLUTE
    ..\Middlewares\Third_Party\FreeRTOS\Source\CMSIS_RTOS_V2\cmsis_os2.c 0x00000000   Number         0  cmsis_os2.o ABSOLUTE
    ..\Middlewares\Third_Party\FreeRTOS\Source\portable\RVDS\ARM_CM4F\port.c 0x00000000   Number         0  port.o ABSOLUTE
    ..\UserCode\App\Source\modbus_rtu_slave.c 0x00000000   Number         0  modbus_rtu_slave.o ABSOLUTE
    ..\UserCode\App\Source\modbus_test.c     0x00000000   Number         0  modbus_test.o ABSOLUTE
    ..\UserCode\Driver\Source\rs485.c        0x00000000   Number         0  rs485.o ABSOLUTE
    ..\UserCode\FreeModbus\modbus\ascii\mbascii.c 0x00000000   Number         0  mbascii.o ABSOLUTE
    ..\UserCode\FreeModbus\modbus\functions\mbfunccoils.c 0x00000000   Number         0  mbfunccoils.o ABSOLUTE
    ..\UserCode\FreeModbus\modbus\functions\mbfuncdiag.c 0x00000000   Number         0  mbfuncdiag.o ABSOLUTE
    ..\UserCode\FreeModbus\modbus\functions\mbfuncdisc.c 0x00000000   Number         0  mbfuncdisc.o ABSOLUTE
    ..\UserCode\FreeModbus\modbus\functions\mbfuncfile.c 0x00000000   Number         0  mbfuncfile.o ABSOLUTE
    ..\UserCode\FreeModbus\modbus\functions\mbfuncholding.c 0x00000000   Number         0  mbfuncholding.o ABSOLUTE
    ..\UserCode\FreeModbus\modbus\functions\mbfuncinput.c 0x00000000   Number         0  mbfuncinput.o ABSOLUTE
    ..\UserCode\FreeModbus\modbus\functions\mbfuncother.c 0x00000000   Number         0  mbfuncother.o ABSOLUTE
    ..\UserCode\FreeModbus\modbus\functions\mbutils.c 0x00000000   Number         0  mbutils.o ABSOLUTE
    ..\UserCode\FreeModbus\modbus\mb.c       0x00000000   Number         0  mb.o ABSOLUTE
    ..\UserCode\FreeModbus\modbus\mdcb.c     0x00000000   Number         0  mdcb.o ABSOLUTE
    ..\UserCode\FreeModbus\modbus\rtu\mbcrc.c 0x00000000   Number         0  mbcrc.o ABSOLUTE
    ..\UserCode\FreeModbus\modbus\rtu\mbrtu.c 0x00000000   Number         0  mbrtu.o ABSOLUTE
    ..\UserCode\FreeModbus\modbus\tcp\mbtcp.c 0x00000000   Number         0  mbtcp.o ABSOLUTE
    ..\UserCode\FreeModbus\port\portevent.c  0x00000000   Number         0  portevent.o ABSOLUTE
    ..\UserCode\FreeModbus\port\portserial.c 0x00000000   Number         0  portserial.o ABSOLUTE
    ..\UserCode\FreeModbus\port\porttimer.c  0x00000000   Number         0  porttimer.o ABSOLUTE
    ..\UserCode\Service\Source\rn7326.c      0x00000000   Number         0  rn7326.o ABSOLUTE
    ..\\UserCode\\App\\Source\\modbus_rtu_slave.c 0x00000000   Number         0  modbus_rtu_slave.o ABSOLUTE
    ..\\UserCode\\App\\Source\\modbus_test.c 0x00000000   Number         0  modbus_test.o ABSOLUTE
    ..\\UserCode\\Driver\\Source\\rs485.c    0x00000000   Number         0  rs485.o ABSOLUTE
    ..\\UserCode\\FreeModbus\\modbus\\ascii\\mbascii.c 0x00000000   Number         0  mbascii.o ABSOLUTE
    ..\\UserCode\\FreeModbus\\modbus\\functions\\mbfunccoils.c 0x00000000   Number         0  mbfunccoils.o ABSOLUTE
    ..\\UserCode\\FreeModbus\\modbus\\functions\\mbfuncdisc.c 0x00000000   Number         0  mbfuncdisc.o ABSOLUTE
    ..\\UserCode\\FreeModbus\\modbus\\functions\\mbfuncfile.c 0x00000000   Number         0  mbfuncfile.o ABSOLUTE
    ..\\UserCode\\FreeModbus\\modbus\\functions\\mbfuncholding.c 0x00000000   Number         0  mbfuncholding.o ABSOLUTE
    ..\\UserCode\\FreeModbus\\modbus\\functions\\mbfuncinput.c 0x00000000   Number         0  mbfuncinput.o ABSOLUTE
    ..\\UserCode\\FreeModbus\\modbus\\functions\\mbfuncother.c 0x00000000   Number         0  mbfuncother.o ABSOLUTE
    ..\\UserCode\\FreeModbus\\modbus\\functions\\mbutils.c 0x00000000   Number         0  mbutils.o ABSOLUTE
    ..\\UserCode\\FreeModbus\\modbus\\mb.c   0x00000000   Number         0  mb.o ABSOLUTE
    ..\\UserCode\\FreeModbus\\modbus\\mdcb.c 0x00000000   Number         0  mdcb.o ABSOLUTE
    ..\\UserCode\\FreeModbus\\modbus\\rtu\\mbcrc.c 0x00000000   Number         0  mbcrc.o ABSOLUTE
    ..\\UserCode\\FreeModbus\\modbus\\rtu\\mbrtu.c 0x00000000   Number         0  mbrtu.o ABSOLUTE
    ..\\UserCode\\FreeModbus\\modbus\\tcp\\mbtcp.c 0x00000000   Number         0  mbtcp.o ABSOLUTE
    ..\\UserCode\\FreeModbus\\port\\portevent.c 0x00000000   Number         0  portevent.o ABSOLUTE
    ..\\UserCode\\FreeModbus\\port\\portserial.c 0x00000000   Number         0  portserial.o ABSOLUTE
    ..\\UserCode\\FreeModbus\\port\\porttimer.c 0x00000000   Number         0  porttimer.o ABSOLUTE
    ..\\UserCode\\Service\\Source\\rn7326.c  0x00000000   Number         0  rn7326.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    startup_stm32l496xx.s                    0x00000000   Number         0  startup_stm32l496xx.o ABSOLUTE
    RESET                                    0x08000000   Section      428  startup_stm32l496xx.o(RESET)
    !!!main                                  0x080001ac   Section        8  __main.o(!!!main)
    !!!scatter                               0x080001b4   Section       52  __scatter.o(!!!scatter)
    !!dczerorl2                              0x080001e8   Section       90  __dczerorl2.o(!!dczerorl2)
    !!handler_zi                             0x08000244   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$libinit$$00000000          0x08000260   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000001          0x08000262   Section        4  libinit2.o(.ARM.Collect$$libinit$$00000001)
    .ARM.Collect$$libinit$$00000004          0x08000266   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$0000000A          0x08000266   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    .ARM.Collect$$libinit$$0000000C          0x08000266   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x08000266   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$00000011          0x08000266   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$00000013          0x08000266   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x08000266   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000017          0x08000266   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x08000266   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x08000266   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x08000266   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x08000266   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x08000266   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x08000266   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x08000266   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$0000002C          0x08000266   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    .ARM.Collect$$libinit$$0000002E          0x08000266   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x08000266   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x08000266   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000033          0x08000266   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000033)
    .ARM.Collect$$libshutdown$$00000000      0x08000268   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x0800026a   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000004      0x0800026a   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000007      0x0800026a   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    .ARM.Collect$$libshutdown$$0000000A      0x0800026a   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    .ARM.Collect$$libshutdown$$0000000C      0x0800026a   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000F      0x0800026a   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    .ARM.Collect$$libshutdown$$00000010      0x0800026a   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    .ARM.Collect$$rtentry$$00000000          0x0800026c   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x0800026c   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x0800026c   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x08000272   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x08000272   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x08000276   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x08000276   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x0800027e   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x08000280   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x08000280   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x08000284   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .emb_text                                0x0800028c   Section      190  port.o(.emb_text)
    $v0                                      0x0800028c   Number         0  port.o(.emb_text)
    .text                                    0x0800036c   Section       64  startup_stm32l496xx.o(.text)
    $v0                                      0x0800036c   Number         0  startup_stm32l496xx.o(.text)
    .text                                    0x080003ac   Section      238  lludivv7m.o(.text)
    .text                                    0x0800049a   Section      138  rt_memcpy_v6.o(.text)
    .text                                    0x08000524   Section       16  aeabi_memset.o(.text)
    .text                                    0x08000534   Section       68  rt_memclr.o(.text)
    .text                                    0x08000578   Section       78  rt_memclr_w.o(.text)
    .text                                    0x080005c6   Section        0  heapauxi.o(.text)
    .text                                    0x080005cc   Section      100  rt_memcpy_w.o(.text)
    .text                                    0x08000630   Section       74  sys_stackheap_outer.o(.text)
    .text                                    0x0800067a   Section        0  exit.o(.text)
    .text                                    0x0800068c   Section        8  libspace.o(.text)
    .text                                    0x08000694   Section        0  sys_exit.o(.text)
    .text                                    0x080006a0   Section        2  use_no_semi.o(.text)
    .text                                    0x080006a2   Section        0  indicate_semi.o(.text)
    i.BusFault_Handler                       0x080006a2   Section        0  stm32l4xx_it.o(i.BusFault_Handler)
    i.CAN1_RX0_IRQHandler                    0x080006a4   Section        0  stm32l4xx_it.o(i.CAN1_RX0_IRQHandler)
    i.CANComTask                             0x080006b0   Section        0  freertos.o(i.CANComTask)
    i.DebugMon_Handler                       0x080006b8   Section        0  stm32l4xx_it.o(i.DebugMon_Handler)
    i.EXTI1_IRQHandler                       0x080006ba   Section        0  stm32l4xx_it.o(i.EXTI1_IRQHandler)
    i.EXTI2_IRQHandler                       0x080006c0   Section        0  stm32l4xx_it.o(i.EXTI2_IRQHandler)
    i.EnergyMeterTask                        0x080006c6   Section        0  freertos.o(i.EnergyMeterTask)
    i.Error_Handler                          0x080006d6   Section        0  main.o(i.Error_Handler)
    i.HAL_CAN_ErrorCallback                  0x080006da   Section        0  stm32l4xx_hal_can.o(i.HAL_CAN_ErrorCallback)
    i.HAL_CAN_IRQHandler                     0x080006dc   Section        0  stm32l4xx_hal_can.o(i.HAL_CAN_IRQHandler)
    i.HAL_CAN_Init                           0x080008de   Section        0  stm32l4xx_hal_can.o(i.HAL_CAN_Init)
    i.HAL_CAN_MspInit                        0x08000a30   Section        0  can.o(i.HAL_CAN_MspInit)
    i.HAL_CAN_RxFifo0FullCallback            0x08000aa4   Section        0  stm32l4xx_hal_can.o(i.HAL_CAN_RxFifo0FullCallback)
    i.HAL_CAN_RxFifo0MsgPendingCallback      0x08000aa6   Section        0  stm32l4xx_hal_can.o(i.HAL_CAN_RxFifo0MsgPendingCallback)
    i.HAL_CAN_RxFifo1FullCallback            0x08000aa8   Section        0  stm32l4xx_hal_can.o(i.HAL_CAN_RxFifo1FullCallback)
    i.HAL_CAN_RxFifo1MsgPendingCallback      0x08000aaa   Section        0  stm32l4xx_hal_can.o(i.HAL_CAN_RxFifo1MsgPendingCallback)
    i.HAL_CAN_SleepCallback                  0x08000aac   Section        0  stm32l4xx_hal_can.o(i.HAL_CAN_SleepCallback)
    i.HAL_CAN_TxMailbox0AbortCallback        0x08000aae   Section        0  stm32l4xx_hal_can.o(i.HAL_CAN_TxMailbox0AbortCallback)
    i.HAL_CAN_TxMailbox0CompleteCallback     0x08000ab0   Section        0  stm32l4xx_hal_can.o(i.HAL_CAN_TxMailbox0CompleteCallback)
    i.HAL_CAN_TxMailbox1AbortCallback        0x08000ab2   Section        0  stm32l4xx_hal_can.o(i.HAL_CAN_TxMailbox1AbortCallback)
    i.HAL_CAN_TxMailbox1CompleteCallback     0x08000ab4   Section        0  stm32l4xx_hal_can.o(i.HAL_CAN_TxMailbox1CompleteCallback)
    i.HAL_CAN_TxMailbox2AbortCallback        0x08000ab6   Section        0  stm32l4xx_hal_can.o(i.HAL_CAN_TxMailbox2AbortCallback)
    i.HAL_CAN_TxMailbox2CompleteCallback     0x08000ab8   Section        0  stm32l4xx_hal_can.o(i.HAL_CAN_TxMailbox2CompleteCallback)
    i.HAL_CAN_WakeUpFromRxMsgCallback        0x08000aba   Section        0  stm32l4xx_hal_can.o(i.HAL_CAN_WakeUpFromRxMsgCallback)
    i.HAL_DMA_Abort                          0x08000abc   Section        0  stm32l4xx_hal_dma.o(i.HAL_DMA_Abort)
    i.HAL_DMA_Abort_IT                       0x08000b06   Section        0  stm32l4xx_hal_dma.o(i.HAL_DMA_Abort_IT)
    i.HAL_Delay                              0x08000b54   Section        0  stm32l4xx_hal.o(i.HAL_Delay)
    i.HAL_GPIO_EXTI_Callback                 0x08000b78   Section        0  stm32l4xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback)
    i.HAL_GPIO_EXTI_IRQHandler               0x08000b7c   Section        0  stm32l4xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler)
    i.HAL_GPIO_Init                          0x08000b94   Section        0  stm32l4xx_hal_gpio.o(i.HAL_GPIO_Init)
    i.HAL_GPIO_TogglePin                     0x08000d66   Section        0  stm32l4xx_hal_gpio.o(i.HAL_GPIO_TogglePin)
    i.HAL_GPIO_WritePin                      0x08000d76   Section        0  stm32l4xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    i.HAL_GetTick                            0x08000d84   Section        0  stm32l4xx_hal.o(i.HAL_GetTick)
    i.HAL_I2CEx_ConfigAnalogFilter           0x08000d90   Section        0  stm32l4xx_hal_i2c_ex.o(i.HAL_I2CEx_ConfigAnalogFilter)
    i.HAL_I2CEx_ConfigDigitalFilter          0x08000dea   Section        0  stm32l4xx_hal_i2c_ex.o(i.HAL_I2CEx_ConfigDigitalFilter)
    i.HAL_I2C_Init                           0x08000e40   Section        0  stm32l4xx_hal_i2c.o(i.HAL_I2C_Init)
    i.HAL_I2C_MspInit                        0x08000f00   Section        0  i2c.o(i.HAL_I2C_MspInit)
    i.HAL_IncTick                            0x08000fe0   Section        0  stm32l4xx_hal.o(i.HAL_IncTick)
    i.HAL_Init                               0x08000ff0   Section        0  stm32l4xx_hal.o(i.HAL_Init)
    i.HAL_InitTick                           0x08001010   Section        0  stm32l4xx_hal_timebase_tim.o(i.HAL_InitTick)
    i.HAL_MspInit                            0x08001084   Section        0  stm32l4xx_hal_msp.o(i.HAL_MspInit)
    i.HAL_NVIC_ClearPendingIRQ               0x080010b8   Section        0  stm32l4xx_hal_cortex.o(i.HAL_NVIC_ClearPendingIRQ)
    i.HAL_NVIC_EnableIRQ                     0x080010d2   Section        0  stm32l4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    i.HAL_NVIC_SetPriority                   0x080010ec   Section        0  stm32l4xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    i.HAL_NVIC_SetPriorityGrouping           0x0800112c   Section        0  stm32l4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    i.HAL_PWREx_ControlVoltageScaling        0x08001150   Section        0  stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling)
    i.HAL_PWREx_GetVoltageRange              0x080011bc   Section        0  stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_GetVoltageRange)
    i.HAL_RCCEx_PeriphCLKConfig              0x080011cc   Section        0  stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig)
    i.HAL_RCC_ClockConfig                    0x080015a0   Section        0  stm32l4xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    i.HAL_RCC_GetClockConfig                 0x08001710   Section        0  stm32l4xx_hal_rcc.o(i.HAL_RCC_GetClockConfig)
    i.HAL_RCC_GetHCLKFreq                    0x0800174c   Section        0  stm32l4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq)
    i.HAL_RCC_GetPCLK1Freq                   0x08001758   Section        0  stm32l4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    i.HAL_RCC_GetPCLK2Freq                   0x0800177c   Section        0  stm32l4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    i.HAL_RCC_GetSysClockFreq                0x080017a0   Section        0  stm32l4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    i.HAL_RCC_OscConfig                      0x08001844   Section        0  stm32l4xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    i.HAL_SPI_Init                           0x08001e08   Section        0  stm32l4xx_hal_spi.o(i.HAL_SPI_Init)
    i.HAL_SPI_MspInit                        0x08001eec   Section        0  spi.o(i.HAL_SPI_MspInit)
    i.HAL_SPI_Receive                        0x08001f54   Section        0  stm32l4xx_hal_spi.o(i.HAL_SPI_Receive)
    i.HAL_SPI_Transmit                       0x080020e0   Section        0  stm32l4xx_hal_spi.o(i.HAL_SPI_Transmit)
    i.HAL_SPI_TransmitReceive                0x080022a2   Section        0  stm32l4xx_hal_spi.o(i.HAL_SPI_TransmitReceive)
    i.HAL_TIMEx_Break2Callback               0x08002514   Section        0  stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_Break2Callback)
    i.HAL_TIMEx_BreakCallback                0x08002516   Section        0  stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback)
    i.HAL_TIMEx_CommutCallback               0x08002518   Section        0  stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback)
    i.HAL_TIM_Base_Init                      0x0800251a   Section        0  stm32l4xx_hal_tim.o(i.HAL_TIM_Base_Init)
    i.HAL_TIM_Base_MspInit                   0x08002568   Section        0  tim.o(i.HAL_TIM_Base_MspInit)
    i.HAL_TIM_Base_Start_IT                  0x080025a4   Section        0  stm32l4xx_hal_tim.o(i.HAL_TIM_Base_Start_IT)
    i.HAL_TIM_IC_CaptureCallback             0x08002630   Section        0  stm32l4xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback)
    i.HAL_TIM_IRQHandler                     0x08002632   Section        0  stm32l4xx_hal_tim.o(i.HAL_TIM_IRQHandler)
    i.HAL_TIM_OC_DelayElapsedCallback        0x080027be   Section        0  stm32l4xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback)
    i.HAL_TIM_PWM_PulseFinishedCallback      0x080027c0   Section        0  stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback)
    i.HAL_TIM_PeriodElapsedCallback          0x080027c4   Section        0  main.o(i.HAL_TIM_PeriodElapsedCallback)
    i.HAL_TIM_TriggerCallback                0x080027d8   Section        0  stm32l4xx_hal_tim.o(i.HAL_TIM_TriggerCallback)
    i.HAL_UARTEx_RxEventCallback             0x080027da   Section        0  stm32l4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback)
    i.HAL_UARTEx_WakeupCallback              0x080027dc   Section        0  stm32l4xx_hal_uart_ex.o(i.HAL_UARTEx_WakeupCallback)
    i.HAL_UART_ErrorCallback                 0x080027e0   Section        0  portserial.o(i.HAL_UART_ErrorCallback)
    i.HAL_UART_IRQHandler                    0x0800280c   Section        0  stm32l4xx_hal_uart.o(i.HAL_UART_IRQHandler)
    i.HAL_UART_Init                          0x08002ab0   Section        0  stm32l4xx_hal_uart.o(i.HAL_UART_Init)
    i.HAL_UART_MspInit                       0x08002b1c   Section        0  usart.o(i.HAL_UART_MspInit)
    i.HAL_UART_TxCpltCallback                0x08002bac   Section        0  portserial.o(i.HAL_UART_TxCpltCallback)
    i.HardFault_Handler                      0x08002bcc   Section        0  stm32l4xx_it.o(i.HardFault_Handler)
    i.MX_CAN1_Init                           0x08002bd0   Section        0  can.o(i.MX_CAN1_Init)
    i.MX_FREERTOS_Init                       0x08002c1c   Section        0  freertos.o(i.MX_FREERTOS_Init)
    i.MX_GPIO_Init                           0x08002c84   Section        0  gpio.o(i.MX_GPIO_Init)
    i.MX_I2C1_Init                           0x08002d94   Section        0  i2c.o(i.MX_I2C1_Init)
    i.MX_I2C2_Init                           0x08002dec   Section        0  i2c.o(i.MX_I2C2_Init)
    i.MX_SPI2_Init                           0x08002e44   Section        0  spi.o(i.MX_SPI2_Init)
    i.MX_TIM16_Init                          0x08002e94   Section        0  tim.o(i.MX_TIM16_Init)
    i.MX_USART1_UART_Init                    0x08002ecc   Section        0  usart.o(i.MX_USART1_UART_Init)
    i.MemManage_Handler                      0x08002f0c   Section        0  stm32l4xx_it.o(i.MemManage_Handler)
    i.ModbusComTask                          0x08002f10   Section        0  freertos.o(i.ModbusComTask)
    i.Modbus_Test_Get_Holding_Regs           0x08002fd0   Section        0  modbus_test.o(i.Modbus_Test_Get_Holding_Regs)
    i.Modbus_Test_Inc_Callback_Count         0x08002fd8   Section        0  modbus_test.o(i.Modbus_Test_Inc_Callback_Count)
    i.Modbus_Test_Inc_Poll_Count             0x08002fe8   Section        0  modbus_test.o(i.Modbus_Test_Inc_Poll_Count)
    i.Modbus_Test_Inc_Rx_Count               0x08002ff8   Section        0  modbus_test.o(i.Modbus_Test_Inc_Rx_Count)
    i.Modbus_Test_Inc_Timer_Count            0x08003008   Section        0  modbus_test.o(i.Modbus_Test_Inc_Timer_Count)
    i.Modbus_Test_Inc_Tx_Count               0x08003018   Section        0  modbus_test.o(i.Modbus_Test_Inc_Tx_Count)
    i.Modbus_Test_Init                       0x08003028   Section        0  modbus_test.o(i.Modbus_Test_Init)
    i.Modbus_Test_LED_Indicate               0x08003058   Section        0  modbus_test.o(i.Modbus_Test_LED_Indicate)
    i.Modbus_Test_Update_Registers           0x08003090   Section        0  modbus_test.o(i.Modbus_Test_Update_Registers)
    i.NMI_Handler                            0x080030cc   Section        0  stm32l4xx_it.o(i.NMI_Handler)
    i.RCCEx_PLLSAI1_Config                   0x080030d0   Section        0  stm32l4xx_hal_rcc_ex.o(i.RCCEx_PLLSAI1_Config)
    RCCEx_PLLSAI1_Config                     0x080030d1   Thumb Code   304  stm32l4xx_hal_rcc_ex.o(i.RCCEx_PLLSAI1_Config)
    i.RCCEx_PLLSAI2_Config                   0x08003210   Section        0  stm32l4xx_hal_rcc_ex.o(i.RCCEx_PLLSAI2_Config)
    RCCEx_PLLSAI2_Config                     0x08003211   Thumb Code   270  stm32l4xx_hal_rcc_ex.o(i.RCCEx_PLLSAI2_Config)
    i.RCC_SetFlashLatencyFromMSIRange        0x0800332c   Section        0  stm32l4xx_hal_rcc.o(i.RCC_SetFlashLatencyFromMSIRange)
    RCC_SetFlashLatencyFromMSIRange          0x0800332d   Thumb Code   122  stm32l4xx_hal_rcc.o(i.RCC_SetFlashLatencyFromMSIRange)
    i.RN7326_ReadReg                         0x080033b0   Section        0  rn7326.o(i.RN7326_ReadReg)
    i.RS485_Enter_RX_Mode                    0x080034a0   Section        0  rs485.o(i.RS485_Enter_RX_Mode)
    i.RS485_Enter_TX_Mode                    0x080034c4   Section        0  rs485.o(i.RS485_Enter_TX_Mode)
    i.RS485_Init                             0x080034e6   Section        0  rs485.o(i.RS485_Init)
    i.SPI_EndRxTransaction                   0x0800351e   Section        0  stm32l4xx_hal_spi.o(i.SPI_EndRxTransaction)
    SPI_EndRxTransaction                     0x0800351f   Thumb Code   126  stm32l4xx_hal_spi.o(i.SPI_EndRxTransaction)
    i.SPI_EndRxTxTransaction                 0x0800359c   Section        0  stm32l4xx_hal_spi.o(i.SPI_EndRxTxTransaction)
    SPI_EndRxTxTransaction                   0x0800359d   Thumb Code   100  stm32l4xx_hal_spi.o(i.SPI_EndRxTxTransaction)
    i.SPI_WaitFifoStateUntilTimeout          0x08003600   Section        0  stm32l4xx_hal_spi.o(i.SPI_WaitFifoStateUntilTimeout)
    SPI_WaitFifoStateUntilTimeout            0x08003601   Thumb Code   218  stm32l4xx_hal_spi.o(i.SPI_WaitFifoStateUntilTimeout)
    i.SPI_WaitFlagStateUntilTimeout          0x080036e8   Section        0  stm32l4xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout)
    SPI_WaitFlagStateUntilTimeout            0x080036e9   Thumb Code   184  stm32l4xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout)
    i.SensorsTask                            0x080037a4   Section        0  freertos.o(i.SensorsTask)
    i.SysMonitorTask                         0x080037ac   Section        0  freertos.o(i.SysMonitorTask)
    i.SysTick_Handler                        0x080037b4   Section        0  cmsis_os2.o(i.SysTick_Handler)
    i.SystemClock_Config                     0x080037ce   Section        0  main.o(i.SystemClock_Config)
    i.SystemInit                             0x08003840   Section        0  system_stm32l4xx.o(i.SystemInit)
    i.TIM1_UP_TIM16_IRQHandler               0x08003850   Section        0  stm32l4xx_it.o(i.TIM1_UP_TIM16_IRQHandler)
    i.TIM6_DAC_IRQHandler                    0x0800387c   Section        0  stm32l4xx_it.o(i.TIM6_DAC_IRQHandler)
    i.TIM_Base_SetConfig                     0x08003888   Section        0  stm32l4xx_hal_tim.o(i.TIM_Base_SetConfig)
    i.UART_AdvFeatureConfig                  0x0800394e   Section        0  stm32l4xx_hal_uart.o(i.UART_AdvFeatureConfig)
    i.UART_CheckIdleState                    0x08003a16   Section        0  stm32l4xx_hal_uart.o(i.UART_CheckIdleState)
    i.UART_DMAAbortOnError                   0x08003a76   Section        0  stm32l4xx_hal_uart.o(i.UART_DMAAbortOnError)
    UART_DMAAbortOnError                     0x08003a77   Thumb Code    20  stm32l4xx_hal_uart.o(i.UART_DMAAbortOnError)
    i.UART_EndRxTransfer                     0x08003a8a   Section        0  stm32l4xx_hal_uart.o(i.UART_EndRxTransfer)
    UART_EndRxTransfer                       0x08003a8b   Thumb Code    74  stm32l4xx_hal_uart.o(i.UART_EndRxTransfer)
    i.UART_EndTransmit_IT                    0x08003ad4   Section        0  stm32l4xx_hal_uart.o(i.UART_EndTransmit_IT)
    UART_EndTransmit_IT                      0x08003ad5   Thumb Code    34  stm32l4xx_hal_uart.o(i.UART_EndTransmit_IT)
    i.UART_SetConfig                         0x08003af8   Section        0  stm32l4xx_hal_uart.o(i.UART_SetConfig)
    i.UART_WaitOnFlagUntilTimeout            0x08003db8   Section        0  stm32l4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout)
    i.USART1_IRQHandler                      0x08003e78   Section        0  stm32l4xx_it.o(i.USART1_IRQHandler)
    i.UsageFault_Handler                     0x08003ed0   Section        0  stm32l4xx_it.o(i.UsageFault_Handler)
    i.__NVIC_SetPriority                     0x08003ed2   Section        0  stm32l4xx_hal_cortex.o(i.__NVIC_SetPriority)
    __NVIC_SetPriority                       0x08003ed3   Thumb Code    32  stm32l4xx_hal_cortex.o(i.__NVIC_SetPriority)
    i.eMBEnable                              0x08003ef4   Section        0  mb.o(i.eMBEnable)
    i.eMBFuncReadCoils                       0x08003f14   Section        0  mbfunccoils.o(i.eMBFuncReadCoils)
    i.eMBFuncReadDiscreteInputs              0x08003f90   Section        0  mbfuncdisc.o(i.eMBFuncReadDiscreteInputs)
    i.eMBFuncReadHoldingRegister             0x08004008   Section        0  mbfuncholding.o(i.eMBFuncReadHoldingRegister)
    i.eMBFuncReadInputRegister               0x0800406e   Section        0  mbfuncinput.o(i.eMBFuncReadInputRegister)
    i.eMBFuncReadWriteMultipleHoldingRegister 0x080040d2   Section        0  mbfuncholding.o(i.eMBFuncReadWriteMultipleHoldingRegister)
    i.eMBFuncReportSlaveID                   0x08004170   Section        0  mbfuncother.o(i.eMBFuncReportSlaveID)
    i.eMBFuncWriteCoil                       0x08004194   Section        0  mbfunccoils.o(i.eMBFuncWriteCoil)
    i.eMBFuncWriteHoldingRegister            0x080041ee   Section        0  mbfuncholding.o(i.eMBFuncWriteHoldingRegister)
    i.eMBFuncWriteMultipleCoils              0x08004220   Section        0  mbfunccoils.o(i.eMBFuncWriteMultipleCoils)
    i.eMBFuncWriteMultipleHoldingRegister    0x08004286   Section        0  mbfuncholding.o(i.eMBFuncWriteMultipleHoldingRegister)
    i.eMBInit                                0x080042d8   Section        0  mb.o(i.eMBInit)
    i.eMBPoll                                0x08004360   Section        0  mb.o(i.eMBPoll)
    i.eMBRTUInit                             0x08004414   Section        0  mbrtu.o(i.eMBRTUInit)
    i.eMBRTUReceive                          0x0800445c   Section        0  mbrtu.o(i.eMBRTUReceive)
    i.eMBRTUSend                             0x080044a4   Section        0  mbrtu.o(i.eMBRTUSend)
    i.eMBRTUStart                            0x08004560   Section        0  mbrtu.o(i.eMBRTUStart)
    i.eMBRTUStop                             0x08004580   Section        0  mbrtu.o(i.eMBRTUStop)
    i.eMBRegCoilsCB                          0x08004594   Section        0  mdcb.o(i.eMBRegCoilsCB)
    i.eMBRegDiscreteCB                       0x08004608   Section        0  mdcb.o(i.eMBRegDiscreteCB)
    i.eMBRegHoldingCB                        0x08004654   Section        0  mdcb.o(i.eMBRegHoldingCB)
    i.eMBRegInputCB                          0x080046dc   Section        0  mdcb.o(i.eMBRegInputCB)
    i.main                                   0x08004718   Section        0  main.o(i.main)
    i.osDelay                                0x0800474a   Section        0  cmsis_os2.o(i.osDelay)
    i.osKernelInitialize                     0x08004768   Section        0  cmsis_os2.o(i.osKernelInitialize)
    i.osKernelStart                          0x08004790   Section        0  cmsis_os2.o(i.osKernelStart)
    i.osThreadNew                            0x080047c8   Section        0  cmsis_os2.o(i.osThreadNew)
    i.prvAddCurrentTaskToDelayedList         0x08004884   Section        0  tasks.o(i.prvAddCurrentTaskToDelayedList)
    prvAddCurrentTaskToDelayedList           0x08004885   Thumb Code    86  tasks.o(i.prvAddCurrentTaskToDelayedList)
    i.prvAddNewTaskToReadyList               0x080048e4   Section        0  tasks.o(i.prvAddNewTaskToReadyList)
    prvAddNewTaskToReadyList                 0x080048e5   Thumb Code   124  tasks.o(i.prvAddNewTaskToReadyList)
    i.prvCheckForValidListAndQueue           0x0800496c   Section        0  timers.o(i.prvCheckForValidListAndQueue)
    prvCheckForValidListAndQueue             0x0800496d   Thumb Code    72  timers.o(i.prvCheckForValidListAndQueue)
    i.prvCheckTasksWaitingTermination        0x080049c4   Section        0  tasks.o(i.prvCheckTasksWaitingTermination)
    prvCheckTasksWaitingTermination          0x080049c5   Thumb Code    52  tasks.o(i.prvCheckTasksWaitingTermination)
    i.prvCopyDataFromQueue                   0x08004a00   Section        0  queue.o(i.prvCopyDataFromQueue)
    prvCopyDataFromQueue                     0x08004a01   Thumb Code    40  queue.o(i.prvCopyDataFromQueue)
    i.prvCopyDataToQueue                     0x08004a28   Section        0  queue.o(i.prvCopyDataToQueue)
    prvCopyDataToQueue                       0x08004a29   Thumb Code   110  queue.o(i.prvCopyDataToQueue)
    i.prvDeleteTCB                           0x08004a96   Section        0  tasks.o(i.prvDeleteTCB)
    prvDeleteTCB                             0x08004a97   Thumb Code    64  tasks.o(i.prvDeleteTCB)
    i.prvGetNextExpireTime                   0x08004ad8   Section        0  timers.o(i.prvGetNextExpireTime)
    prvGetNextExpireTime                     0x08004ad9   Thumb Code    30  timers.o(i.prvGetNextExpireTime)
    i.prvHeapInit                            0x08004afc   Section        0  heap_4.o(i.prvHeapInit)
    prvHeapInit                              0x08004afd   Thumb Code    68  heap_4.o(i.prvHeapInit)
    i.prvIdleTask                            0x08004b48   Section        0  tasks.o(i.prvIdleTask)
    prvIdleTask                              0x08004b49   Thumb Code    30  tasks.o(i.prvIdleTask)
    i.prvInitialiseNewQueue                  0x08004b70   Section        0  queue.o(i.prvInitialiseNewQueue)
    prvInitialiseNewQueue                    0x08004b71   Thumb Code    34  queue.o(i.prvInitialiseNewQueue)
    i.prvInitialiseNewTask                   0x08004b92   Section        0  tasks.o(i.prvInitialiseNewTask)
    prvInitialiseNewTask                     0x08004b93   Thumb Code   168  tasks.o(i.prvInitialiseNewTask)
    i.prvInitialiseTaskLists                 0x08004c40   Section        0  tasks.o(i.prvInitialiseTaskLists)
    prvInitialiseTaskLists                   0x08004c41   Thumb Code    74  tasks.o(i.prvInitialiseTaskLists)
    i.prvInsertBlockIntoFreeList             0x08004c98   Section        0  heap_4.o(i.prvInsertBlockIntoFreeList)
    prvInsertBlockIntoFreeList               0x08004c99   Thumb Code    78  heap_4.o(i.prvInsertBlockIntoFreeList)
    i.prvInsertTimerInActiveList             0x08004cec   Section        0  timers.o(i.prvInsertTimerInActiveList)
    prvInsertTimerInActiveList               0x08004ced   Thumb Code    60  timers.o(i.prvInsertTimerInActiveList)
    i.prvIsQueueEmpty                        0x08004d2c   Section        0  queue.o(i.prvIsQueueEmpty)
    prvIsQueueEmpty                          0x08004d2d   Thumb Code    26  queue.o(i.prvIsQueueEmpty)
    i.prvIsQueueFull                         0x08004d46   Section        0  queue.o(i.prvIsQueueFull)
    prvIsQueueFull                           0x08004d47   Thumb Code    30  queue.o(i.prvIsQueueFull)
    i.prvProcessExpiredTimer                 0x08004d64   Section        0  timers.o(i.prvProcessExpiredTimer)
    prvProcessExpiredTimer                   0x08004d65   Thumb Code    98  timers.o(i.prvProcessExpiredTimer)
    i.prvProcessReceivedCommands             0x08004dcc   Section        0  timers.o(i.prvProcessReceivedCommands)
    prvProcessReceivedCommands               0x08004dcd   Thumb Code   248  timers.o(i.prvProcessReceivedCommands)
    i.prvProcessTimerOrBlockTask             0x08004ec8   Section        0  timers.o(i.prvProcessTimerOrBlockTask)
    prvProcessTimerOrBlockTask               0x08004ec9   Thumb Code   100  timers.o(i.prvProcessTimerOrBlockTask)
    i.prvResetNextTaskUnblockTime            0x08004f34   Section        0  tasks.o(i.prvResetNextTaskUnblockTime)
    prvResetNextTaskUnblockTime              0x08004f35   Thumb Code    30  tasks.o(i.prvResetNextTaskUnblockTime)
    i.prvSampleTimeNow                       0x08004f58   Section        0  timers.o(i.prvSampleTimeNow)
    prvSampleTimeNow                         0x08004f59   Thumb Code    38  timers.o(i.prvSampleTimeNow)
    i.prvSwitchTimerLists                    0x08004f84   Section        0  timers.o(i.prvSwitchTimerLists)
    prvSwitchTimerLists                      0x08004f85   Thumb Code   106  timers.o(i.prvSwitchTimerLists)
    i.prvTaskExitError                       0x08004ff4   Section        0  port.o(i.prvTaskExitError)
    prvTaskExitError                         0x08004ff5   Thumb Code    40  port.o(i.prvTaskExitError)
    i.prvTimerTask                           0x08005020   Section        0  timers.o(i.prvTimerTask)
    prvTimerTask                             0x08005021   Thumb Code    20  timers.o(i.prvTimerTask)
    i.prvUnlockQueue                         0x08005034   Section        0  queue.o(i.prvUnlockQueue)
    prvUnlockQueue                           0x08005035   Thumb Code   106  queue.o(i.prvUnlockQueue)
    i.prveMBError2Exception                  0x0800509e   Section        0  mbutils.o(i.prveMBError2Exception)
    i.prvvTIMERExpiredISR                    0x080050bc   Section        0  porttimer.o(i.prvvTIMERExpiredISR)
    i.prvvUARTRxISR                          0x080050f8   Section        0  portserial.o(i.prvvUARTRxISR)
    i.prvvUARTTxReadyISR                     0x0800510c   Section        0  portserial.o(i.prvvUARTTxReadyISR)
    i.pvPortMalloc                           0x08005120   Section        0  heap_4.o(i.pvPortMalloc)
    i.pxPortInitialiseStack                  0x080051fc   Section        0  port.o(i.pxPortInitialiseStack)
    i.test_read_LostVoltage                  0x08005220   Section        0  rn7326.o(i.test_read_LostVoltage)
    i.test_read_voltage                      0x08005240   Section        0  rn7326.o(i.test_read_voltage)
    i.usMBCRC16                              0x08005264   Section        0  mbcrc.o(i.usMBCRC16)
    i.uxListRemove                           0x08005290   Section        0  list.o(i.uxListRemove)
    i.vApplicationGetIdleTaskMemory          0x080052b8   Section        0  cmsis_os2.o(i.vApplicationGetIdleTaskMemory)
    i.vApplicationGetTimerTaskMemory         0x080052cc   Section        0  cmsis_os2.o(i.vApplicationGetTimerTaskMemory)
    i.vListInitialise                        0x080052e4   Section        0  list.o(i.vListInitialise)
    i.vListInitialiseItem                    0x080052fa   Section        0  list.o(i.vListInitialiseItem)
    i.vListInsert                            0x08005300   Section        0  list.o(i.vListInsert)
    i.vListInsertEnd                         0x08005330   Section        0  list.o(i.vListInsertEnd)
    i.vMBPortSerialEnable                    0x08005348   Section        0  portserial.o(i.vMBPortSerialEnable)
    i.vMBPortTimersDisable                   0x0800539c   Section        0  porttimer.o(i.vMBPortTimersDisable)
    i.vMBPortTimersEnable                    0x080053d8   Section        0  porttimer.o(i.vMBPortTimersEnable)
    i.vPortEnterCritical                     0x08005404   Section        0  port.o(i.vPortEnterCritical)
    i.vPortExitCritical                      0x08005444   Section        0  port.o(i.vPortExitCritical)
    i.vPortFree                              0x08005470   Section        0  heap_4.o(i.vPortFree)
    i.vPortSetupTimerInterrupt               0x080054d4   Section        0  port.o(i.vPortSetupTimerInterrupt)
    i.vPortValidateInterruptPriority         0x080054f8   Section        0  port.o(i.vPortValidateInterruptPriority)
    i.vQueueAddToRegistry                    0x0800554c   Section        0  queue.o(i.vQueueAddToRegistry)
    i.vQueueWaitForMessageRestricted         0x08005570   Section        0  queue.o(i.vQueueWaitForMessageRestricted)
    i.vTaskDelay                             0x080055b8   Section        0  tasks.o(i.vTaskDelay)
    i.vTaskInternalSetTimeOutState           0x08005608   Section        0  tasks.o(i.vTaskInternalSetTimeOutState)
    i.vTaskMissedYield                       0x08005618   Section        0  tasks.o(i.vTaskMissedYield)
    i.vTaskPlaceOnEventList                  0x08005624   Section        0  tasks.o(i.vTaskPlaceOnEventList)
    i.vTaskPlaceOnEventListRestricted        0x08005658   Section        0  tasks.o(i.vTaskPlaceOnEventListRestricted)
    i.vTaskStartScheduler                    0x08005694   Section        0  tasks.o(i.vTaskStartScheduler)
    i.vTaskSuspendAll                        0x08005724   Section        0  tasks.o(i.vTaskSuspendAll)
    i.vTaskSwitchContext                     0x08005734   Section        0  tasks.o(i.vTaskSwitchContext)
    i.xMBPortEventGet                        0x0800579c   Section        0  portevent.o(i.xMBPortEventGet)
    i.xMBPortEventInit                       0x080057b8   Section        0  portevent.o(i.xMBPortEventInit)
    i.xMBPortEventPost                       0x080057c8   Section        0  portevent.o(i.xMBPortEventPost)
    i.xMBPortSerialGetByte                   0x080057d8   Section        0  portserial.o(i.xMBPortSerialGetByte)
    i.xMBPortSerialInit                      0x080057e8   Section        0  portserial.o(i.xMBPortSerialInit)
    i.xMBPortSerialPutByte                   0x080057ec   Section        0  portserial.o(i.xMBPortSerialPutByte)
    i.xMBPortTimersInit                      0x080057fc   Section        0  porttimer.o(i.xMBPortTimersInit)
    i.xMBRTUReceiveFSM                       0x08005854   Section        0  mbrtu.o(i.xMBRTUReceiveFSM)
    i.xMBRTUTimerT35Expired                  0x080058c4   Section        0  mbrtu.o(i.xMBRTUTimerT35Expired)
    i.xMBRTUTransmitFSM                      0x080058f8   Section        0  mbrtu.o(i.xMBRTUTransmitFSM)
    i.xMBUtilGetBits                         0x0800595c   Section        0  mbutils.o(i.xMBUtilGetBits)
    i.xMBUtilSetBits                         0x0800597e   Section        0  mbutils.o(i.xMBUtilSetBits)
    i.xPortStartScheduler                    0x080059a8   Section        0  port.o(i.xPortStartScheduler)
    i.xPortSysTickHandler                    0x08005a9c   Section        0  port.o(i.xPortSysTickHandler)
    i.xQueueGenericCreateStatic              0x08005ac8   Section        0  queue.o(i.xQueueGenericCreateStatic)
    i.xQueueGenericReset                     0x08005b34   Section        0  queue.o(i.xQueueGenericReset)
    i.xQueueGenericSend                      0x08005bc0   Section        0  queue.o(i.xQueueGenericSend)
    i.xQueueGenericSendFromISR               0x08005d24   Section        0  queue.o(i.xQueueGenericSendFromISR)
    i.xQueueReceive                          0x08005de4   Section        0  queue.o(i.xQueueReceive)
    i.xTaskCheckForTimeOut                   0x08005f20   Section        0  tasks.o(i.xTaskCheckForTimeOut)
    i.xTaskCreate                            0x08005f9c   Section        0  tasks.o(i.xTaskCreate)
    i.xTaskCreateStatic                      0x08006006   Section        0  tasks.o(i.xTaskCreateStatic)
    i.xTaskGetSchedulerState                 0x08006064   Section        0  tasks.o(i.xTaskGetSchedulerState)
    i.xTaskGetTickCount                      0x08006080   Section        0  tasks.o(i.xTaskGetTickCount)
    i.xTaskIncrementTick                     0x0800608c   Section        0  tasks.o(i.xTaskIncrementTick)
    i.xTaskPriorityDisinherit                0x08006158   Section        0  tasks.o(i.xTaskPriorityDisinherit)
    i.xTaskRemoveFromEventList               0x080061d4   Section        0  tasks.o(i.xTaskRemoveFromEventList)
    i.xTaskResumeAll                         0x0800624c   Section        0  tasks.o(i.xTaskResumeAll)
    i.xTimerCreateTimerTask                  0x08006314   Section        0  timers.o(i.xTimerCreateTimerTask)
    i.xTimerGenericCommand                   0x0800637c   Section        0  timers.o(i.xTimerGenericCommand)
    x$fpl$fpinit                             0x080063e8   Section       10  fpinit.o(x$fpl$fpinit)
    $v0                                      0x080063e8   Number         0  fpinit.o(x$fpl$fpinit)
    .constdata                               0x080063f4   Section      180  freertos.o(.constdata)
    .constdata                               0x080064a8   Section       64  system_stm32l4xx.o(.constdata)
    .constdata                               0x080064e8   Section        8  system_stm32l4xx.o(.constdata)
    .constdata                               0x080064f0   Section      512  mbcrc.o(.constdata)
    aucCRCHi                                 0x080064f0   Data         256  mbcrc.o(.constdata)
    aucCRCLo                                 0x080065f0   Data         256  mbcrc.o(.constdata)
    .conststring                             0x080066f0   Section       72  freertos.o(.conststring)
    .data                                    0x20000000   Section       28  freertos.o(.data)
    update_counter                           0x20000000   Data           4  freertos.o(.data)
    counter                                  0x20000004   Data           4  freertos.o(.data)
    .data                                    0x2000001c   Section       12  stm32l4xx_hal.o(.data)
    .data                                    0x20000028   Section        4  system_stm32l4xx.o(.data)
    .data                                    0x2000002c   Section       60  tasks.o(.data)
    uxDeletedTasksWaitingCleanUp             0x20000030   Data           4  tasks.o(.data)
    uxCurrentNumberOfTasks                   0x20000034   Data           4  tasks.o(.data)
    xTickCount                               0x20000038   Data           4  tasks.o(.data)
    uxTopReadyPriority                       0x2000003c   Data           4  tasks.o(.data)
    xSchedulerRunning                        0x20000040   Data           4  tasks.o(.data)
    xPendedTicks                             0x20000044   Data           4  tasks.o(.data)
    xYieldPending                            0x20000048   Data           4  tasks.o(.data)
    xNumOfOverflows                          0x2000004c   Data           4  tasks.o(.data)
    uxTaskNumber                             0x20000050   Data           4  tasks.o(.data)
    xNextTaskUnblockTime                     0x20000054   Data           4  tasks.o(.data)
    xIdleTaskHandle                          0x20000058   Data           4  tasks.o(.data)
    uxSchedulerSuspended                     0x2000005c   Data           4  tasks.o(.data)
    pxDelayedTaskList                        0x20000060   Data           4  tasks.o(.data)
    pxOverflowDelayedTaskList                0x20000064   Data           4  tasks.o(.data)
    .data                                    0x20000068   Section       20  timers.o(.data)
    xTimerQueue                              0x20000068   Data           4  timers.o(.data)
    xTimerTaskHandle                         0x2000006c   Data           4  timers.o(.data)
    xLastTime                                0x20000070   Data           4  timers.o(.data)
    pxCurrentTimerList                       0x20000074   Data           4  timers.o(.data)
    pxOverflowTimerList                      0x20000078   Data           4  timers.o(.data)
    .data                                    0x2000007c   Section        4  cmsis_os2.o(.data)
    KernelState                              0x2000007c   Data           4  cmsis_os2.o(.data)
    .data                                    0x20000080   Section       32  heap_4.o(.data)
    pxEnd                                    0x20000080   Data           4  heap_4.o(.data)
    xFreeBytesRemaining                      0x20000084   Data           4  heap_4.o(.data)
    xMinimumEverFreeBytesRemaining           0x20000088   Data           4  heap_4.o(.data)
    xNumberOfSuccessfulAllocations           0x2000008c   Data           4  heap_4.o(.data)
    xNumberOfSuccessfulFrees                 0x20000090   Data           4  heap_4.o(.data)
    xBlockAllocatedBit                       0x20000094   Data           4  heap_4.o(.data)
    xStart                                   0x20000098   Data           8  heap_4.o(.data)
    .data                                    0x200000a0   Section       12  port.o(.data)
    ucMaxSysCallPriority                     0x200000a0   Data           1  port.o(.data)
    uxCriticalNesting                        0x200000a4   Data           4  port.o(.data)
    ulMaxPRIGROUPValue                       0x200000a8   Data           4  port.o(.data)
    .data                                    0x200000ac   Section       52  modbus_test.o(.data)
    rx_count                                 0x200000ac   Data           4  modbus_test.o(.data)
    tx_count                                 0x200000b0   Data           4  modbus_test.o(.data)
    timer_count                              0x200000b4   Data           4  modbus_test.o(.data)
    error_count                              0x200000b8   Data           4  modbus_test.o(.data)
    poll_count                               0x200000bc   Data           4  modbus_test.o(.data)
    init_count                               0x200000c0   Data           4  modbus_test.o(.data)
    callback_count                           0x200000c4   Data           4  modbus_test.o(.data)
    update_counter                           0x200000c8   Data           4  modbus_test.o(.data)
    test_holding_regs                        0x200000cc   Data          20  modbus_test.o(.data)
    .data                                    0x200000e0   Section        2  rn7326.o(.data)
    .data                                    0x200000e2   Section        2  mbfuncother.o(.data)
    usMBSlaveIDLen                           0x200000e2   Data           2  mbfuncother.o(.data)
    .data                                    0x200000e4   Section       12  mbrtu.o(.data)
    sendTestFrame                            0x200000e4   Data           1  mbrtu.o(.data)
    isFirstByte                              0x200000e5   Data           1  mbrtu.o(.data)
    eSndState                                0x200000e6   Data           1  mbrtu.o(.data)
    eRcvState                                0x200000e7   Data           1  mbrtu.o(.data)
    usSndBufferCount                         0x200000e8   Data           2  mbrtu.o(.data)
    usRcvBufferPos                           0x200000ea   Data           2  mbrtu.o(.data)
    pucSndBufferCur                          0x200000ec   Data           4  mbrtu.o(.data)
    .data                                    0x200000f0   Section      172  mb.o(.data)
    eMBState                                 0x200000f0   Data           1  mb.o(.data)
    ucRcvAddress                             0x200000f1   Data           1  mb.o(.data)
    ucFunctionCode                           0x200000f2   Data           1  mb.o(.data)
    eException                               0x200000f3   Data           1  mb.o(.data)
    ucMBAddress                              0x200000f4   Data           1  mb.o(.data)
    eMBCurrentMode                           0x200000f5   Data           1  mb.o(.data)
    usLength                                 0x200000f6   Data           2  mb.o(.data)
    ucMBFrame                                0x200000f8   Data           4  mb.o(.data)
    peMBFrameSendCur                         0x200000fc   Data           4  mb.o(.data)
    pvMBFrameStartCur                        0x20000100   Data           4  mb.o(.data)
    pvMBFrameStopCur                         0x20000104   Data           4  mb.o(.data)
    peMBFrameReceiveCur                      0x20000108   Data           4  mb.o(.data)
    pvMBFrameCloseCur                        0x2000010c   Data           4  mb.o(.data)
    xFuncHandlers                            0x2000011c   Data         128  mb.o(.data)
    .data                                    0x2000019c   Section      152  mdcb.o(.data)
    .data                                    0x20000234   Section        2  portevent.o(.data)
    eQueuedEvent                             0x20000234   Data           1  portevent.o(.data)
    xEventInQueue                            0x20000235   Data           1  portevent.o(.data)
    .data                                    0x20000236   Section        2  portserial.o(.data)
    xTxEnabled                               0x20000236   Data           1  portserial.o(.data)
    .data                                    0x20000238   Section        8  porttimer.o(.data)
    debug_timer_init_count                   0x20000238   Data           4  porttimer.o(.data)
    timer_count                              0x2000023c   Data           4  porttimer.o(.data)
    .bss                                     0x20000240   Section       40  can.o(.bss)
    .bss                                     0x20000268   Section      168  i2c.o(.bss)
    .bss                                     0x20000310   Section      100  spi.o(.bss)
    .bss                                     0x20000374   Section       76  tim.o(.bss)
    .bss                                     0x200003c0   Section      132  usart.o(.bss)
    .bss                                     0x20000444   Section       76  stm32l4xx_hal_timebase_tim.o(.bss)
    .bss                                     0x20000490   Section       64  queue.o(.bss)
    .bss                                     0x200004d0   Section     1220  tasks.o(.bss)
    pxReadyTasksLists                        0x200004d0   Data        1120  tasks.o(.bss)
    xDelayedTaskList1                        0x20000930   Data          20  tasks.o(.bss)
    xDelayedTaskList2                        0x20000944   Data          20  tasks.o(.bss)
    xPendingReadyList                        0x20000958   Data          20  tasks.o(.bss)
    xTasksWaitingTermination                 0x2000096c   Data          20  tasks.o(.bss)
    xSuspendedTaskList                       0x20000980   Data          20  tasks.o(.bss)
    .bss                                     0x20000994   Section      280  timers.o(.bss)
    xStaticTimerQueue                        0x20000994   Data          80  timers.o(.bss)
    ucStaticTimerQueueStorage                0x200009e4   Data         160  timers.o(.bss)
    xActiveTimerList1                        0x20000a84   Data          20  timers.o(.bss)
    xActiveTimerList2                        0x20000a98   Data          20  timers.o(.bss)
    .bss                                     0x20000aac   Section     1720  cmsis_os2.o(.bss)
    Idle_TCB                                 0x20000aac   Data          92  cmsis_os2.o(.bss)
    Idle_Stack                               0x20000b08   Data         512  cmsis_os2.o(.bss)
    Timer_TCB                                0x20000d08   Data          92  cmsis_os2.o(.bss)
    Timer_Stack                              0x20000d64   Data        1024  cmsis_os2.o(.bss)
    .bss                                     0x20001164   Section     8192  heap_4.o(.bss)
    ucHeap                                   0x20001164   Data        8192  heap_4.o(.bss)
    .bss                                     0x20003164   Section       32  mbfuncother.o(.bss)
    ucMBSlaveID                              0x20003164   Data          32  mbfuncother.o(.bss)
    .bss                                     0x20003184   Section      256  mbrtu.o(.bss)
    .bss                                     0x20003284   Section       96  libspace.o(.bss)
    HEAP                                     0x200032e8   Section      512  startup_stm32l496xx.o(HEAP)
    Heap_Mem                                 0x200032e8   Data         512  startup_stm32l496xx.o(HEAP)
    STACK                                    0x200034e8   Section     1024  startup_stm32l496xx.o(STACK)
    Stack_Mem                                0x200034e8   Data        1024  startup_stm32l496xx.o(STACK)
    __initial_sp                             0x200038e8   Data           0  startup_stm32l496xx.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OSPACE$ROPI$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __rt_locale                               - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_ctype                             - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_numeric                           - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_Size                           0x000001ac   Number         0  startup_stm32l496xx.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32l496xx.o(RESET)
    __Vectors_End                            0x080001ac   Data           0  startup_stm32l496xx.o(RESET)
    __main                                   0x080001ad   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x080001b5   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x080001b5   Thumb Code    44  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x080001b5   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_null                       0x080001c3   Thumb Code     0  __scatter.o(!!!scatter)
    __decompress                             0x080001e9   Thumb Code    90  __dczerorl2.o(!!dczerorl2)
    __decompress1                            0x080001e9   Thumb Code     0  __dczerorl2.o(!!dczerorl2)
    __scatterload_zeroinit                   0x08000245   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    __rt_lib_init                            0x08000261   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_fp_1                       0x08000263   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000001)
    __rt_lib_init_alloca_1                   0x08000267   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_argv_1                     0x08000267   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    __rt_lib_init_atexit_1                   0x08000267   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_clock_1                    0x08000267   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_cpp_1                      0x08000267   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_exceptions_1               0x08000267   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_fp_trap_1                  0x08000267   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_getenv_1                   0x08000267   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_heap_1                     0x08000267   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    __rt_lib_init_lc_collate_1               0x08000267   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_lc_ctype_1                 0x08000267   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_monetary_1              0x08000267   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_numeric_1               0x08000267   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_time_1                  0x08000267   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_preinit_1                  0x08000267   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_rand_1                     0x08000267   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_return                     0x08000267   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000033)
    __rt_lib_init_signal_1                   0x08000267   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_stdio_1                    0x08000267   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_init_user_alloc_1               0x08000267   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_shutdown                        0x08000269   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x0800026b   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_fp_trap_1              0x0800026b   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    __rt_lib_shutdown_heap_1                 0x0800026b   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    __rt_lib_shutdown_return                 0x0800026b   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    __rt_lib_shutdown_signal_1               0x0800026b   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    __rt_lib_shutdown_stdio_1                0x0800026b   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_user_alloc_1           0x0800026b   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_entry                               0x0800026d   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x0800026d   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x0800026d   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x08000273   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x08000273   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x08000277   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x08000277   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x0800027f   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x08000281   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x08000281   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x08000285   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    SVC_Handler                              0x0800028d   Thumb Code    28  port.o(.emb_text)
    __asm___6_port_c_39a90d8d__prvStartFirstTask 0x080002ad   Thumb Code    36  port.o(.emb_text)
    __asm___6_port_c_39a90d8d__prvEnableVFP  0x080002d5   Thumb Code    16  port.o(.emb_text)
    PendSV_Handler                           0x080002e9   Thumb Code    88  port.o(.emb_text)
    vPortGetIPSR                             0x08000345   Thumb Code     6  port.o(.emb_text)
    Reset_Handler                            0x0800036d   Thumb Code     8  startup_stm32l496xx.o(.text)
    ADC1_2_IRQHandler                        0x08000387   Thumb Code     0  startup_stm32l496xx.o(.text)
    ADC3_IRQHandler                          0x08000387   Thumb Code     0  startup_stm32l496xx.o(.text)
    CAN1_RX1_IRQHandler                      0x08000387   Thumb Code     0  startup_stm32l496xx.o(.text)
    CAN1_SCE_IRQHandler                      0x08000387   Thumb Code     0  startup_stm32l496xx.o(.text)
    CAN1_TX_IRQHandler                       0x08000387   Thumb Code     0  startup_stm32l496xx.o(.text)
    CAN2_RX0_IRQHandler                      0x08000387   Thumb Code     0  startup_stm32l496xx.o(.text)
    CAN2_RX1_IRQHandler                      0x08000387   Thumb Code     0  startup_stm32l496xx.o(.text)
    CAN2_SCE_IRQHandler                      0x08000387   Thumb Code     0  startup_stm32l496xx.o(.text)
    CAN2_TX_IRQHandler                       0x08000387   Thumb Code     0  startup_stm32l496xx.o(.text)
    COMP_IRQHandler                          0x08000387   Thumb Code     0  startup_stm32l496xx.o(.text)
    CRS_IRQHandler                           0x08000387   Thumb Code     0  startup_stm32l496xx.o(.text)
    DCMI_IRQHandler                          0x08000387   Thumb Code     0  startup_stm32l496xx.o(.text)
    DFSDM1_FLT0_IRQHandler                   0x08000387   Thumb Code     0  startup_stm32l496xx.o(.text)
    DFSDM1_FLT1_IRQHandler                   0x08000387   Thumb Code     0  startup_stm32l496xx.o(.text)
    DFSDM1_FLT2_IRQHandler                   0x08000387   Thumb Code     0  startup_stm32l496xx.o(.text)
    DFSDM1_FLT3_IRQHandler                   0x08000387   Thumb Code     0  startup_stm32l496xx.o(.text)
    DMA1_Channel1_IRQHandler                 0x08000387   Thumb Code     0  startup_stm32l496xx.o(.text)
    DMA1_Channel2_IRQHandler                 0x08000387   Thumb Code     0  startup_stm32l496xx.o(.text)
    DMA1_Channel3_IRQHandler                 0x08000387   Thumb Code     0  startup_stm32l496xx.o(.text)
    DMA1_Channel4_IRQHandler                 0x08000387   Thumb Code     0  startup_stm32l496xx.o(.text)
    DMA1_Channel5_IRQHandler                 0x08000387   Thumb Code     0  startup_stm32l496xx.o(.text)
    DMA1_Channel6_IRQHandler                 0x08000387   Thumb Code     0  startup_stm32l496xx.o(.text)
    DMA1_Channel7_IRQHandler                 0x08000387   Thumb Code     0  startup_stm32l496xx.o(.text)
    DMA2D_IRQHandler                         0x08000387   Thumb Code     0  startup_stm32l496xx.o(.text)
    DMA2_Channel1_IRQHandler                 0x08000387   Thumb Code     0  startup_stm32l496xx.o(.text)
    DMA2_Channel2_IRQHandler                 0x08000387   Thumb Code     0  startup_stm32l496xx.o(.text)
    DMA2_Channel3_IRQHandler                 0x08000387   Thumb Code     0  startup_stm32l496xx.o(.text)
    DMA2_Channel4_IRQHandler                 0x08000387   Thumb Code     0  startup_stm32l496xx.o(.text)
    DMA2_Channel5_IRQHandler                 0x08000387   Thumb Code     0  startup_stm32l496xx.o(.text)
    DMA2_Channel6_IRQHandler                 0x08000387   Thumb Code     0  startup_stm32l496xx.o(.text)
    DMA2_Channel7_IRQHandler                 0x08000387   Thumb Code     0  startup_stm32l496xx.o(.text)
    EXTI0_IRQHandler                         0x08000387   Thumb Code     0  startup_stm32l496xx.o(.text)
    EXTI15_10_IRQHandler                     0x08000387   Thumb Code     0  startup_stm32l496xx.o(.text)
    EXTI3_IRQHandler                         0x08000387   Thumb Code     0  startup_stm32l496xx.o(.text)
    EXTI4_IRQHandler                         0x08000387   Thumb Code     0  startup_stm32l496xx.o(.text)
    EXTI9_5_IRQHandler                       0x08000387   Thumb Code     0  startup_stm32l496xx.o(.text)
    FLASH_IRQHandler                         0x08000387   Thumb Code     0  startup_stm32l496xx.o(.text)
    FMC_IRQHandler                           0x08000387   Thumb Code     0  startup_stm32l496xx.o(.text)
    FPU_IRQHandler                           0x08000387   Thumb Code     0  startup_stm32l496xx.o(.text)
    I2C1_ER_IRQHandler                       0x08000387   Thumb Code     0  startup_stm32l496xx.o(.text)
    I2C1_EV_IRQHandler                       0x08000387   Thumb Code     0  startup_stm32l496xx.o(.text)
    I2C2_ER_IRQHandler                       0x08000387   Thumb Code     0  startup_stm32l496xx.o(.text)
    I2C2_EV_IRQHandler                       0x08000387   Thumb Code     0  startup_stm32l496xx.o(.text)
    I2C3_ER_IRQHandler                       0x08000387   Thumb Code     0  startup_stm32l496xx.o(.text)
    I2C3_EV_IRQHandler                       0x08000387   Thumb Code     0  startup_stm32l496xx.o(.text)
    I2C4_ER_IRQHandler                       0x08000387   Thumb Code     0  startup_stm32l496xx.o(.text)
    I2C4_EV_IRQHandler                       0x08000387   Thumb Code     0  startup_stm32l496xx.o(.text)
    LCD_IRQHandler                           0x08000387   Thumb Code     0  startup_stm32l496xx.o(.text)
    LPTIM1_IRQHandler                        0x08000387   Thumb Code     0  startup_stm32l496xx.o(.text)
    LPTIM2_IRQHandler                        0x08000387   Thumb Code     0  startup_stm32l496xx.o(.text)
    LPUART1_IRQHandler                       0x08000387   Thumb Code     0  startup_stm32l496xx.o(.text)
    OTG_FS_IRQHandler                        0x08000387   Thumb Code     0  startup_stm32l496xx.o(.text)
    PVD_PVM_IRQHandler                       0x08000387   Thumb Code     0  startup_stm32l496xx.o(.text)
    QUADSPI_IRQHandler                       0x08000387   Thumb Code     0  startup_stm32l496xx.o(.text)
    RCC_IRQHandler                           0x08000387   Thumb Code     0  startup_stm32l496xx.o(.text)
    RNG_IRQHandler                           0x08000387   Thumb Code     0  startup_stm32l496xx.o(.text)
    RTC_Alarm_IRQHandler                     0x08000387   Thumb Code     0  startup_stm32l496xx.o(.text)
    RTC_WKUP_IRQHandler                      0x08000387   Thumb Code     0  startup_stm32l496xx.o(.text)
    SAI1_IRQHandler                          0x08000387   Thumb Code     0  startup_stm32l496xx.o(.text)
    SAI2_IRQHandler                          0x08000387   Thumb Code     0  startup_stm32l496xx.o(.text)
    SDMMC1_IRQHandler                        0x08000387   Thumb Code     0  startup_stm32l496xx.o(.text)
    SPI1_IRQHandler                          0x08000387   Thumb Code     0  startup_stm32l496xx.o(.text)
    SPI2_IRQHandler                          0x08000387   Thumb Code     0  startup_stm32l496xx.o(.text)
    SPI3_IRQHandler                          0x08000387   Thumb Code     0  startup_stm32l496xx.o(.text)
    SWPMI1_IRQHandler                        0x08000387   Thumb Code     0  startup_stm32l496xx.o(.text)
    TAMP_STAMP_IRQHandler                    0x08000387   Thumb Code     0  startup_stm32l496xx.o(.text)
    TIM1_BRK_TIM15_IRQHandler                0x08000387   Thumb Code     0  startup_stm32l496xx.o(.text)
    TIM1_CC_IRQHandler                       0x08000387   Thumb Code     0  startup_stm32l496xx.o(.text)
    TIM1_TRG_COM_TIM17_IRQHandler            0x08000387   Thumb Code     0  startup_stm32l496xx.o(.text)
    TIM2_IRQHandler                          0x08000387   Thumb Code     0  startup_stm32l496xx.o(.text)
    TIM3_IRQHandler                          0x08000387   Thumb Code     0  startup_stm32l496xx.o(.text)
    TIM4_IRQHandler                          0x08000387   Thumb Code     0  startup_stm32l496xx.o(.text)
    TIM5_IRQHandler                          0x08000387   Thumb Code     0  startup_stm32l496xx.o(.text)
    TIM7_IRQHandler                          0x08000387   Thumb Code     0  startup_stm32l496xx.o(.text)
    TIM8_BRK_IRQHandler                      0x08000387   Thumb Code     0  startup_stm32l496xx.o(.text)
    TIM8_CC_IRQHandler                       0x08000387   Thumb Code     0  startup_stm32l496xx.o(.text)
    TIM8_TRG_COM_IRQHandler                  0x08000387   Thumb Code     0  startup_stm32l496xx.o(.text)
    TIM8_UP_IRQHandler                       0x08000387   Thumb Code     0  startup_stm32l496xx.o(.text)
    TSC_IRQHandler                           0x08000387   Thumb Code     0  startup_stm32l496xx.o(.text)
    UART4_IRQHandler                         0x08000387   Thumb Code     0  startup_stm32l496xx.o(.text)
    UART5_IRQHandler                         0x08000387   Thumb Code     0  startup_stm32l496xx.o(.text)
    USART2_IRQHandler                        0x08000387   Thumb Code     0  startup_stm32l496xx.o(.text)
    USART3_IRQHandler                        0x08000387   Thumb Code     0  startup_stm32l496xx.o(.text)
    WWDG_IRQHandler                          0x08000387   Thumb Code     0  startup_stm32l496xx.o(.text)
    __user_initial_stackheap                 0x08000389   Thumb Code     0  startup_stm32l496xx.o(.text)
    __aeabi_uldivmod                         0x080003ad   Thumb Code     0  lludivv7m.o(.text)
    _ll_udiv                                 0x080003ad   Thumb Code   238  lludivv7m.o(.text)
    __aeabi_memcpy                           0x0800049b   Thumb Code     0  rt_memcpy_v6.o(.text)
    __rt_memcpy                              0x0800049b   Thumb Code   138  rt_memcpy_v6.o(.text)
    _memcpy_lastbytes                        0x08000501   Thumb Code     0  rt_memcpy_v6.o(.text)
    __aeabi_memset                           0x08000525   Thumb Code    16  aeabi_memset.o(.text)
    __aeabi_memclr                           0x08000535   Thumb Code     0  rt_memclr.o(.text)
    __rt_memclr                              0x08000535   Thumb Code    68  rt_memclr.o(.text)
    _memset                                  0x08000539   Thumb Code     0  rt_memclr.o(.text)
    __aeabi_memclr4                          0x08000579   Thumb Code     0  rt_memclr_w.o(.text)
    __aeabi_memclr8                          0x08000579   Thumb Code     0  rt_memclr_w.o(.text)
    __rt_memclr_w                            0x08000579   Thumb Code    78  rt_memclr_w.o(.text)
    _memset_w                                0x0800057d   Thumb Code     0  rt_memclr_w.o(.text)
    __use_two_region_memory                  0x080005c7   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x080005c9   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x080005cb   Thumb Code     2  heapauxi.o(.text)
    __aeabi_memcpy4                          0x080005cd   Thumb Code     0  rt_memcpy_w.o(.text)
    __aeabi_memcpy8                          0x080005cd   Thumb Code     0  rt_memcpy_w.o(.text)
    __rt_memcpy_w                            0x080005cd   Thumb Code   100  rt_memcpy_w.o(.text)
    _memcpy_lastbytes_aligned                0x08000615   Thumb Code     0  rt_memcpy_w.o(.text)
    __user_setup_stackheap                   0x08000631   Thumb Code    74  sys_stackheap_outer.o(.text)
    exit                                     0x0800067b   Thumb Code    18  exit.o(.text)
    __user_libspace                          0x0800068d   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x0800068d   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x0800068d   Thumb Code     0  libspace.o(.text)
    _sys_exit                                0x08000695   Thumb Code     8  sys_exit.o(.text)
    __I$use$semihosting                      0x080006a1   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x080006a1   Thumb Code     2  use_no_semi.o(.text)
    BusFault_Handler                         0x080006a3   Thumb Code     2  stm32l4xx_it.o(i.BusFault_Handler)
    __semihosting_library_function           0x080006a3   Thumb Code     0  indicate_semi.o(.text)
    CAN1_RX0_IRQHandler                      0x080006a5   Thumb Code     6  stm32l4xx_it.o(i.CAN1_RX0_IRQHandler)
    CANComTask                               0x080006b1   Thumb Code     8  freertos.o(i.CANComTask)
    DebugMon_Handler                         0x080006b9   Thumb Code     2  stm32l4xx_it.o(i.DebugMon_Handler)
    EXTI1_IRQHandler                         0x080006bb   Thumb Code     6  stm32l4xx_it.o(i.EXTI1_IRQHandler)
    EXTI2_IRQHandler                         0x080006c1   Thumb Code     6  stm32l4xx_it.o(i.EXTI2_IRQHandler)
    EnergyMeterTask                          0x080006c7   Thumb Code    16  freertos.o(i.EnergyMeterTask)
    Error_Handler                            0x080006d7   Thumb Code     4  main.o(i.Error_Handler)
    HAL_CAN_ErrorCallback                    0x080006db   Thumb Code     2  stm32l4xx_hal_can.o(i.HAL_CAN_ErrorCallback)
    HAL_CAN_IRQHandler                       0x080006dd   Thumb Code   508  stm32l4xx_hal_can.o(i.HAL_CAN_IRQHandler)
    HAL_CAN_Init                             0x080008df   Thumb Code   338  stm32l4xx_hal_can.o(i.HAL_CAN_Init)
    HAL_CAN_MspInit                          0x08000a31   Thumb Code   106  can.o(i.HAL_CAN_MspInit)
    HAL_CAN_RxFifo0FullCallback              0x08000aa5   Thumb Code     2  stm32l4xx_hal_can.o(i.HAL_CAN_RxFifo0FullCallback)
    HAL_CAN_RxFifo0MsgPendingCallback        0x08000aa7   Thumb Code     2  stm32l4xx_hal_can.o(i.HAL_CAN_RxFifo0MsgPendingCallback)
    HAL_CAN_RxFifo1FullCallback              0x08000aa9   Thumb Code     2  stm32l4xx_hal_can.o(i.HAL_CAN_RxFifo1FullCallback)
    HAL_CAN_RxFifo1MsgPendingCallback        0x08000aab   Thumb Code     2  stm32l4xx_hal_can.o(i.HAL_CAN_RxFifo1MsgPendingCallback)
    HAL_CAN_SleepCallback                    0x08000aad   Thumb Code     2  stm32l4xx_hal_can.o(i.HAL_CAN_SleepCallback)
    HAL_CAN_TxMailbox0AbortCallback          0x08000aaf   Thumb Code     2  stm32l4xx_hal_can.o(i.HAL_CAN_TxMailbox0AbortCallback)
    HAL_CAN_TxMailbox0CompleteCallback       0x08000ab1   Thumb Code     2  stm32l4xx_hal_can.o(i.HAL_CAN_TxMailbox0CompleteCallback)
    HAL_CAN_TxMailbox1AbortCallback          0x08000ab3   Thumb Code     2  stm32l4xx_hal_can.o(i.HAL_CAN_TxMailbox1AbortCallback)
    HAL_CAN_TxMailbox1CompleteCallback       0x08000ab5   Thumb Code     2  stm32l4xx_hal_can.o(i.HAL_CAN_TxMailbox1CompleteCallback)
    HAL_CAN_TxMailbox2AbortCallback          0x08000ab7   Thumb Code     2  stm32l4xx_hal_can.o(i.HAL_CAN_TxMailbox2AbortCallback)
    HAL_CAN_TxMailbox2CompleteCallback       0x08000ab9   Thumb Code     2  stm32l4xx_hal_can.o(i.HAL_CAN_TxMailbox2CompleteCallback)
    HAL_CAN_WakeUpFromRxMsgCallback          0x08000abb   Thumb Code     2  stm32l4xx_hal_can.o(i.HAL_CAN_WakeUpFromRxMsgCallback)
    HAL_DMA_Abort                            0x08000abd   Thumb Code    74  stm32l4xx_hal_dma.o(i.HAL_DMA_Abort)
    HAL_DMA_Abort_IT                         0x08000b07   Thumb Code    78  stm32l4xx_hal_dma.o(i.HAL_DMA_Abort_IT)
    HAL_Delay                                0x08000b55   Thumb Code    32  stm32l4xx_hal.o(i.HAL_Delay)
    HAL_GPIO_EXTI_Callback                   0x08000b79   Thumb Code     2  stm32l4xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback)
    HAL_GPIO_EXTI_IRQHandler                 0x08000b7d   Thumb Code    18  stm32l4xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler)
    HAL_GPIO_Init                            0x08000b95   Thumb Code   420  stm32l4xx_hal_gpio.o(i.HAL_GPIO_Init)
    HAL_GPIO_TogglePin                       0x08000d67   Thumb Code    16  stm32l4xx_hal_gpio.o(i.HAL_GPIO_TogglePin)
    HAL_GPIO_WritePin                        0x08000d77   Thumb Code    12  stm32l4xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    HAL_GetTick                              0x08000d85   Thumb Code     6  stm32l4xx_hal.o(i.HAL_GetTick)
    HAL_I2CEx_ConfigAnalogFilter             0x08000d91   Thumb Code    90  stm32l4xx_hal_i2c_ex.o(i.HAL_I2CEx_ConfigAnalogFilter)
    HAL_I2CEx_ConfigDigitalFilter            0x08000deb   Thumb Code    86  stm32l4xx_hal_i2c_ex.o(i.HAL_I2CEx_ConfigDigitalFilter)
    HAL_I2C_Init                             0x08000e41   Thumb Code   186  stm32l4xx_hal_i2c.o(i.HAL_I2C_Init)
    HAL_I2C_MspInit                          0x08000f01   Thumb Code   208  i2c.o(i.HAL_I2C_MspInit)
    HAL_IncTick                              0x08000fe1   Thumb Code    12  stm32l4xx_hal.o(i.HAL_IncTick)
    HAL_Init                                 0x08000ff1   Thumb Code    30  stm32l4xx_hal.o(i.HAL_Init)
    HAL_InitTick                             0x08001011   Thumb Code    98  stm32l4xx_hal_timebase_tim.o(i.HAL_InitTick)
    HAL_MspInit                              0x08001085   Thumb Code    48  stm32l4xx_hal_msp.o(i.HAL_MspInit)
    HAL_NVIC_ClearPendingIRQ                 0x080010b9   Thumb Code    26  stm32l4xx_hal_cortex.o(i.HAL_NVIC_ClearPendingIRQ)
    HAL_NVIC_EnableIRQ                       0x080010d3   Thumb Code    26  stm32l4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    HAL_NVIC_SetPriority                     0x080010ed   Thumb Code    60  stm32l4xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    HAL_NVIC_SetPriorityGrouping             0x0800112d   Thumb Code    26  stm32l4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    HAL_PWREx_ControlVoltageScaling          0x08001151   Thumb Code    96  stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling)
    HAL_PWREx_GetVoltageRange                0x080011bd   Thumb Code    10  stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_GetVoltageRange)
    HAL_RCCEx_PeriphCLKConfig                0x080011cd   Thumb Code   962  stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig)
    HAL_RCC_ClockConfig                      0x080015a1   Thumb Code   346  stm32l4xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    HAL_RCC_GetClockConfig                   0x08001711   Thumb Code    52  stm32l4xx_hal_rcc.o(i.HAL_RCC_GetClockConfig)
    HAL_RCC_GetHCLKFreq                      0x0800174d   Thumb Code     6  stm32l4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq)
    HAL_RCC_GetPCLK1Freq                     0x08001759   Thumb Code    26  stm32l4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    HAL_RCC_GetPCLK2Freq                     0x0800177d   Thumb Code    26  stm32l4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    HAL_RCC_GetSysClockFreq                  0x080017a1   Thumb Code   150  stm32l4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    HAL_RCC_OscConfig                        0x08001845   Thumb Code  1466  stm32l4xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    HAL_SPI_Init                             0x08001e09   Thumb Code   226  stm32l4xx_hal_spi.o(i.HAL_SPI_Init)
    HAL_SPI_MspInit                          0x08001eed   Thumb Code    90  spi.o(i.HAL_SPI_MspInit)
    HAL_SPI_Receive                          0x08001f55   Thumb Code   390  stm32l4xx_hal_spi.o(i.HAL_SPI_Receive)
    HAL_SPI_Transmit                         0x080020e1   Thumb Code   444  stm32l4xx_hal_spi.o(i.HAL_SPI_Transmit)
    HAL_SPI_TransmitReceive                  0x080022a3   Thumb Code   620  stm32l4xx_hal_spi.o(i.HAL_SPI_TransmitReceive)
    HAL_TIMEx_Break2Callback                 0x08002515   Thumb Code     2  stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_Break2Callback)
    HAL_TIMEx_BreakCallback                  0x08002517   Thumb Code     2  stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback)
    HAL_TIMEx_CommutCallback                 0x08002519   Thumb Code     2  stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback)
    HAL_TIM_Base_Init                        0x0800251b   Thumb Code    78  stm32l4xx_hal_tim.o(i.HAL_TIM_Base_Init)
    HAL_TIM_Base_MspInit                     0x08002569   Thumb Code    50  tim.o(i.HAL_TIM_Base_MspInit)
    HAL_TIM_Base_Start_IT                    0x080025a5   Thumb Code   110  stm32l4xx_hal_tim.o(i.HAL_TIM_Base_Start_IT)
    HAL_TIM_IC_CaptureCallback               0x08002631   Thumb Code     2  stm32l4xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback)
    HAL_TIM_IRQHandler                       0x08002633   Thumb Code   396  stm32l4xx_hal_tim.o(i.HAL_TIM_IRQHandler)
    HAL_TIM_OC_DelayElapsedCallback          0x080027bf   Thumb Code     2  stm32l4xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback)
    HAL_TIM_PWM_PulseFinishedCallback        0x080027c1   Thumb Code     2  stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback)
    HAL_TIM_PeriodElapsedCallback            0x080027c5   Thumb Code    14  main.o(i.HAL_TIM_PeriodElapsedCallback)
    HAL_TIM_TriggerCallback                  0x080027d9   Thumb Code     2  stm32l4xx_hal_tim.o(i.HAL_TIM_TriggerCallback)
    HAL_UARTEx_RxEventCallback               0x080027db   Thumb Code     2  stm32l4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback)
    HAL_UARTEx_WakeupCallback                0x080027dd   Thumb Code     2  stm32l4xx_hal_uart_ex.o(i.HAL_UARTEx_WakeupCallback)
    HAL_UART_ErrorCallback                   0x080027e1   Thumb Code    36  portserial.o(i.HAL_UART_ErrorCallback)
    HAL_UART_IRQHandler                      0x0800280d   Thumb Code   666  stm32l4xx_hal_uart.o(i.HAL_UART_IRQHandler)
    HAL_UART_Init                            0x08002ab1   Thumb Code   108  stm32l4xx_hal_uart.o(i.HAL_UART_Init)
    HAL_UART_MspInit                         0x08002b1d   Thumb Code   134  usart.o(i.HAL_UART_MspInit)
    HAL_UART_TxCpltCallback                  0x08002bad   Thumb Code    22  portserial.o(i.HAL_UART_TxCpltCallback)
    HardFault_Handler                        0x08002bcd   Thumb Code     2  stm32l4xx_it.o(i.HardFault_Handler)
    MX_CAN1_Init                             0x08002bd1   Thumb Code    66  can.o(i.MX_CAN1_Init)
    MX_FREERTOS_Init                         0x08002c1d   Thumb Code    74  freertos.o(i.MX_FREERTOS_Init)
    MX_GPIO_Init                             0x08002c85   Thumb Code   258  gpio.o(i.MX_GPIO_Init)
    MX_I2C1_Init                             0x08002d95   Thumb Code    76  i2c.o(i.MX_I2C1_Init)
    MX_I2C2_Init                             0x08002ded   Thumb Code    76  i2c.o(i.MX_I2C2_Init)
    MX_SPI2_Init                             0x08002e45   Thumb Code    72  spi.o(i.MX_SPI2_Init)
    MX_TIM16_Init                            0x08002e95   Thumb Code    48  tim.o(i.MX_TIM16_Init)
    MX_USART1_UART_Init                      0x08002ecd   Thumb Code    56  usart.o(i.MX_USART1_UART_Init)
    MemManage_Handler                        0x08002f0d   Thumb Code     2  stm32l4xx_it.o(i.MemManage_Handler)
    ModbusComTask                            0x08002f11   Thumb Code   184  freertos.o(i.ModbusComTask)
    Modbus_Test_Get_Holding_Regs             0x08002fd1   Thumb Code     4  modbus_test.o(i.Modbus_Test_Get_Holding_Regs)
    Modbus_Test_Inc_Callback_Count           0x08002fd9   Thumb Code    10  modbus_test.o(i.Modbus_Test_Inc_Callback_Count)
    Modbus_Test_Inc_Poll_Count               0x08002fe9   Thumb Code    10  modbus_test.o(i.Modbus_Test_Inc_Poll_Count)
    Modbus_Test_Inc_Rx_Count                 0x08002ff9   Thumb Code    10  modbus_test.o(i.Modbus_Test_Inc_Rx_Count)
    Modbus_Test_Inc_Timer_Count              0x08003009   Thumb Code    10  modbus_test.o(i.Modbus_Test_Inc_Timer_Count)
    Modbus_Test_Inc_Tx_Count                 0x08003019   Thumb Code    10  modbus_test.o(i.Modbus_Test_Inc_Tx_Count)
    Modbus_Test_Init                         0x08003029   Thumb Code    42  modbus_test.o(i.Modbus_Test_Init)
    Modbus_Test_LED_Indicate                 0x08003059   Thumb Code    52  modbus_test.o(i.Modbus_Test_LED_Indicate)
    Modbus_Test_Update_Registers             0x08003091   Thumb Code    54  modbus_test.o(i.Modbus_Test_Update_Registers)
    NMI_Handler                              0x080030cd   Thumb Code     2  stm32l4xx_it.o(i.NMI_Handler)
    RN7326_ReadReg                           0x080033b1   Thumb Code   230  rn7326.o(i.RN7326_ReadReg)
    RS485_Enter_RX_Mode                      0x080034a1   Thumb Code    36  rs485.o(i.RS485_Enter_RX_Mode)
    RS485_Enter_TX_Mode                      0x080034c5   Thumb Code    34  rs485.o(i.RS485_Enter_TX_Mode)
    RS485_Init                               0x080034e7   Thumb Code    56  rs485.o(i.RS485_Init)
    SensorsTask                              0x080037a5   Thumb Code     8  freertos.o(i.SensorsTask)
    SysMonitorTask                           0x080037ad   Thumb Code     8  freertos.o(i.SysMonitorTask)
    SysTick_Handler                          0x080037b5   Thumb Code    26  cmsis_os2.o(i.SysTick_Handler)
    SystemClock_Config                       0x080037cf   Thumb Code   114  main.o(i.SystemClock_Config)
    SystemInit                               0x08003841   Thumb Code    12  system_stm32l4xx.o(i.SystemInit)
    TIM1_UP_TIM16_IRQHandler                 0x08003851   Thumb Code    38  stm32l4xx_it.o(i.TIM1_UP_TIM16_IRQHandler)
    TIM6_DAC_IRQHandler                      0x0800387d   Thumb Code     6  stm32l4xx_it.o(i.TIM6_DAC_IRQHandler)
    TIM_Base_SetConfig                       0x08003889   Thumb Code   160  stm32l4xx_hal_tim.o(i.TIM_Base_SetConfig)
    UART_AdvFeatureConfig                    0x0800394f   Thumb Code   200  stm32l4xx_hal_uart.o(i.UART_AdvFeatureConfig)
    UART_CheckIdleState                      0x08003a17   Thumb Code    96  stm32l4xx_hal_uart.o(i.UART_CheckIdleState)
    UART_SetConfig                           0x08003af9   Thumb Code   662  stm32l4xx_hal_uart.o(i.UART_SetConfig)
    UART_WaitOnFlagUntilTimeout              0x08003db9   Thumb Code   190  stm32l4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout)
    USART1_IRQHandler                        0x08003e79   Thumb Code    80  stm32l4xx_it.o(i.USART1_IRQHandler)
    UsageFault_Handler                       0x08003ed1   Thumb Code     2  stm32l4xx_it.o(i.UsageFault_Handler)
    eMBEnable                                0x08003ef5   Thumb Code    28  mb.o(i.eMBEnable)
    eMBFuncReadCoils                         0x08003f15   Thumb Code   124  mbfunccoils.o(i.eMBFuncReadCoils)
    eMBFuncReadDiscreteInputs                0x08003f91   Thumb Code   120  mbfuncdisc.o(i.eMBFuncReadDiscreteInputs)
    eMBFuncReadHoldingRegister               0x08004009   Thumb Code   102  mbfuncholding.o(i.eMBFuncReadHoldingRegister)
    eMBFuncReadInputRegister                 0x0800406f   Thumb Code   100  mbfuncinput.o(i.eMBFuncReadInputRegister)
    eMBFuncReadWriteMultipleHoldingRegister  0x080040d3   Thumb Code   156  mbfuncholding.o(i.eMBFuncReadWriteMultipleHoldingRegister)
    eMBFuncReportSlaveID                     0x08004171   Thumb Code    26  mbfuncother.o(i.eMBFuncReportSlaveID)
    eMBFuncWriteCoil                         0x08004195   Thumb Code    90  mbfunccoils.o(i.eMBFuncWriteCoil)
    eMBFuncWriteHoldingRegister              0x080041ef   Thumb Code    50  mbfuncholding.o(i.eMBFuncWriteHoldingRegister)
    eMBFuncWriteMultipleCoils                0x08004221   Thumb Code   102  mbfunccoils.o(i.eMBFuncWriteMultipleCoils)
    eMBFuncWriteMultipleHoldingRegister      0x08004287   Thumb Code    82  mbfuncholding.o(i.eMBFuncWriteMultipleHoldingRegister)
    eMBInit                                  0x080042d9   Thumb Code   102  mb.o(i.eMBInit)
    eMBPoll                                  0x08004361   Thumb Code   174  mb.o(i.eMBPoll)
    eMBRTUInit                               0x08004415   Thumb Code    68  mbrtu.o(i.eMBRTUInit)
    eMBRTUReceive                            0x0800445d   Thumb Code    64  mbrtu.o(i.eMBRTUReceive)
    eMBRTUSend                               0x080044a5   Thumb Code   180  mbrtu.o(i.eMBRTUSend)
    eMBRTUStart                              0x08004561   Thumb Code    26  mbrtu.o(i.eMBRTUStart)
    eMBRTUStop                               0x08004581   Thumb Code    20  mbrtu.o(i.eMBRTUStop)
    eMBRegCoilsCB                            0x08004595   Thumb Code   110  mdcb.o(i.eMBRegCoilsCB)
    eMBRegDiscreteCB                         0x08004609   Thumb Code    72  mdcb.o(i.eMBRegDiscreteCB)
    eMBRegHoldingCB                          0x08004655   Thumb Code   132  mdcb.o(i.eMBRegHoldingCB)
    eMBRegInputCB                            0x080046dd   Thumb Code    56  mdcb.o(i.eMBRegInputCB)
    main                                     0x08004719   Thumb Code    50  main.o(i.main)
    osDelay                                  0x0800474b   Thumb Code    28  cmsis_os2.o(i.osDelay)
    osKernelInitialize                       0x08004769   Thumb Code    34  cmsis_os2.o(i.osKernelInitialize)
    osKernelStart                            0x08004791   Thumb Code    48  cmsis_os2.o(i.osKernelStart)
    osThreadNew                              0x080047c9   Thumb Code   186  cmsis_os2.o(i.osThreadNew)
    prveMBError2Exception                    0x0800509f   Thumb Code    28  mbutils.o(i.prveMBError2Exception)
    prvvTIMERExpiredISR                      0x080050bd   Thumb Code    46  porttimer.o(i.prvvTIMERExpiredISR)
    prvvUARTRxISR                            0x080050f9   Thumb Code    16  portserial.o(i.prvvUARTRxISR)
    prvvUARTTxReadyISR                       0x0800510d   Thumb Code    16  portserial.o(i.prvvUARTTxReadyISR)
    pvPortMalloc                             0x08005121   Thumb Code   214  heap_4.o(i.pvPortMalloc)
    pxPortInitialiseStack                    0x080051fd   Thumb Code    30  port.o(i.pxPortInitialiseStack)
    test_read_LostVoltage                    0x08005221   Thumb Code    28  rn7326.o(i.test_read_LostVoltage)
    test_read_voltage                        0x08005241   Thumb Code    30  rn7326.o(i.test_read_voltage)
    usMBCRC16                                0x08005265   Thumb Code    38  mbcrc.o(i.usMBCRC16)
    uxListRemove                             0x08005291   Thumb Code    38  list.o(i.uxListRemove)
    vApplicationGetIdleTaskMemory            0x080052b9   Thumb Code    16  cmsis_os2.o(i.vApplicationGetIdleTaskMemory)
    vApplicationGetTimerTaskMemory           0x080052cd   Thumb Code    18  cmsis_os2.o(i.vApplicationGetTimerTaskMemory)
    vListInitialise                          0x080052e5   Thumb Code    22  list.o(i.vListInitialise)
    vListInitialiseItem                      0x080052fb   Thumb Code     6  list.o(i.vListInitialiseItem)
    vListInsert                              0x08005301   Thumb Code    48  list.o(i.vListInsert)
    vListInsertEnd                           0x08005331   Thumb Code    24  list.o(i.vListInsertEnd)
    vMBPortSerialEnable                      0x08005349   Thumb Code    76  portserial.o(i.vMBPortSerialEnable)
    vMBPortTimersDisable                     0x0800539d   Thumb Code    56  porttimer.o(i.vMBPortTimersDisable)
    vMBPortTimersEnable                      0x080053d9   Thumb Code    38  porttimer.o(i.vMBPortTimersEnable)
    vPortEnterCritical                       0x08005405   Thumb Code    54  port.o(i.vPortEnterCritical)
    vPortExitCritical                        0x08005445   Thumb Code    38  port.o(i.vPortExitCritical)
    vPortFree                                0x08005471   Thumb Code    94  heap_4.o(i.vPortFree)
    vPortSetupTimerInterrupt                 0x080054d5   Thumb Code    32  port.o(i.vPortSetupTimerInterrupt)
    vPortValidateInterruptPriority           0x080054f9   Thumb Code    74  port.o(i.vPortValidateInterruptPriority)
    vQueueAddToRegistry                      0x0800554d   Thumb Code    32  queue.o(i.vQueueAddToRegistry)
    vQueueWaitForMessageRestricted           0x08005571   Thumb Code    70  queue.o(i.vQueueWaitForMessageRestricted)
    vTaskDelay                               0x080055b9   Thumb Code    70  tasks.o(i.vTaskDelay)
    vTaskInternalSetTimeOutState             0x08005609   Thumb Code    12  tasks.o(i.vTaskInternalSetTimeOutState)
    vTaskMissedYield                         0x08005619   Thumb Code     8  tasks.o(i.vTaskMissedYield)
    vTaskPlaceOnEventList                    0x08005625   Thumb Code    46  tasks.o(i.vTaskPlaceOnEventList)
    vTaskPlaceOnEventListRestricted          0x08005659   Thumb Code    54  tasks.o(i.vTaskPlaceOnEventListRestricted)
    vTaskStartScheduler                      0x08005695   Thumb Code   126  tasks.o(i.vTaskStartScheduler)
    vTaskSuspendAll                          0x08005725   Thumb Code    10  tasks.o(i.vTaskSuspendAll)
    vTaskSwitchContext                       0x08005735   Thumb Code    94  tasks.o(i.vTaskSwitchContext)
    xMBPortEventGet                          0x0800579d   Thumb Code    24  portevent.o(i.xMBPortEventGet)
    xMBPortEventInit                         0x080057b9   Thumb Code    10  portevent.o(i.xMBPortEventInit)
    xMBPortEventPost                         0x080057c9   Thumb Code    12  portevent.o(i.xMBPortEventPost)
    xMBPortSerialGetByte                     0x080057d9   Thumb Code    10  portserial.o(i.xMBPortSerialGetByte)
    xMBPortSerialInit                        0x080057e9   Thumb Code     4  portserial.o(i.xMBPortSerialInit)
    xMBPortSerialPutByte                     0x080057ed   Thumb Code    10  portserial.o(i.xMBPortSerialPutByte)
    xMBPortTimersInit                        0x080057fd   Thumb Code    78  porttimer.o(i.xMBPortTimersInit)
    xMBRTUReceiveFSM                         0x08005855   Thumb Code   102  mbrtu.o(i.xMBRTUReceiveFSM)
    xMBRTUTimerT35Expired                    0x080058c5   Thumb Code    48  mbrtu.o(i.xMBRTUTimerT35Expired)
    xMBRTUTransmitFSM                        0x080058f9   Thumb Code    94  mbrtu.o(i.xMBRTUTransmitFSM)
    xMBUtilGetBits                           0x0800595d   Thumb Code    34  mbutils.o(i.xMBUtilGetBits)
    xMBUtilSetBits                           0x0800597f   Thumb Code    42  mbutils.o(i.xMBUtilSetBits)
    xPortStartScheduler                      0x080059a9   Thumb Code   222  port.o(i.xPortStartScheduler)
    xPortSysTickHandler                      0x08005a9d   Thumb Code    38  port.o(i.xPortSysTickHandler)
    xQueueGenericCreateStatic                0x08005ac9   Thumb Code   108  queue.o(i.xQueueGenericCreateStatic)
    xQueueGenericReset                       0x08005b35   Thumb Code   134  queue.o(i.xQueueGenericReset)
    xQueueGenericSend                        0x08005bc1   Thumb Code   352  queue.o(i.xQueueGenericSend)
    xQueueGenericSendFromISR                 0x08005d25   Thumb Code   192  queue.o(i.xQueueGenericSendFromISR)
    xQueueReceive                            0x08005de5   Thumb Code   310  queue.o(i.xQueueReceive)
    xTaskCheckForTimeOut                     0x08005f21   Thumb Code   118  tasks.o(i.xTaskCheckForTimeOut)
    xTaskCreate                              0x08005f9d   Thumb Code   100  tasks.o(i.xTaskCreate)
    xTaskCreateStatic                        0x08006007   Thumb Code    92  tasks.o(i.xTaskCreateStatic)
    xTaskGetSchedulerState                   0x08006065   Thumb Code    24  tasks.o(i.xTaskGetSchedulerState)
    xTaskGetTickCount                        0x08006081   Thumb Code     6  tasks.o(i.xTaskGetTickCount)
    xTaskIncrementTick                       0x0800608d   Thumb Code   196  tasks.o(i.xTaskIncrementTick)
    xTaskPriorityDisinherit                  0x08006159   Thumb Code   116  tasks.o(i.xTaskPriorityDisinherit)
    xTaskRemoveFromEventList                 0x080061d5   Thumb Code   106  tasks.o(i.xTaskRemoveFromEventList)
    xTaskResumeAll                           0x0800624d   Thumb Code   184  tasks.o(i.xTaskResumeAll)
    xTimerCreateTimerTask                    0x08006315   Thumb Code    86  timers.o(i.xTimerCreateTimerTask)
    xTimerGenericCommand                     0x0800637d   Thumb Code   104  timers.o(i.xTimerGenericCommand)
    _fp_init                                 0x080063e9   Thumb Code    10  fpinit.o(x$fpl$fpinit)
    __fplib_config_fpu_vfp                   0x080063f1   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    __fplib_config_pureend_doubles           0x080063f1   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    energymeterTask_attributes               0x080063f4   Data          36  freertos.o(.constdata)
    modbuscomTask_attributes                 0x08006418   Data          36  freertos.o(.constdata)
    cancomTask_attributes                    0x0800643c   Data          36  freertos.o(.constdata)
    sensorsTask_attributes                   0x08006460   Data          36  freertos.o(.constdata)
    sysmonitorTask_attributes                0x08006484   Data          36  freertos.o(.constdata)
    AHBPrescTable                            0x080064a8   Data          16  system_stm32l4xx.o(.constdata)
    MSIRangeTable                            0x080064b8   Data          48  system_stm32l4xx.o(.constdata)
    APBPrescTable                            0x080064e8   Data           8  system_stm32l4xx.o(.constdata)
    Region$$Table$$Base                      0x08006738   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x08006758   Number         0  anon$$obj.o(Region$$Table)
    energymeterTaskHandle                    0x20000008   Data           4  freertos.o(.data)
    modbuscomTaskHandle                      0x2000000c   Data           4  freertos.o(.data)
    cancomTaskHandle                         0x20000010   Data           4  freertos.o(.data)
    sensorsTaskHandle                        0x20000014   Data           4  freertos.o(.data)
    sysmonitorTaskHandle                     0x20000018   Data           4  freertos.o(.data)
    uwTickFreq                               0x2000001c   Data           1  stm32l4xx_hal.o(.data)
    uwTickPrio                               0x20000020   Data           4  stm32l4xx_hal.o(.data)
    uwTick                                   0x20000024   Data           4  stm32l4xx_hal.o(.data)
    SystemCoreClock                          0x20000028   Data           4  system_stm32l4xx.o(.data)
    pxCurrentTCB                             0x2000002c   Data           4  tasks.o(.data)
    t_V                                      0x200000e0   Data           1  rn7326.o(.data)
    t_L                                      0x200000e1   Data           1  rn7326.o(.data)
    pxMBFrameCBByteReceived                  0x20000110   Data           4  mb.o(.data)
    pxMBFrameCBTransmitterEmpty              0x20000114   Data           4  mb.o(.data)
    pxMBPortCBTimerExpired                   0x20000118   Data           4  mb.o(.data)
    usRegInputStart                          0x2000019c   Data           2  mdcb.o(.data)
    usRegHoldingStart                        0x2000019e   Data           2  mdcb.o(.data)
    ucRegCoilsBuf                            0x200001a0   Data           2  mdcb.o(.data)
    ucRegDiscreteBuf                         0x200001a2   Data           2  mdcb.o(.data)
    usRegInputBuf                            0x200001a4   Data          16  mdcb.o(.data)
    usRegHoldingBuf                          0x200001b4   Data         128  mdcb.o(.data)
    ucRxByte                                 0x20000237   Data           1  portserial.o(.data)
    hcan1                                    0x20000240   Data          40  can.o(.bss)
    hi2c1                                    0x20000268   Data          84  i2c.o(.bss)
    hi2c2                                    0x200002bc   Data          84  i2c.o(.bss)
    hspi2                                    0x20000310   Data         100  spi.o(.bss)
    htim16                                   0x20000374   Data          76  tim.o(.bss)
    huart1                                   0x200003c0   Data         132  usart.o(.bss)
    htim6                                    0x20000444   Data          76  stm32l4xx_hal_timebase_tim.o(.bss)
    xQueueRegistry                           0x20000490   Data          64  queue.o(.bss)
    ucRTUBuf                                 0x20003184   Data         256  mbrtu.o(.bss)
    __libspace_start                         0x20003284   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x200032e4   Data           0  libspace.o(.bss)



==============================================================================

Memory Map of the image

  Image Entry point : 0x080001ad

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00006998, Max: 0x00080000, ABSOLUTE, COMPRESSED[0x000067dc])

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x00006758, Max: 0x00080000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x000001ac   Data   RO            3    RESET               startup_stm32l496xx.o
    0x080001ac   0x080001ac   0x00000008   Code   RO         7687  * !!!main             c_w.l(__main.o)
    0x080001b4   0x080001b4   0x00000034   Code   RO         7858    !!!scatter          c_w.l(__scatter.o)
    0x080001e8   0x080001e8   0x0000005a   Code   RO         7856    !!dczerorl2         c_w.l(__dczerorl2.o)
    0x08000242   0x08000242   0x00000002   PAD
    0x08000244   0x08000244   0x0000001c   Code   RO         7860    !!handler_zi        c_w.l(__scatter_zi.o)
    0x08000260   0x08000260   0x00000002   Code   RO         7726    .ARM.Collect$$libinit$$00000000  c_w.l(libinit.o)
    0x08000262   0x08000262   0x00000004   Code   RO         7738    .ARM.Collect$$libinit$$00000001  c_w.l(libinit2.o)
    0x08000266   0x08000266   0x00000000   Code   RO         7741    .ARM.Collect$$libinit$$00000004  c_w.l(libinit2.o)
    0x08000266   0x08000266   0x00000000   Code   RO         7744    .ARM.Collect$$libinit$$0000000A  c_w.l(libinit2.o)
    0x08000266   0x08000266   0x00000000   Code   RO         7746    .ARM.Collect$$libinit$$0000000C  c_w.l(libinit2.o)
    0x08000266   0x08000266   0x00000000   Code   RO         7748    .ARM.Collect$$libinit$$0000000E  c_w.l(libinit2.o)
    0x08000266   0x08000266   0x00000000   Code   RO         7751    .ARM.Collect$$libinit$$00000011  c_w.l(libinit2.o)
    0x08000266   0x08000266   0x00000000   Code   RO         7753    .ARM.Collect$$libinit$$00000013  c_w.l(libinit2.o)
    0x08000266   0x08000266   0x00000000   Code   RO         7755    .ARM.Collect$$libinit$$00000015  c_w.l(libinit2.o)
    0x08000266   0x08000266   0x00000000   Code   RO         7757    .ARM.Collect$$libinit$$00000017  c_w.l(libinit2.o)
    0x08000266   0x08000266   0x00000000   Code   RO         7759    .ARM.Collect$$libinit$$00000019  c_w.l(libinit2.o)
    0x08000266   0x08000266   0x00000000   Code   RO         7761    .ARM.Collect$$libinit$$0000001B  c_w.l(libinit2.o)
    0x08000266   0x08000266   0x00000000   Code   RO         7763    .ARM.Collect$$libinit$$0000001D  c_w.l(libinit2.o)
    0x08000266   0x08000266   0x00000000   Code   RO         7765    .ARM.Collect$$libinit$$0000001F  c_w.l(libinit2.o)
    0x08000266   0x08000266   0x00000000   Code   RO         7767    .ARM.Collect$$libinit$$00000021  c_w.l(libinit2.o)
    0x08000266   0x08000266   0x00000000   Code   RO         7769    .ARM.Collect$$libinit$$00000023  c_w.l(libinit2.o)
    0x08000266   0x08000266   0x00000000   Code   RO         7771    .ARM.Collect$$libinit$$00000025  c_w.l(libinit2.o)
    0x08000266   0x08000266   0x00000000   Code   RO         7775    .ARM.Collect$$libinit$$0000002C  c_w.l(libinit2.o)
    0x08000266   0x08000266   0x00000000   Code   RO         7777    .ARM.Collect$$libinit$$0000002E  c_w.l(libinit2.o)
    0x08000266   0x08000266   0x00000000   Code   RO         7779    .ARM.Collect$$libinit$$00000030  c_w.l(libinit2.o)
    0x08000266   0x08000266   0x00000000   Code   RO         7781    .ARM.Collect$$libinit$$00000032  c_w.l(libinit2.o)
    0x08000266   0x08000266   0x00000002   Code   RO         7782    .ARM.Collect$$libinit$$00000033  c_w.l(libinit2.o)
    0x08000268   0x08000268   0x00000002   Code   RO         7813    .ARM.Collect$$libshutdown$$00000000  c_w.l(libshutdown.o)
    0x0800026a   0x0800026a   0x00000000   Code   RO         7839    .ARM.Collect$$libshutdown$$00000002  c_w.l(libshutdown2.o)
    0x0800026a   0x0800026a   0x00000000   Code   RO         7841    .ARM.Collect$$libshutdown$$00000004  c_w.l(libshutdown2.o)
    0x0800026a   0x0800026a   0x00000000   Code   RO         7844    .ARM.Collect$$libshutdown$$00000007  c_w.l(libshutdown2.o)
    0x0800026a   0x0800026a   0x00000000   Code   RO         7847    .ARM.Collect$$libshutdown$$0000000A  c_w.l(libshutdown2.o)
    0x0800026a   0x0800026a   0x00000000   Code   RO         7849    .ARM.Collect$$libshutdown$$0000000C  c_w.l(libshutdown2.o)
    0x0800026a   0x0800026a   0x00000000   Code   RO         7852    .ARM.Collect$$libshutdown$$0000000F  c_w.l(libshutdown2.o)
    0x0800026a   0x0800026a   0x00000002   Code   RO         7853    .ARM.Collect$$libshutdown$$00000010  c_w.l(libshutdown2.o)
    0x0800026c   0x0800026c   0x00000000   Code   RO         7689    .ARM.Collect$$rtentry$$00000000  c_w.l(__rtentry.o)
    0x0800026c   0x0800026c   0x00000000   Code   RO         7697    .ARM.Collect$$rtentry$$00000002  c_w.l(__rtentry2.o)
    0x0800026c   0x0800026c   0x00000006   Code   RO         7709    .ARM.Collect$$rtentry$$00000004  c_w.l(__rtentry4.o)
    0x08000272   0x08000272   0x00000000   Code   RO         7699    .ARM.Collect$$rtentry$$00000009  c_w.l(__rtentry2.o)
    0x08000272   0x08000272   0x00000004   Code   RO         7700    .ARM.Collect$$rtentry$$0000000A  c_w.l(__rtentry2.o)
    0x08000276   0x08000276   0x00000000   Code   RO         7702    .ARM.Collect$$rtentry$$0000000C  c_w.l(__rtentry2.o)
    0x08000276   0x08000276   0x00000008   Code   RO         7703    .ARM.Collect$$rtentry$$0000000D  c_w.l(__rtentry2.o)
    0x0800027e   0x0800027e   0x00000002   Code   RO         7730    .ARM.Collect$$rtexit$$00000000  c_w.l(rtexit.o)
    0x08000280   0x08000280   0x00000000   Code   RO         7786    .ARM.Collect$$rtexit$$00000002  c_w.l(rtexit2.o)
    0x08000280   0x08000280   0x00000004   Code   RO         7787    .ARM.Collect$$rtexit$$00000003  c_w.l(rtexit2.o)
    0x08000284   0x08000284   0x00000006   Code   RO         7788    .ARM.Collect$$rtexit$$00000004  c_w.l(rtexit2.o)
    0x0800028a   0x0800028a   0x00000002   PAD
    0x0800028c   0x0800028c   0x000000de   Code   RO         6683    .emb_text           port.o
    0x0800036a   0x0800036a   0x00000002   PAD
    0x0800036c   0x0800036c   0x00000040   Code   RO            4    .text               startup_stm32l496xx.o
    0x080003ac   0x080003ac   0x000000ee   Code   RO         7673    .text               c_w.l(lludivv7m.o)
    0x0800049a   0x0800049a   0x0000008a   Code   RO         7675    .text               c_w.l(rt_memcpy_v6.o)
    0x08000524   0x08000524   0x00000010   Code   RO         7679    .text               c_w.l(aeabi_memset.o)
    0x08000534   0x08000534   0x00000044   Code   RO         7681    .text               c_w.l(rt_memclr.o)
    0x08000578   0x08000578   0x0000004e   Code   RO         7683    .text               c_w.l(rt_memclr_w.o)
    0x080005c6   0x080005c6   0x00000006   Code   RO         7685    .text               c_w.l(heapauxi.o)
    0x080005cc   0x080005cc   0x00000064   Code   RO         7694    .text               c_w.l(rt_memcpy_w.o)
    0x08000630   0x08000630   0x0000004a   Code   RO         7713    .text               c_w.l(sys_stackheap_outer.o)
    0x0800067a   0x0800067a   0x00000012   Code   RO         7715    .text               c_w.l(exit.o)
    0x0800068c   0x0800068c   0x00000008   Code   RO         7727    .text               c_w.l(libspace.o)
    0x08000694   0x08000694   0x0000000c   Code   RO         7783    .text               c_w.l(sys_exit.o)
    0x080006a0   0x080006a0   0x00000002   Code   RO         7802    .text               c_w.l(use_no_semi.o)
    0x080006a2   0x080006a2   0x00000000   Code   RO         7804    .text               c_w.l(indicate_semi.o)
    0x080006a2   0x080006a2   0x00000002   Code   RO          599    i.BusFault_Handler  stm32l4xx_it.o
    0x080006a4   0x080006a4   0x0000000c   Code   RO          600    i.CAN1_RX0_IRQHandler  stm32l4xx_it.o
    0x080006b0   0x080006b0   0x00000008   Code   RO          255    i.CANComTask        freertos.o
    0x080006b8   0x080006b8   0x00000002   Code   RO          601    i.DebugMon_Handler  stm32l4xx_it.o
    0x080006ba   0x080006ba   0x00000006   Code   RO          602    i.EXTI1_IRQHandler  stm32l4xx_it.o
    0x080006c0   0x080006c0   0x00000006   Code   RO          603    i.EXTI2_IRQHandler  stm32l4xx_it.o
    0x080006c6   0x080006c6   0x00000010   Code   RO          256    i.EnergyMeterTask   freertos.o
    0x080006d6   0x080006d6   0x00000004   Code   RO           13    i.Error_Handler     main.o
    0x080006da   0x080006da   0x00000002   Code   RO          762    i.HAL_CAN_ErrorCallback  stm32l4xx_hal_can.o
    0x080006dc   0x080006dc   0x00000202   Code   RO          769    i.HAL_CAN_IRQHandler  stm32l4xx_hal_can.o
    0x080008de   0x080008de   0x00000152   Code   RO          770    i.HAL_CAN_Init      stm32l4xx_hal_can.o
    0x08000a30   0x08000a30   0x00000074   Code   RO          354    i.HAL_CAN_MspInit   can.o
    0x08000aa4   0x08000aa4   0x00000002   Code   RO          777    i.HAL_CAN_RxFifo0FullCallback  stm32l4xx_hal_can.o
    0x08000aa6   0x08000aa6   0x00000002   Code   RO          778    i.HAL_CAN_RxFifo0MsgPendingCallback  stm32l4xx_hal_can.o
    0x08000aa8   0x08000aa8   0x00000002   Code   RO          779    i.HAL_CAN_RxFifo1FullCallback  stm32l4xx_hal_can.o
    0x08000aaa   0x08000aaa   0x00000002   Code   RO          780    i.HAL_CAN_RxFifo1MsgPendingCallback  stm32l4xx_hal_can.o
    0x08000aac   0x08000aac   0x00000002   Code   RO          781    i.HAL_CAN_SleepCallback  stm32l4xx_hal_can.o
    0x08000aae   0x08000aae   0x00000002   Code   RO          784    i.HAL_CAN_TxMailbox0AbortCallback  stm32l4xx_hal_can.o
    0x08000ab0   0x08000ab0   0x00000002   Code   RO          785    i.HAL_CAN_TxMailbox0CompleteCallback  stm32l4xx_hal_can.o
    0x08000ab2   0x08000ab2   0x00000002   Code   RO          786    i.HAL_CAN_TxMailbox1AbortCallback  stm32l4xx_hal_can.o
    0x08000ab4   0x08000ab4   0x00000002   Code   RO          787    i.HAL_CAN_TxMailbox1CompleteCallback  stm32l4xx_hal_can.o
    0x08000ab6   0x08000ab6   0x00000002   Code   RO          788    i.HAL_CAN_TxMailbox2AbortCallback  stm32l4xx_hal_can.o
    0x08000ab8   0x08000ab8   0x00000002   Code   RO          789    i.HAL_CAN_TxMailbox2CompleteCallback  stm32l4xx_hal_can.o
    0x08000aba   0x08000aba   0x00000002   Code   RO          791    i.HAL_CAN_WakeUpFromRxMsgCallback  stm32l4xx_hal_can.o
    0x08000abc   0x08000abc   0x0000004a   Code   RO         2357    i.HAL_DMA_Abort     stm32l4xx_hal_dma.o
    0x08000b06   0x08000b06   0x0000004e   Code   RO         2358    i.HAL_DMA_Abort_IT  stm32l4xx_hal_dma.o
    0x08000b54   0x08000b54   0x00000024   Code   RO          997    i.HAL_Delay         stm32l4xx_hal.o
    0x08000b78   0x08000b78   0x00000002   Code   RO         2291    i.HAL_GPIO_EXTI_Callback  stm32l4xx_hal_gpio.o
    0x08000b7a   0x08000b7a   0x00000002   PAD
    0x08000b7c   0x08000b7c   0x00000018   Code   RO         2292    i.HAL_GPIO_EXTI_IRQHandler  stm32l4xx_hal_gpio.o
    0x08000b94   0x08000b94   0x000001d2   Code   RO         2293    i.HAL_GPIO_Init     stm32l4xx_hal_gpio.o
    0x08000d66   0x08000d66   0x00000010   Code   RO         2296    i.HAL_GPIO_TogglePin  stm32l4xx_hal_gpio.o
    0x08000d76   0x08000d76   0x0000000c   Code   RO         2297    i.HAL_GPIO_WritePin  stm32l4xx_hal_gpio.o
    0x08000d82   0x08000d82   0x00000002   PAD
    0x08000d84   0x08000d84   0x0000000c   Code   RO         1001    i.HAL_GetTick       stm32l4xx_hal.o
    0x08000d90   0x08000d90   0x0000005a   Code   RO         1696    i.HAL_I2CEx_ConfigAnalogFilter  stm32l4xx_hal_i2c_ex.o
    0x08000dea   0x08000dea   0x00000056   Code   RO         1697    i.HAL_I2CEx_ConfigDigitalFilter  stm32l4xx_hal_i2c_ex.o
    0x08000e40   0x08000e40   0x000000c0   Code   RO         1235    i.HAL_I2C_Init      stm32l4xx_hal_i2c.o
    0x08000f00   0x08000f00   0x000000e0   Code   RO          396    i.HAL_I2C_MspInit   i2c.o
    0x08000fe0   0x08000fe0   0x00000010   Code   RO         1007    i.HAL_IncTick       stm32l4xx_hal.o
    0x08000ff0   0x08000ff0   0x0000001e   Code   RO         1008    i.HAL_Init          stm32l4xx_hal.o
    0x0800100e   0x0800100e   0x00000002   PAD
    0x08001010   0x08001010   0x00000074   Code   RO          717    i.HAL_InitTick      stm32l4xx_hal_timebase_tim.o
    0x08001084   0x08001084   0x00000034   Code   RO          693    i.HAL_MspInit       stm32l4xx_hal_msp.o
    0x080010b8   0x080010b8   0x0000001a   Code   RO         2838    i.HAL_NVIC_ClearPendingIRQ  stm32l4xx_hal_cortex.o
    0x080010d2   0x080010d2   0x0000001a   Code   RO         2840    i.HAL_NVIC_EnableIRQ  stm32l4xx_hal_cortex.o
    0x080010ec   0x080010ec   0x00000040   Code   RO         2846    i.HAL_NVIC_SetPriority  stm32l4xx_hal_cortex.o
    0x0800112c   0x0800112c   0x00000024   Code   RO         2847    i.HAL_NVIC_SetPriorityGrouping  stm32l4xx_hal_cortex.o
    0x08001150   0x08001150   0x0000006c   Code   RO         2584    i.HAL_PWREx_ControlVoltageScaling  stm32l4xx_hal_pwr_ex.o
    0x080011bc   0x080011bc   0x00000010   Code   RO         2615    i.HAL_PWREx_GetVoltageRange  stm32l4xx_hal_pwr_ex.o
    0x080011cc   0x080011cc   0x000003d2   Code   RO         1881    i.HAL_RCCEx_PeriphCLKConfig  stm32l4xx_hal_rcc_ex.o
    0x0800159e   0x0800159e   0x00000002   PAD
    0x080015a0   0x080015a0   0x00000170   Code   RO         1751    i.HAL_RCC_ClockConfig  stm32l4xx_hal_rcc.o
    0x08001710   0x08001710   0x0000003c   Code   RO         1754    i.HAL_RCC_GetClockConfig  stm32l4xx_hal_rcc.o
    0x0800174c   0x0800174c   0x0000000c   Code   RO         1755    i.HAL_RCC_GetHCLKFreq  stm32l4xx_hal_rcc.o
    0x08001758   0x08001758   0x00000024   Code   RO         1757    i.HAL_RCC_GetPCLK1Freq  stm32l4xx_hal_rcc.o
    0x0800177c   0x0800177c   0x00000024   Code   RO         1758    i.HAL_RCC_GetPCLK2Freq  stm32l4xx_hal_rcc.o
    0x080017a0   0x080017a0   0x000000a4   Code   RO         1760    i.HAL_RCC_GetSysClockFreq  stm32l4xx_hal_rcc.o
    0x08001844   0x08001844   0x000005c4   Code   RO         1763    i.HAL_RCC_OscConfig  stm32l4xx_hal_rcc.o
    0x08001e08   0x08001e08   0x000000e2   Code   RO         3083    i.HAL_SPI_Init      stm32l4xx_hal_spi.o
    0x08001eea   0x08001eea   0x00000002   PAD
    0x08001eec   0x08001eec   0x00000068   Code   RO          474    i.HAL_SPI_MspInit   spi.o
    0x08001f54   0x08001f54   0x0000018c   Code   RO         3086    i.HAL_SPI_Receive   stm32l4xx_hal_spi.o
    0x080020e0   0x080020e0   0x000001c2   Code   RO         3091    i.HAL_SPI_Transmit  stm32l4xx_hal_spi.o
    0x080022a2   0x080022a2   0x00000272   Code   RO         3092    i.HAL_SPI_TransmitReceive  stm32l4xx_hal_spi.o
    0x08002514   0x08002514   0x00000002   Code   RO         4150    i.HAL_TIMEx_Break2Callback  stm32l4xx_hal_tim_ex.o
    0x08002516   0x08002516   0x00000002   Code   RO         4151    i.HAL_TIMEx_BreakCallback  stm32l4xx_hal_tim_ex.o
    0x08002518   0x08002518   0x00000002   Code   RO         4152    i.HAL_TIMEx_CommutCallback  stm32l4xx_hal_tim_ex.o
    0x0800251a   0x0800251a   0x0000004e   Code   RO         3425    i.HAL_TIM_Base_Init  stm32l4xx_hal_tim.o
    0x08002568   0x08002568   0x0000003c   Code   RO          516    i.HAL_TIM_Base_MspInit  tim.o
    0x080025a4   0x080025a4   0x0000008c   Code   RO         3430    i.HAL_TIM_Base_Start_IT  stm32l4xx_hal_tim.o
    0x08002630   0x08002630   0x00000002   Code   RO         3459    i.HAL_TIM_IC_CaptureCallback  stm32l4xx_hal_tim.o
    0x08002632   0x08002632   0x0000018c   Code   RO         3473    i.HAL_TIM_IRQHandler  stm32l4xx_hal_tim.o
    0x080027be   0x080027be   0x00000002   Code   RO         3476    i.HAL_TIM_OC_DelayElapsedCallback  stm32l4xx_hal_tim.o
    0x080027c0   0x080027c0   0x00000002   Code   RO         3503    i.HAL_TIM_PWM_PulseFinishedCallback  stm32l4xx_hal_tim.o
    0x080027c2   0x080027c2   0x00000002   PAD
    0x080027c4   0x080027c4   0x00000014   Code   RO           14    i.HAL_TIM_PeriodElapsedCallback  main.o
    0x080027d8   0x080027d8   0x00000002   Code   RO         3516    i.HAL_TIM_TriggerCallback  stm32l4xx_hal_tim.o
    0x080027da   0x080027da   0x00000002   Code   RO         4444    i.HAL_UARTEx_RxEventCallback  stm32l4xx_hal_uart.o
    0x080027dc   0x080027dc   0x00000002   Code   RO         4842    i.HAL_UARTEx_WakeupCallback  stm32l4xx_hal_uart_ex.o
    0x080027de   0x080027de   0x00000002   PAD
    0x080027e0   0x080027e0   0x0000002c   Code   RO         7556    i.HAL_UART_ErrorCallback  portserial.o
    0x0800280c   0x0800280c   0x000002a4   Code   RO         4463    i.HAL_UART_IRQHandler  stm32l4xx_hal_uart.o
    0x08002ab0   0x08002ab0   0x0000006c   Code   RO         4464    i.HAL_UART_Init     stm32l4xx_hal_uart.o
    0x08002b1c   0x08002b1c   0x00000090   Code   RO          558    i.HAL_UART_MspInit  usart.o
    0x08002bac   0x08002bac   0x00000020   Code   RO         7558    i.HAL_UART_TxCpltCallback  portserial.o
    0x08002bcc   0x08002bcc   0x00000002   Code   RO          604    i.HardFault_Handler  stm32l4xx_it.o
    0x08002bce   0x08002bce   0x00000002   PAD
    0x08002bd0   0x08002bd0   0x0000004c   Code   RO          355    i.MX_CAN1_Init      can.o
    0x08002c1c   0x08002c1c   0x00000068   Code   RO          257    i.MX_FREERTOS_Init  freertos.o
    0x08002c84   0x08002c84   0x00000110   Code   RO          231    i.MX_GPIO_Init      gpio.o
    0x08002d94   0x08002d94   0x00000058   Code   RO          397    i.MX_I2C1_Init      i2c.o
    0x08002dec   0x08002dec   0x00000058   Code   RO          398    i.MX_I2C2_Init      i2c.o
    0x08002e44   0x08002e44   0x00000050   Code   RO          475    i.MX_SPI2_Init      spi.o
    0x08002e94   0x08002e94   0x00000038   Code   RO          517    i.MX_TIM16_Init     tim.o
    0x08002ecc   0x08002ecc   0x00000040   Code   RO          559    i.MX_USART1_UART_Init  usart.o
    0x08002f0c   0x08002f0c   0x00000002   Code   RO          605    i.MemManage_Handler  stm32l4xx_it.o
    0x08002f0e   0x08002f0e   0x00000002   PAD
    0x08002f10   0x08002f10   0x000000c0   Code   RO          258    i.ModbusComTask     freertos.o
    0x08002fd0   0x08002fd0   0x00000008   Code   RO         6813    i.Modbus_Test_Get_Holding_Regs  modbus_test.o
    0x08002fd8   0x08002fd8   0x00000010   Code   RO         6815    i.Modbus_Test_Inc_Callback_Count  modbus_test.o
    0x08002fe8   0x08002fe8   0x00000010   Code   RO         6817    i.Modbus_Test_Inc_Poll_Count  modbus_test.o
    0x08002ff8   0x08002ff8   0x00000010   Code   RO         6818    i.Modbus_Test_Inc_Rx_Count  modbus_test.o
    0x08003008   0x08003008   0x00000010   Code   RO         6819    i.Modbus_Test_Inc_Timer_Count  modbus_test.o
    0x08003018   0x08003018   0x00000010   Code   RO         6820    i.Modbus_Test_Inc_Tx_Count  modbus_test.o
    0x08003028   0x08003028   0x00000030   Code   RO         6821    i.Modbus_Test_Init  modbus_test.o
    0x08003058   0x08003058   0x00000038   Code   RO         6822    i.Modbus_Test_LED_Indicate  modbus_test.o
    0x08003090   0x08003090   0x0000003c   Code   RO         6824    i.Modbus_Test_Update_Registers  modbus_test.o
    0x080030cc   0x080030cc   0x00000002   Code   RO          606    i.NMI_Handler       stm32l4xx_it.o
    0x080030ce   0x080030ce   0x00000002   PAD
    0x080030d0   0x080030d0   0x00000140   Code   RO         1885    i.RCCEx_PLLSAI1_Config  stm32l4xx_hal_rcc_ex.o
    0x08003210   0x08003210   0x0000011c   Code   RO         1886    i.RCCEx_PLLSAI2_Config  stm32l4xx_hal_rcc_ex.o
    0x0800332c   0x0800332c   0x00000084   Code   RO         1764    i.RCC_SetFlashLatencyFromMSIRange  stm32l4xx_hal_rcc.o
    0x080033b0   0x080033b0   0x000000f0   Code   RO         6988    i.RN7326_ReadReg    rn7326.o
    0x080034a0   0x080034a0   0x00000024   Code   RO         6916    i.RS485_Enter_RX_Mode  rs485.o
    0x080034c4   0x080034c4   0x00000022   Code   RO         6917    i.RS485_Enter_TX_Mode  rs485.o
    0x080034e6   0x080034e6   0x00000038   Code   RO         6919    i.RS485_Init        rs485.o
    0x0800351e   0x0800351e   0x0000007e   Code   RO         3120    i.SPI_EndRxTransaction  stm32l4xx_hal_spi.o
    0x0800359c   0x0800359c   0x00000064   Code   RO         3121    i.SPI_EndRxTxTransaction  stm32l4xx_hal_spi.o
    0x08003600   0x08003600   0x000000e6   Code   RO         3126    i.SPI_WaitFifoStateUntilTimeout  stm32l4xx_hal_spi.o
    0x080036e6   0x080036e6   0x00000002   PAD
    0x080036e8   0x080036e8   0x000000bc   Code   RO         3127    i.SPI_WaitFlagStateUntilTimeout  stm32l4xx_hal_spi.o
    0x080037a4   0x080037a4   0x00000008   Code   RO          259    i.SensorsTask       freertos.o
    0x080037ac   0x080037ac   0x00000008   Code   RO          260    i.SysMonitorTask    freertos.o
    0x080037b4   0x080037b4   0x0000001a   Code   RO         6103    i.SysTick_Handler   cmsis_os2.o
    0x080037ce   0x080037ce   0x00000072   Code   RO           15    i.SystemClock_Config  main.o
    0x08003840   0x08003840   0x00000010   Code   RO         4922    i.SystemInit        system_stm32l4xx.o
    0x08003850   0x08003850   0x0000002c   Code   RO          607    i.TIM1_UP_TIM16_IRQHandler  stm32l4xx_it.o
    0x0800387c   0x0800387c   0x0000000c   Code   RO          608    i.TIM6_DAC_IRQHandler  stm32l4xx_it.o
    0x08003888   0x08003888   0x000000c6   Code   RO         3518    i.TIM_Base_SetConfig  stm32l4xx_hal_tim.o
    0x0800394e   0x0800394e   0x000000c8   Code   RO         4478    i.UART_AdvFeatureConfig  stm32l4xx_hal_uart.o
    0x08003a16   0x08003a16   0x00000060   Code   RO         4479    i.UART_CheckIdleState  stm32l4xx_hal_uart.o
    0x08003a76   0x08003a76   0x00000014   Code   RO         4480    i.UART_DMAAbortOnError  stm32l4xx_hal_uart.o
    0x08003a8a   0x08003a8a   0x0000004a   Code   RO         4490    i.UART_EndRxTransfer  stm32l4xx_hal_uart.o
    0x08003ad4   0x08003ad4   0x00000022   Code   RO         4491    i.UART_EndTransmit_IT  stm32l4xx_hal_uart.o
    0x08003af6   0x08003af6   0x00000002   PAD
    0x08003af8   0x08003af8   0x000002c0   Code   RO         4495    i.UART_SetConfig    stm32l4xx_hal_uart.o
    0x08003db8   0x08003db8   0x000000be   Code   RO         4500    i.UART_WaitOnFlagUntilTimeout  stm32l4xx_hal_uart.o
    0x08003e76   0x08003e76   0x00000002   PAD
    0x08003e78   0x08003e78   0x00000058   Code   RO          609    i.USART1_IRQHandler  stm32l4xx_it.o
    0x08003ed0   0x08003ed0   0x00000002   Code   RO          610    i.UsageFault_Handler  stm32l4xx_it.o
    0x08003ed2   0x08003ed2   0x00000020   Code   RO         2853    i.__NVIC_SetPriority  stm32l4xx_hal_cortex.o
    0x08003ef2   0x08003ef2   0x00000002   PAD
    0x08003ef4   0x08003ef4   0x00000020   Code   RO         7410    i.eMBEnable         mb.o
    0x08003f14   0x08003f14   0x0000007c   Code   RO         7072    i.eMBFuncReadCoils  mbfunccoils.o
    0x08003f90   0x08003f90   0x00000078   Code   RO         7111    i.eMBFuncReadDiscreteInputs  mbfuncdisc.o
    0x08004008   0x08004008   0x00000066   Code   RO         7153    i.eMBFuncReadHoldingRegister  mbfuncholding.o
    0x0800406e   0x0800406e   0x00000064   Code   RO         7195    i.eMBFuncReadInputRegister  mbfuncinput.o
    0x080040d2   0x080040d2   0x0000009c   Code   RO         7154    i.eMBFuncReadWriteMultipleHoldingRegister  mbfuncholding.o
    0x0800416e   0x0800416e   0x00000002   PAD
    0x08004170   0x08004170   0x00000024   Code   RO         7219    i.eMBFuncReportSlaveID  mbfuncother.o
    0x08004194   0x08004194   0x0000005a   Code   RO         7073    i.eMBFuncWriteCoil  mbfunccoils.o
    0x080041ee   0x080041ee   0x00000032   Code   RO         7155    i.eMBFuncWriteHoldingRegister  mbfuncholding.o
    0x08004220   0x08004220   0x00000066   Code   RO         7074    i.eMBFuncWriteMultipleCoils  mbfunccoils.o
    0x08004286   0x08004286   0x00000052   Code   RO         7156    i.eMBFuncWriteMultipleHoldingRegister  mbfuncholding.o
    0x080042d8   0x080042d8   0x00000088   Code   RO         7411    i.eMBInit           mb.o
    0x08004360   0x08004360   0x000000b4   Code   RO         7412    i.eMBPoll           mb.o
    0x08004414   0x08004414   0x00000048   Code   RO         7314    i.eMBRTUInit        mbrtu.o
    0x0800445c   0x0800445c   0x00000048   Code   RO         7315    i.eMBRTUReceive     mbrtu.o
    0x080044a4   0x080044a4   0x000000bc   Code   RO         7316    i.eMBRTUSend        mbrtu.o
    0x08004560   0x08004560   0x00000020   Code   RO         7317    i.eMBRTUStart       mbrtu.o
    0x08004580   0x08004580   0x00000014   Code   RO         7318    i.eMBRTUStop        mbrtu.o
    0x08004594   0x08004594   0x00000074   Code   RO         7473    i.eMBRegCoilsCB     mdcb.o
    0x08004608   0x08004608   0x0000004c   Code   RO         7474    i.eMBRegDiscreteCB  mdcb.o
    0x08004654   0x08004654   0x00000088   Code   RO         7475    i.eMBRegHoldingCB   mdcb.o
    0x080046dc   0x080046dc   0x0000003c   Code   RO         7476    i.eMBRegInputCB     mdcb.o
    0x08004718   0x08004718   0x00000032   Code   RO           16    i.main              main.o
    0x0800474a   0x0800474a   0x0000001c   Code   RO         6105    i.osDelay           cmsis_os2.o
    0x08004766   0x08004766   0x00000002   PAD
    0x08004768   0x08004768   0x00000028   Code   RO         6119    i.osKernelInitialize  cmsis_os2.o
    0x08004790   0x08004790   0x00000038   Code   RO         6122    i.osKernelStart     cmsis_os2.o
    0x080047c8   0x080047c8   0x000000ba   Code   RO         6164    i.osThreadNew       cmsis_os2.o
    0x08004882   0x08004882   0x00000002   PAD
    0x08004884   0x08004884   0x00000060   Code   RO         5540    i.prvAddCurrentTaskToDelayedList  tasks.o
    0x080048e4   0x080048e4   0x00000088   Code   RO         5541    i.prvAddNewTaskToReadyList  tasks.o
    0x0800496c   0x0800496c   0x00000058   Code   RO         5916    i.prvCheckForValidListAndQueue  timers.o
    0x080049c4   0x080049c4   0x0000003c   Code   RO         5542    i.prvCheckTasksWaitingTermination  tasks.o
    0x08004a00   0x08004a00   0x00000028   Code   RO         5127    i.prvCopyDataFromQueue  queue.o
    0x08004a28   0x08004a28   0x0000006e   Code   RO         5128    i.prvCopyDataToQueue  queue.o
    0x08004a96   0x08004a96   0x00000040   Code   RO         5543    i.prvDeleteTCB      tasks.o
    0x08004ad6   0x08004ad6   0x00000002   PAD
    0x08004ad8   0x08004ad8   0x00000024   Code   RO         5917    i.prvGetNextExpireTime  timers.o
    0x08004afc   0x08004afc   0x0000004c   Code   RO         6628    i.prvHeapInit       heap_4.o
    0x08004b48   0x08004b48   0x00000028   Code   RO         5544    i.prvIdleTask       tasks.o
    0x08004b70   0x08004b70   0x00000022   Code   RO         5131    i.prvInitialiseNewQueue  queue.o
    0x08004b92   0x08004b92   0x000000ae   Code   RO         5545    i.prvInitialiseNewTask  tasks.o
    0x08004c40   0x08004c40   0x00000058   Code   RO         5546    i.prvInitialiseTaskLists  tasks.o
    0x08004c98   0x08004c98   0x00000054   Code   RO         6629    i.prvInsertBlockIntoFreeList  heap_4.o
    0x08004cec   0x08004cec   0x00000040   Code   RO         5919    i.prvInsertTimerInActiveList  timers.o
    0x08004d2c   0x08004d2c   0x0000001a   Code   RO         5132    i.prvIsQueueEmpty   queue.o
    0x08004d46   0x08004d46   0x0000001e   Code   RO         5133    i.prvIsQueueFull    queue.o
    0x08004d64   0x08004d64   0x00000068   Code   RO         5920    i.prvProcessExpiredTimer  timers.o
    0x08004dcc   0x08004dcc   0x000000fc   Code   RO         5921    i.prvProcessReceivedCommands  timers.o
    0x08004ec8   0x08004ec8   0x0000006c   Code   RO         5922    i.prvProcessTimerOrBlockTask  timers.o
    0x08004f34   0x08004f34   0x00000024   Code   RO         5548    i.prvResetNextTaskUnblockTime  tasks.o
    0x08004f58   0x08004f58   0x0000002c   Code   RO         5923    i.prvSampleTimeNow  timers.o
    0x08004f84   0x08004f84   0x00000070   Code   RO         5924    i.prvSwitchTimerLists  timers.o
    0x08004ff4   0x08004ff4   0x0000002c   Code   RO         6684    i.prvTaskExitError  port.o
    0x08005020   0x08005020   0x00000014   Code   RO         5925    i.prvTimerTask      timers.o
    0x08005034   0x08005034   0x0000006a   Code   RO         5134    i.prvUnlockQueue    queue.o
    0x0800509e   0x0800509e   0x0000001c   Code   RO         7252    i.prveMBError2Exception  mbutils.o
    0x080050ba   0x080050ba   0x00000002   PAD
    0x080050bc   0x080050bc   0x0000003c   Code   RO         7631    i.prvvTIMERExpiredISR  porttimer.o
    0x080050f8   0x080050f8   0x00000014   Code   RO         7559    i.prvvUARTRxISR     portserial.o
    0x0800510c   0x0800510c   0x00000014   Code   RO         7560    i.prvvUARTTxReadyISR  portserial.o
    0x08005120   0x08005120   0x000000dc   Code   RO         6630    i.pvPortMalloc      heap_4.o
    0x080051fc   0x080051fc   0x00000024   Code   RO         6685    i.pxPortInitialiseStack  port.o
    0x08005220   0x08005220   0x00000020   Code   RO         6990    i.test_read_LostVoltage  rn7326.o
    0x08005240   0x08005240   0x00000024   Code   RO         6991    i.test_read_voltage  rn7326.o
    0x08005264   0x08005264   0x0000002c   Code   RO         7288    i.usMBCRC16         mbcrc.o
    0x08005290   0x08005290   0x00000026   Code   RO         5087    i.uxListRemove      list.o
    0x080052b6   0x080052b6   0x00000002   PAD
    0x080052b8   0x080052b8   0x00000014   Code   RO         6176    i.vApplicationGetIdleTaskMemory  cmsis_os2.o
    0x080052cc   0x080052cc   0x00000018   Code   RO         6177    i.vApplicationGetTimerTaskMemory  cmsis_os2.o
    0x080052e4   0x080052e4   0x00000016   Code   RO         5088    i.vListInitialise   list.o
    0x080052fa   0x080052fa   0x00000006   Code   RO         5089    i.vListInitialiseItem  list.o
    0x08005300   0x08005300   0x00000030   Code   RO         5090    i.vListInsert       list.o
    0x08005330   0x08005330   0x00000018   Code   RO         5091    i.vListInsertEnd    list.o
    0x08005348   0x08005348   0x00000054   Code   RO         7561    i.vMBPortSerialEnable  portserial.o
    0x0800539c   0x0800539c   0x0000003c   Code   RO         7662    i.vMBPortTimersDisable  porttimer.o
    0x080053d8   0x080053d8   0x0000002c   Code   RO         7656    i.vMBPortTimersEnable  porttimer.o
    0x08005404   0x08005404   0x00000040   Code   RO         6687    i.vPortEnterCritical  port.o
    0x08005444   0x08005444   0x0000002c   Code   RO         6688    i.vPortExitCritical  port.o
    0x08005470   0x08005470   0x00000064   Code   RO         6631    i.vPortFree         heap_4.o
    0x080054d4   0x080054d4   0x00000024   Code   RO         6689    i.vPortSetupTimerInterrupt  port.o
    0x080054f8   0x080054f8   0x00000054   Code   RO         6690    i.vPortValidateInterruptPriority  port.o
    0x0800554c   0x0800554c   0x00000024   Code   RO         5140    i.vQueueAddToRegistry  queue.o
    0x08005570   0x08005570   0x00000046   Code   RO         5144    i.vQueueWaitForMessageRestricted  queue.o
    0x080055b6   0x080055b6   0x00000002   PAD
    0x080055b8   0x080055b8   0x00000050   Code   RO         5561    i.vTaskDelay        tasks.o
    0x08005608   0x08005608   0x00000010   Code   RO         5566    i.vTaskInternalSetTimeOutState  tasks.o
    0x08005618   0x08005618   0x0000000c   Code   RO         5567    i.vTaskMissedYield  tasks.o
    0x08005624   0x08005624   0x00000034   Code   RO         5569    i.vTaskPlaceOnEventList  tasks.o
    0x08005658   0x08005658   0x0000003c   Code   RO         5570    i.vTaskPlaceOnEventListRestricted  tasks.o
    0x08005694   0x08005694   0x00000090   Code   RO         5578    i.vTaskStartScheduler  tasks.o
    0x08005724   0x08005724   0x00000010   Code   RO         5580    i.vTaskSuspendAll   tasks.o
    0x08005734   0x08005734   0x00000068   Code   RO         5581    i.vTaskSwitchContext  tasks.o
    0x0800579c   0x0800579c   0x0000001c   Code   RO         7518    i.xMBPortEventGet   portevent.o
    0x080057b8   0x080057b8   0x00000010   Code   RO         7519    i.xMBPortEventInit  portevent.o
    0x080057c8   0x080057c8   0x00000010   Code   RO         7520    i.xMBPortEventPost  portevent.o
    0x080057d8   0x080057d8   0x00000010   Code   RO         7562    i.xMBPortSerialGetByte  portserial.o
    0x080057e8   0x080057e8   0x00000004   Code   RO         7563    i.xMBPortSerialInit  portserial.o
    0x080057ec   0x080057ec   0x00000010   Code   RO         7564    i.xMBPortSerialPutByte  portserial.o
    0x080057fc   0x080057fc   0x00000058   Code   RO         7632    i.xMBPortTimersInit  porttimer.o
    0x08005854   0x08005854   0x00000070   Code   RO         7319    i.xMBRTUReceiveFSM  mbrtu.o
    0x080058c4   0x080058c4   0x00000034   Code   RO         7320    i.xMBRTUTimerT35Expired  mbrtu.o
    0x080058f8   0x080058f8   0x00000064   Code   RO         7321    i.xMBRTUTransmitFSM  mbrtu.o
    0x0800595c   0x0800595c   0x00000022   Code   RO         7253    i.xMBUtilGetBits    mbutils.o
    0x0800597e   0x0800597e   0x0000002a   Code   RO         7254    i.xMBUtilSetBits    mbutils.o
    0x080059a8   0x080059a8   0x000000f4   Code   RO         6691    i.xPortStartScheduler  port.o
    0x08005a9c   0x08005a9c   0x0000002c   Code   RO         6692    i.xPortSysTickHandler  port.o
    0x08005ac8   0x08005ac8   0x0000006c   Code   RO         5150    i.xQueueGenericCreateStatic  queue.o
    0x08005b34   0x08005b34   0x0000008c   Code   RO         5151    i.xQueueGenericReset  queue.o
    0x08005bc0   0x08005bc0   0x00000164   Code   RO         5152    i.xQueueGenericSend  queue.o
    0x08005d24   0x08005d24   0x000000c0   Code   RO         5153    i.xQueueGenericSendFromISR  queue.o
    0x08005de4   0x08005de4   0x0000013c   Code   RO         5162    i.xQueueReceive     queue.o
    0x08005f20   0x08005f20   0x0000007c   Code   RO         5583    i.xTaskCheckForTimeOut  tasks.o
    0x08005f9c   0x08005f9c   0x0000006a   Code   RO         5584    i.xTaskCreate       tasks.o
    0x08006006   0x08006006   0x0000005c   Code   RO         5585    i.xTaskCreateStatic  tasks.o
    0x08006062   0x08006062   0x00000002   PAD
    0x08006064   0x08006064   0x0000001c   Code   RO         5589    i.xTaskGetSchedulerState  tasks.o
    0x08006080   0x08006080   0x0000000c   Code   RO         5590    i.xTaskGetTickCount  tasks.o
    0x0800608c   0x0800608c   0x000000cc   Code   RO         5592    i.xTaskIncrementTick  tasks.o
    0x08006158   0x08006158   0x0000007c   Code   RO         5595    i.xTaskPriorityDisinherit  tasks.o
    0x080061d4   0x080061d4   0x00000078   Code   RO         5597    i.xTaskRemoveFromEventList  tasks.o
    0x0800624c   0x0800624c   0x000000c8   Code   RO         5598    i.xTaskResumeAll    tasks.o
    0x08006314   0x08006314   0x00000068   Code   RO         5934    i.xTimerCreateTimerTask  timers.o
    0x0800637c   0x0800637c   0x0000006c   Code   RO         5935    i.xTimerGenericCommand  timers.o
    0x080063e8   0x080063e8   0x0000000a   Code   RO         7798    x$fpl$fpinit        fz_wm.l(fpinit.o)
    0x080063f2   0x080063f2   0x00000002   PAD
    0x080063f4   0x080063f4   0x000000b4   Data   RO          261    .constdata          freertos.o
    0x080064a8   0x080064a8   0x00000040   Data   RO         4923    .constdata          system_stm32l4xx.o
    0x080064e8   0x080064e8   0x00000008   Data   RO         4924    .constdata          system_stm32l4xx.o
    0x080064f0   0x080064f0   0x00000200   Data   RO         7289    .constdata          mbcrc.o
    0x080066f0   0x080066f0   0x00000048   Data   RO          262    .conststring        freertos.o
    0x08006738   0x08006738   0x00000020   Data   RO         7854    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM2 (Exec base: 0x10000000, Load base: 0x080067dc, Size: 0x00000000, Max: 0x00010000, ABSOLUTE)

    **** No section assigned to this execution region ****


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x08006758, Size: 0x000038e8, Max: 0x00040000, ABSOLUTE, COMPRESSED[0x00000084])

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   COMPRESSED   0x0000001c   Data   RW          263    .data               freertos.o
    0x2000001c   COMPRESSED   0x0000000c   Data   RW         1025    .data               stm32l4xx_hal.o
    0x20000028   COMPRESSED   0x00000004   Data   RW         4925    .data               system_stm32l4xx.o
    0x2000002c   COMPRESSED   0x0000003c   Data   RW         5601    .data               tasks.o
    0x20000068   COMPRESSED   0x00000014   Data   RW         5943    .data               timers.o
    0x2000007c   COMPRESSED   0x00000004   Data   RW         6179    .data               cmsis_os2.o
    0x20000080   COMPRESSED   0x00000020   Data   RW         6637    .data               heap_4.o
    0x200000a0   COMPRESSED   0x0000000c   Data   RW         6693    .data               port.o
    0x200000ac   COMPRESSED   0x00000034   Data   RW         6825    .data               modbus_test.o
    0x200000e0   COMPRESSED   0x00000002   Data   RW         6992    .data               rn7326.o
    0x200000e2   COMPRESSED   0x00000002   Data   RW         7222    .data               mbfuncother.o
    0x200000e4   COMPRESSED   0x0000000c   Data   RW         7323    .data               mbrtu.o
    0x200000f0   COMPRESSED   0x000000ac   Data   RW         7414    .data               mb.o
    0x2000019c   COMPRESSED   0x00000098   Data   RW         7477    .data               mdcb.o
    0x20000234   COMPRESSED   0x00000002   Data   RW         7521    .data               portevent.o
    0x20000236   COMPRESSED   0x00000002   Data   RW         7565    .data               portserial.o
    0x20000238   COMPRESSED   0x00000008   Data   RW         7633    .data               porttimer.o
    0x20000240        -       0x00000028   Zero   RW          356    .bss                can.o
    0x20000268        -       0x000000a8   Zero   RW          399    .bss                i2c.o
    0x20000310        -       0x00000064   Zero   RW          476    .bss                spi.o
    0x20000374        -       0x0000004c   Zero   RW          518    .bss                tim.o
    0x200003c0        -       0x00000084   Zero   RW          560    .bss                usart.o
    0x20000444        -       0x0000004c   Zero   RW          720    .bss                stm32l4xx_hal_timebase_tim.o
    0x20000490        -       0x00000040   Zero   RW         5166    .bss                queue.o
    0x200004d0        -       0x000004c4   Zero   RW         5600    .bss                tasks.o
    0x20000994        -       0x00000118   Zero   RW         5942    .bss                timers.o
    0x20000aac        -       0x000006b8   Zero   RW         6178    .bss                cmsis_os2.o
    0x20001164        -       0x00002000   Zero   RW         6636    .bss                heap_4.o
    0x20003164        -       0x00000020   Zero   RW         7221    .bss                mbfuncother.o
    0x20003184        -       0x00000100   Zero   RW         7322    .bss                mbrtu.o
    0x20003284        -       0x00000060   Zero   RW         7728    .bss                c_w.l(libspace.o)
    0x200032e4   COMPRESSED   0x00000004   PAD
    0x200032e8        -       0x00000200   Zero   RW            2    HEAP                startup_stm32l496xx.o
    0x200034e8        -       0x00000400   Zero   RW            1    STACK               startup_stm32l496xx.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       192         20          0          0         40       1761   can.o
       380         24          0          4       1720      65227   cmsis_os2.o
         0          0          0          0          0      19524   event_groups.o
       336         38        252         28          0       8861   freertos.o
       272         14          0          0          0       1059   gpio.o
       480         26          0         32       8192       5087   heap_4.o
       400         40          0          0        168       2574   i2c.o
       138          0          0          0          0       3654   list.o
       188          6          0          0          0     935740   main.o
       348         44          0        172          0       4576   mb.o
         0          0          0          0          0        608   mbascii.o
        44          6        512          0          0       1340   mbcrc.o
       316          0          0          0          0       4318   mbfunccoils.o
       120          0          0          0          0       1832   mbfuncdisc.o
       390          0          0          0          0       5919   mbfuncholding.o
       100          0          0          0          0       1807   mbfuncinput.o
        36         10          0          2         32       1503   mbfuncother.o
       648         46          0         12        256       7187   mbrtu.o
       104          0          0          0          0       2906   mbutils.o
       388         18          0        152          0       4790   mdcb.o
       252         50          0         52          0       5850   modbus_test.o
       818         84          0         12          0      11769   port.o
        60         14          0          2          0       2368   portevent.o
       236         46          0          2          0       5703   portserial.o
       252         34          0          8          0       2419   porttimer.o
      1564         20          0          0         64      17964   queue.o
       308         20          0          2          0       2975   rn7326.o
       126          0          0          0          0       1765   rs485.o
       184         22          0          0        100       1765   spi.o
        64         26        428          0       1536        848   startup_stm32l496xx.o
        94         14          0         12          0      15806   stm32l4xx_hal.o
       878          0          0          0          0      10747   stm32l4xx_hal_can.o
       184         14          0          0          0      33913   stm32l4xx_hal_cortex.o
       152          0          0          0          0       1787   stm32l4xx_hal_dma.o
       520         46          0          0          0       4205   stm32l4xx_hal_gpio.o
       192          6          0          0          0       2811   stm32l4xx_hal_i2c.o
       176          0          0          0          0       2035   stm32l4xx_hal_i2c_ex.o
        52          4          0          0          0        886   stm32l4xx_hal_msp.o
       124         18          0          0          0       1390   stm32l4xx_hal_pwr_ex.o
      2284        110          0          0          0       9193   stm32l4xx_hal_rcc.o
      1582         40          0          0          0       4344   stm32l4xx_hal_rcc_ex.o
      2342         10          0          0          0       9810   stm32l4xx_hal_spi.o
       820         62          0          0          0       6207   stm32l4xx_hal_tim.o
         6          0          0          0          0       2248   stm32l4xx_hal_tim_ex.o
       116         18          0          0         76       1499   stm32l4xx_hal_timebase_tim.o
      2104         72          0          0          0      10200   stm32l4xx_hal_uart.o
         2          0          0          0          0       1000   stm32l4xx_hal_uart_ex.o
       180         26          0          0          0       6014   stm32l4xx_it.o
         0          0          0          0          0        392   stream_buffer.o
        16          4         72          4          0       1229   system_stm32l4xx.o
      2188        186          0         60       1220      28482   tasks.o
       116         18          0          0         76       1664   tim.o
      1040         88          0         20        280      33415   timers.o
       208         18          0          0        132       1838   usart.o

    ----------------------------------------------------------------------
     24166       <USER>       <GROUP>        576      13892    1324814   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        46          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

        90          0          0          0          0          0   __dczerorl2.o
         8          0          0          0          0         68   __main.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        52          8          0          0          0          0   __scatter.o
        28          0          0          0          0          0   __scatter_zi.o
        16          0          0          0          0         68   aeabi_memset.o
        18          0          0          0          0         80   exit.o
         6          0          0          0          0        152   heapauxi.o
         0          0          0          0          0          0   indicate_semi.o
         2          0          0          0          0          0   libinit.o
         6          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
       238          0          0          0          0        100   lludivv7m.o
        68          0          0          0          0         68   rt_memclr.o
        78          0          0          0          0         80   rt_memclr_w.o
       138          0          0          0          0         68   rt_memcpy_v6.o
       100          0          0          0          0         80   rt_memcpy_w.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
        12          4          0          0          0         68   sys_exit.o
        74          0          0          0          0         80   sys_stackheap_outer.o
         2          0          0          0          0         68   use_no_semi.o
        10          0          0          0          0        116   fpinit.o

    ----------------------------------------------------------------------
       994         <USER>          <GROUP>          0        100       1164   Library Totals
         6          0          0          0          4          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

       978         16          0          0         96       1048   c_w.l
        10          0          0          0          0        116   fz_wm.l

    ----------------------------------------------------------------------
       994         <USER>          <GROUP>          0        100       1164   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     25160       1378       1296        576      13992    1301410   Grand Totals
     25160       1378       1296        132      13992    1301410   ELF Image Totals (compressed)
     25160       1378       1296        132          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                26456 (  25.84kB)
    Total RW  Size (RW Data + ZI Data)             14568 (  14.23kB)
    Total ROM Size (Code + RO Data + RW Data)      26588 (  25.96kB)

==============================================================================

