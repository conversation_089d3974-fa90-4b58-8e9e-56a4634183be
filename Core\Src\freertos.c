/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * File Name          : freertos.c
  * Description        : Code for freertos applications
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2025 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */

/* Includes ------------------------------------------------------------------*/
#include "FreeRTOS.h"
#include "task.h"
#include "main.h"
#include "cmsis_os.h"

/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */
#include "iwdg.h"
#include "usart.h"
#include "rs485.h"
#include "modbus_rtu_slave.h"
#include "mb.h"
#include "rn7326.h"
#include "modbus_test.h"
#include "stm32l4xx_hal.h"
/* USER CODE END Includes */

/* Private typedef -----------------------------------------------------------*/
/* USER CODE BEGIN PTD */

/* USER CODE END PTD */

/* Private define ------------------------------------------------------------*/
/* USER CODE BEGIN PD */

/* USER CODE END PD */

/* Private macro -------------------------------------------------------------*/
/* USER CODE BEGIN PM */

/* USER CODE END PM */

/* Private variables ---------------------------------------------------------*/
/* USER CODE BEGIN Variables */

/* USER CODE END Variables */
/* Definitions for energymeterTask */
osThreadId_t energymeterTaskHandle;
const osThreadAttr_t energymeterTask_attributes = {
  .name = "energymeterTask",
  .stack_size = 512 * 4,
  .priority = (osPriority_t) osPriorityRealtime,
};
/* Definitions for modbuscomTask */
osThreadId_t modbuscomTaskHandle;
const osThreadAttr_t modbuscomTask_attributes = {
  .name = "modbuscomTask",
  .stack_size = 512 * 4,
  .priority = (osPriority_t) osPriorityHigh,
};
/* Definitions for cancomTask */
osThreadId_t cancomTaskHandle;
const osThreadAttr_t cancomTask_attributes = {
  .name = "cancomTask",
  .stack_size = 256 * 4,
  .priority = (osPriority_t) osPriorityNormal,
};
/* Definitions for sensorsTask */
osThreadId_t sensorsTaskHandle;
const osThreadAttr_t sensorsTask_attributes = {
  .name = "sensorsTask",
  .stack_size = 256 * 4,
  .priority = (osPriority_t) osPriorityLow,
};
/* Definitions for sysmonitorTask */
osThreadId_t sysmonitorTaskHandle;
const osThreadAttr_t sysmonitorTask_attributes = {
  .name = "sysmonitorTask",
  .stack_size = 256 * 4,
  .priority = (osPriority_t) osPriorityLow,
};

/* Private function prototypes -----------------------------------------------*/
/* USER CODE BEGIN FunctionPrototypes */
// 测试函数声明
extern void Debug_Disable_Modbus_Response(void);
extern void Debug_Enable_Modbus_Response(void);
/* USER CODE END FunctionPrototypes */

void EnergyMeterTask(void *argument);
void ModbusComTask(void *argument);
void CANComTask(void *argument);
void SensorsTask(void *argument);
void SysMonitorTask(void *argument);

void MX_FREERTOS_Init(void); /* (MISRA C 2004 rule 8.1) */

/**
  * @brief  FreeRTOS initialization
  * @param  None
  * @retval None
  */
void MX_FREERTOS_Init(void) {
  /* USER CODE BEGIN Init */

  /* USER CODE END Init */

  /* USER CODE BEGIN RTOS_MUTEX */
  /* add mutexes, ... */
  /* USER CODE END RTOS_MUTEX */

  /* USER CODE BEGIN RTOS_SEMAPHORES */
  /* add semaphores, ... */
  /* USER CODE END RTOS_SEMAPHORES */

  /* USER CODE BEGIN RTOS_TIMERS */
  /* start timers, add new ones, ... */
  /* USER CODE END RTOS_TIMERS */

  /* USER CODE BEGIN RTOS_QUEUES */
  /* add queues, ... */
  /* USER CODE END RTOS_QUEUES */

  /* Create the thread(s) */
  /* creation of energymeterTask */
  energymeterTaskHandle = osThreadNew(EnergyMeterTask, NULL, &energymeterTask_attributes);

  /* creation of modbuscomTask */
  modbuscomTaskHandle = osThreadNew(ModbusComTask, NULL, &modbuscomTask_attributes);

  /* creation of cancomTask */
  cancomTaskHandle = osThreadNew(CANComTask, NULL, &cancomTask_attributes);

  /* creation of sensorsTask */
  sensorsTaskHandle = osThreadNew(SensorsTask, NULL, &sensorsTask_attributes);

  /* creation of sysmonitorTask */
  sysmonitorTaskHandle = osThreadNew(SysMonitorTask, NULL, &sysmonitorTask_attributes);

  /* USER CODE BEGIN RTOS_THREADS */
  /* add threads, ... */
  /* USER CODE END RTOS_THREADS */

  /* USER CODE BEGIN RTOS_EVENTS */
  /* add events, ... */
  /* USER CODE END RTOS_EVENTS */

}

/* USER CODE BEGIN Header_EnergyMeterTask */
/**
  * @brief  Function implementing the energymeterTask thread.
  * @param  argument: Not used
  * @retval None
  */
/* USER CODE END Header_EnergyMeterTask */
void EnergyMeterTask(void *argument)
{
  /* USER CODE BEGIN EnergyMeterTask */
  /* Infinite loop */
  for(;;)
  {
	  test_read_LostVoltage();
	  test_read_voltage();
      osDelay(100);
  }
  /* USER CODE END EnergyMeterTask */
}

/* USER CODE BEGIN Header_ModbusComTask */
/**
* @brief Function implementing the modbuscomTask thread.
* @param argument: Not used
* @retval None
*/
/* USER CODE END Header_ModbusComTask */
void ModbusComTask(void *argument)
{
  /* USER CODE BEGIN ModbusComTask */
    eMBErrorCode eStatus;

    // 添加调试LED或UART输出来指示初始化状态
    HAL_GPIO_WritePin(LED_GPIO_Port, LED_Pin, GPIO_PIN_SET); // 指示任务开始

    // 等待系统稳定
    osDelay(100);

    // 初始化测试功能
    Modbus_Test_Init();

    // 初始化RS485
    RS485_Init();
    RS485_Enter_RX_Mode();

    // 修正参数类型 - 从站地址0x01，串口1，波特率9600，无校验，1停止位
    // 注意：确保从站地址为1，不是0x81
    eStatus = eMBInit(MB_RTU, 1, 1, 9600, MB_PAR_NONE, 1);

    if (eStatus != MB_ENOERR)
    {
        // 初始化失败，闪烁LED指示错误
        while(1)
        {
            HAL_GPIO_TogglePin(LED_GPIO_Port, LED_Pin);
            osDelay(500);
        }
    }

    // 使能Modbus协议栈
    eStatus = eMBEnable();

    if (eStatus != MB_ENOERR)
    {
        // 使能失败，快速闪烁LED指示错误
        while(1)
        {
            HAL_GPIO_TogglePin(LED_GPIO_Port, LED_Pin);
            osDelay(200);
        }
    }

    // 初始化成功，LED常亮一段时间后熄灭
    HAL_GPIO_WritePin(LED_GPIO_Port, LED_Pin, GPIO_PIN_RESET);
    osDelay(1000);
    HAL_GPIO_WritePin(LED_GPIO_Port, LED_Pin, GPIO_PIN_SET);
    /* Infinite loop */
    for(;;)
    {		
        // 轮询Modbus协议栈
        eStatus = eMBPoll();
        Modbus_Test_Inc_Poll_Count();

        // 更新测试寄存器数据
        static uint32_t update_counter = 0;
        if (++update_counter >= 100) {  // 每100ms更新一次
            update_counter = 0;
            Modbus_Test_Update_Registers();
        }



        // 可选：添加活动指示
        static uint32_t counter = 0;
        if (++counter >= 1000)  // 每秒闪一次表示正在运行
        {
            counter = 0;
            Modbus_Test_LED_Indicate(MODBUS_LED_RUNNING);
        }

        // 测试控制：10秒后禁用Modbus响应，20秒后重新启用
        static uint32_t test_counter = 0;
        test_counter++;
        if (test_counter == 10000) {  // 10秒后
            Debug_Disable_Modbus_Response();
        } else if (test_counter == 20000) {  // 20秒后
            Debug_Enable_Modbus_Response();
            test_counter = 0;  // 重置计数器
        }
        
        osDelay(1);
    }
  /* USER CODE END ModbusComTask */
}

/* USER CODE BEGIN Header_CANComTask */
/**
* @brief Function implementing the cancomTask thread.
* @param argument: Not used
* @retval None
*/
/* USER CODE END Header_CANComTask */
void CANComTask(void *argument)
{
  /* USER CODE BEGIN CANComTask */
  /* Infinite loop */
  for(;;)
  {
    osDelay(1);
  }
  /* USER CODE END CANComTask */
}

/* USER CODE BEGIN Header_SensorsTask */
/**
* @brief Function implementing the sensorsTask thread.
* @param argument: Not used
* @retval None
*/
/* USER CODE END Header_SensorsTask */
void SensorsTask(void *argument)
{
  /* USER CODE BEGIN SensorsTask */
  /* Infinite loop */
  for(;;)
  {
    osDelay(1);
  }
  /* USER CODE END SensorsTask */
}

/* USER CODE BEGIN Header_SysMonitorTask */
/**
* @brief Function implementing the sysmonitorTask thread.
* @param argument: Not used
* @retval None
*/
/* USER CODE END Header_SysMonitorTask */
void SysMonitorTask(void *argument)
{
  /* USER CODE BEGIN SysMonitorTask */
  /* Infinite loop */
  for(;;)
  {
//	HAL_IWDG_Refresh(&hiwdg);  
//    osDelay(200);
	  HAL_Delay(200);
//	  HAL_GPIO_TogglePin(GPIOC, GPIO_PIN_13);	  
  }
  /* USER CODE END SysMonitorTask */
}

/* Private application code --------------------------------------------------*/
/* USER CODE BEGIN Application */

/* USER CODE END Application */

