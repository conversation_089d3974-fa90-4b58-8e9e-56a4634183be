// modbus_test.h - FreeModbus测试和调试功能头文件
#ifndef __MODBUS_TEST_H__
#define __MODBUS_TEST_H__

#include <stdint.h>

// LED状态枚举
typedef enum {
    MODBUS_LED_INIT = 0,    // 初始化
    MODBUS_LED_RUNNING,     // 正常运行
    MODBUS_LED_ERROR,       // 错误状态
    MODBUS_LED_COMM         // 通信状态
} ModbusTestLEDState;

// 调试统计结构体
typedef struct {
    uint32_t rx_count;      // 接收字节计数
    uint32_t tx_count;      // 发送字节计数
    uint32_t timer_count;   // 定时器中断计数
    uint32_t error_count;   // 错误计数
} ModbusTestStats;

// 函数声明
void Modbus_Test_Init(void);
void Modbus_Test_Update_Registers(void);
uint16_t* Modbus_Test_Get_Holding_Regs(void);

void Modbus_Test_Inc_Rx_Count(void);
void Modbus_Test_Inc_Tx_Count(void);
void Modbus_Test_Inc_Timer_Count(void);
void Modbus_Test_Inc_Error_Count(void);

void Modbus_Test_Get_Stats(ModbusTestStats* stats);
void Modbus_Test_Print_Stats(void);
void Modbus_Test_LED_Indicate(ModbusTestLEDState state);

int Modbus_Test_Communication(void);

#endif // __MODBUS_TEST_H__
