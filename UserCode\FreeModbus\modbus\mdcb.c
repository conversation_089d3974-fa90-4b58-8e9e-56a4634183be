#include "mb.h"

// ??????????????
#define REG_INPUT_START 0x0000
// ????????????
#define REG_INPUT_NREGS 8
// ??????????????
#define REG_HOLDING_START 0x0000
// ????????????
#define REG_HOLDING_NREGS 64

// ?????????
#define REG_COILS_START 0x0000
// ???????
#define REG_COILS_SIZE 16

// ??????????????
#define REG_DISCRETE_START 0x0000
// ????????????
#define REG_DISCRETE_SIZE 16

/* Private variables ---------------------------------------------------------*/
// ????????????
uint16_t usRegInputBuf[REG_INPUT_NREGS] = {0x1234, 0x5678, 0x9abc, 0xdef0, 0x147b, 0x3f8e, 0x147b, 0x3f8e};
// ??????????????
uint16_t usRegInputStart = REG_INPUT_START;

// ????????????
uint16_t usRegHoldingBuf[REG_HOLDING_NREGS] = {0xAABB, 0xCCDD, 0xEEFF, 0x0011, 0x2233, 0x4455, 0x6677, 0x8899};
// ??????????????
uint16_t usRegHoldingStart = REG_HOLDING_START;

// ?????
uint8_t ucRegCoilsBuf[REG_COILS_SIZE / 8] = {0x01, 0x02};
// ??????????
uint8_t ucRegDiscreteBuf[REG_DISCRETE_SIZE / 8] = {0x01, 0x02};

/**
 * @brief ???????????????????????? 04 eMBFuncReadInputRegister.
 * @note ??��???????? ??????:
 * SlaveAddr(1 Byte)+FuncCode(1 Byte)
 * +StartAddrHiByte(1 Byte)+StartAddrLoByte(1 Byte)
 * +LenAddrHiByte(1 Byte)+LenAddrLoByte(1 Byte)+
 * +CRCAddrHiByte(1 Byte)+CRCAddrLoByte(1 Byte)
 * @param pucRegBuffer ??????????????????????
 * @param usAddress ????????
 * @param usNRegs ??????????????
 * @return eMBErrorCode
 */
eMBErrorCode
eMBRegInputCB(UCHAR *pucRegBuffer, USHORT usAddress, USHORT usNRegs)
{
    eMBErrorCode eStatus = MB_ENOERR;
    int iRegIndex;

    if ((usAddress >= REG_INPUT_START) && (usAddress + usNRegs <= REG_INPUT_START + REG_INPUT_NREGS))
    {
        iRegIndex = (int)(usAddress - usRegInputStart);
        while (usNRegs > 0)
        {
            *pucRegBuffer++ = (UCHAR)(usRegInputBuf[iRegIndex] >> 8);
            *pucRegBuffer++ = (UCHAR)(usRegInputBuf[iRegIndex] & 0xFF);
            iRegIndex++;
            usNRegs--;
        }
    }
    else
    {
        eStatus = MB_ENOREG;
    }
    return eStatus;
}

/**
 * @brief ??????????��?
 * 06 ��???????? eMBFuncWriteHoldingRegister
 * 16 ��??????????? eMBFuncWriteMultipleHoldingRegister
 * 03 ?????????? eMBFuncReadHoldingRegister
 * 23 ??��??????????? eMBFuncReadWriteMultipleHoldingRegister
 *
 * @param pucRegBuffer ??????????????????????
 * @param usAddress ????????
 * @param usNRegs ???��??????????
 * @param eMode ??????
 * @return eMBErrorCode
 */
eMBErrorCode
eMBRegHoldingCB(UCHAR *pucRegBuffer, USHORT usAddress, USHORT usNRegs, eMBRegisterMode eMode)
{
    eMBErrorCode eStatus = MB_ENOERR;
    int iRegIndex = 0;
	//?��????????
    if ((usAddress >= REG_HOLDING_START) &&
        ((usAddress + usNRegs) <= (REG_HOLDING_START + REG_HOLDING_NREGS)))
    {
        iRegIndex = (int)(usAddress - usRegHoldingStart);
		//???????????��???
        switch (eMode)
        {
        case MB_REG_READ: // 读取寄存器 MB_REG_READ = 0
            while (usNRegs > 0)
            {
                uint16_t regValue;
                // 为前几个寄存器提供测试数据
                if (iRegIndex == 0) {
                    regValue = 0x1234;  // 寄存器0的测试值
                } else if (iRegIndex == 1) {
                    regValue = 0x5678;  // 寄存器1的测试值
                } else if (iRegIndex == 2) {
                    regValue = 0x9ABC;  // 寄存器2的测试值
                } else {
                    regValue = usRegHoldingBuf[iRegIndex];  // 其他寄存器使用缓冲区值
                }

                *pucRegBuffer++ = (uint8_t)(regValue >> 8);    // 高字节
                *pucRegBuffer++ = (uint8_t)(regValue & 0xFF);  // 低字节
                iRegIndex++;
                usNRegs--;
            }
            break;
        case MB_REG_WRITE: // ��???? MB_REG_WRITE = 1
            while (usNRegs > 0)
            {
                usRegHoldingBuf[iRegIndex] = *pucRegBuffer++ << 8;  //??8��???
                usRegHoldingBuf[iRegIndex] |= *pucRegBuffer++;  //??8��???
                iRegIndex++;
                usNRegs--;
            }
        }
    }
    else  //??????
    {
        eStatus = MB_ENOREG;
    }
    return eStatus;
}

extern void xMBUtilSetBits(UCHAR *ucByteBuf, USHORT usBitOffset, UCHAR ucNBits,
                           UCHAR ucValue);
extern UCHAR xMBUtilGetBits(UCHAR *ucByteBuf, USHORT usBitOffset, UCHAR ucNBits);
/**
 * @brief ??????????��?01 ????? eMBFuncReadCoils
 * 05 ��??? eMBFuncWriteCoil
 * 15 ��?????? eMBFuncWriteMultipleCoils
 *
 * @note ??????
 *
 * @param pucRegBuffer ??????????????????????
 * @param usAddress ??????
 * @param usNCoils ???��?????????
 * @param eMode ??????
 * @return eMBErrorCode
 */
eMBErrorCode
eMBRegCoilsCB(UCHAR *pucRegBuffer, USHORT usAddress, USHORT usNCoils,
              eMBRegisterMode eMode)
{
    // ??????
    eMBErrorCode eStatus = MB_ENOERR;
    // ?????????
    int16_t iNCoils = (int16_t)usNCoils;
    // ??????????
    int16_t usBitOffset;

    // ?????????????????��??
    if (((int16_t)usAddress >= REG_COILS_START) &&
        (usAddress + usNCoils <= REG_COILS_START + REG_COILS_SIZE))
    {
        // ?????????????
        usBitOffset = (int16_t)(usAddress - REG_COILS_START);
        switch (eMode)
        {
            // ??????
        case MB_REG_READ:
            while (iNCoils > 0)
            {
                *pucRegBuffer++ = xMBUtilGetBits(ucRegCoilsBuf, usBitOffset,
                                                 (uint8_t)(iNCoils > 8 ? 8 : iNCoils));
                iNCoils -= 8;
                usBitOffset += 8;
            }
            break;

            // ��????
        case MB_REG_WRITE:
            while (iNCoils > 0)
            {
                xMBUtilSetBits(ucRegCoilsBuf, usBitOffset,
                               (uint8_t)(iNCoils > 8 ? 8 : iNCoils),
                               *pucRegBuffer++);
                iNCoils -= 8;
            }
            break;
        }
    }
    else
    {
        eStatus = MB_ENOREG;
    }
    return eStatus;
}

/**
 * @brief ???????????????????????��?
 *  02 ?????????? eMBFuncReadDiscreteInputs
 *
 * @param pucRegBuffer ??????????????????????
 *
 * @param usAddress ????????
 * @param usNDiscrete ??????????????
 * @return eMBErrorCode
 */
eMBErrorCode
eMBRegDiscreteCB(UCHAR *pucRegBuffer, USHORT usAddress, USHORT usNDiscrete)
{
    // ??????
    eMBErrorCode eStatus = MB_ENOERR;
    // ?????????????
    int16_t iNDiscrete = (int16_t)usNDiscrete;
    // ?????
    uint16_t usBitOffset;

    // ?��???????????????��??
    if (((int16_t)usAddress >= REG_DISCRETE_START) &&
        (usAddress + usNDiscrete <= REG_DISCRETE_START + REG_DISCRETE_SIZE))
    {
        // ????????
        usBitOffset = (uint16_t)(usAddress - REG_DISCRETE_START);

        while (iNDiscrete > 0)
        {
            *pucRegBuffer++ = xMBUtilGetBits(ucRegDiscreteBuf, usBitOffset,
                                             (uint8_t)(iNDiscrete > 8 ? 8 : iNDiscrete));
            iNDiscrete -= 8;
            usBitOffset += 8;
        }
    }
    else
    {
        eStatus = MB_ENOREG;
    }
    return eStatus;
}

