// modbus_test.c - FreeModbus测试和调试功能
#include "modbus_test.h"
#include "mb.h"
#include "gpio.h"
#include "usart.h"
#include <stdio.h>

// 测试数据
static uint16_t test_holding_regs[10] = {
    0x1234, 0x5678, 0x9ABC, 0xDEF0, 0x1111,
    0x2222, 0x3333, 0x4444, 0x5555, 0x6666
};

// 调试计数器
static uint32_t rx_count = 0;
static uint32_t tx_count = 0;
static uint32_t timer_count = 0;
static uint32_t error_count = 0;
static uint32_t poll_count = 0;
static uint32_t init_count = 0;
static uint32_t callback_count = 0;

/**
 * @brief 初始化Modbus测试功能
 */
void Modbus_Test_Init(void)
{
    // 初始化测试数据
    for (int i = 0; i < 10; i++) {
        test_holding_regs[i] = 0x1000 + i;
    }
    
    // 重置计数器
    rx_count = 0;
    tx_count = 0;
    timer_count = 0;
    error_count = 0;
    poll_count = 0;
    init_count++;
}

/**
 * @brief 更新测试寄存器数据
 */
void Modbus_Test_Update_Registers(void)
{
    static uint32_t update_counter = 0;
    update_counter++;
    
    // 更新一些测试数据
    test_holding_regs[0] = (uint16_t)(update_counter & 0xFFFF);
    test_holding_regs[1] = (uint16_t)((update_counter >> 16) & 0xFFFF);
    test_holding_regs[2] = (uint16_t)(rx_count & 0xFFFF);
    test_holding_regs[3] = (uint16_t)(tx_count & 0xFFFF);
    test_holding_regs[4] = (uint16_t)(timer_count & 0xFFFF);
    test_holding_regs[5] = (uint16_t)(error_count & 0xFFFF);
    test_holding_regs[6] = (uint16_t)(poll_count & 0xFFFF);
    test_holding_regs[7] = (uint16_t)(init_count & 0xFFFF);
    test_holding_regs[8] = (uint16_t)(callback_count & 0xFFFF);
    test_holding_regs[9] = 0x1234; // 固定测试值
}

/**
 * @brief 获取测试寄存器数据
 */
uint16_t* Modbus_Test_Get_Holding_Regs(void)
{
    return test_holding_regs;
}

/**
 * @brief 接收字节计数（调试用）
 */
void Modbus_Test_Inc_Rx_Count(void)
{
    rx_count++;
}

/**
 * @brief 发送字节计数（调试用）
 */
void Modbus_Test_Inc_Tx_Count(void)
{
    tx_count++;
}

/**
 * @brief 定时器中断计数（调试用）
 */
void Modbus_Test_Inc_Timer_Count(void)
{
    timer_count++;
}

/**
 * @brief 错误计数（调试用）
 */
void Modbus_Test_Inc_Error_Count(void)
{
    error_count++;
}

/**
 * @brief 轮询计数（调试用）
 */
void Modbus_Test_Inc_Poll_Count(void)
{
    poll_count++;
}

/**
 * @brief 回调计数（调试用）
 */
void Modbus_Test_Inc_Callback_Count(void)
{
    callback_count++;
}

/**
 * @brief 获取调试统计信息
 */
void Modbus_Test_Get_Stats(ModbusTestStats* stats)
{
    if (stats) {
        stats->rx_count = rx_count;
        stats->tx_count = tx_count;
        stats->timer_count = timer_count;
        stats->error_count = error_count;
    }
}

/**
 * @brief 打印调试信息（如果有串口调试）
 */
void Modbus_Test_Print_Stats(void)
{
    // 如果需要串口调试输出，可以在这里添加printf
    // printf("Modbus Stats: RX=%lu, TX=%lu, Timer=%lu, Error=%lu\r\n", 
    //        rx_count, tx_count, timer_count, error_count);
}

/**
 * @brief LED指示Modbus状态
 */
void Modbus_Test_LED_Indicate(ModbusTestLEDState state)
{
    switch (state) {
        case MODBUS_LED_INIT:
            // 初始化时LED快闪
            HAL_GPIO_WritePin(LED_GPIO_Port, LED_Pin, GPIO_PIN_RESET);
            break;
            
        case MODBUS_LED_RUNNING:
            // 正常运行时LED慢闪
            HAL_GPIO_TogglePin(LED_GPIO_Port, LED_Pin);
            break;
            
        case MODBUS_LED_ERROR:
            // 错误时LED常亮
            HAL_GPIO_WritePin(LED_GPIO_Port, LED_Pin, GPIO_PIN_SET);
            break;
            
        case MODBUS_LED_COMM:
            // 通信时LED快速闪烁
            HAL_GPIO_TogglePin(LED_GPIO_Port, LED_Pin);
            break;
            
        default:
            break;
    }
}

/**
 * @brief 简单的Modbus通信测试
 * @return 0: 成功, 其他: 错误码
 */
int Modbus_Test_Communication(void)
{
    eMBErrorCode eStatus;
    
    // 检查Modbus状态
    eStatus = eMBPoll();
    
    if (eStatus != MB_ENOERR) {
        error_count++;
        return -1;
    }
    
    return 0;
}
