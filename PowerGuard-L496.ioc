#MicroXplorer Configuration settings - do not modify
CAN1.ABOM=ENABLE
CAN1.BS1=CAN_BS1_11TQ
CAN1.BS2=CAN_BS2_4TQ
CAN1.CalculateBaudRate=125000
CAN1.CalculateTimeBit=8000
CAN1.CalculateTimeQuantum=500.0
CAN1.IPParameters=CalculateTimeQuantum,CalculateTimeBit,CalculateBaudRate,Prescaler,BS1,BS2,SJW,ABOM,NART
CAN1.NART=ENABLE
CAN1.Prescaler=40
CAN1.SJW=CAN_SJW_2TQ
FREERTOS.FootprintOK=true
FREERTOS.IPParameters=Tasks01,configTOTAL_HEAP_SIZE,FootprintOK
FREERTOS.Tasks01=energymeterTask,48,512,EnergyMeterTask,Default,NULL,Dynamic,NULL,NULL;modbuscomTask,40,512,Modbus<PERSON>omTask,Default,NULL,Dynamic,NULL,NULL;cancomTask,24,256,<PERSON><PERSON><PERSON>T<PERSON>,Default,NULL,Dynamic,NULL,NULL;sensorsTask,8,256,Sen<PERSON>T<PERSON>,De<PERSON>ult,NULL,Dynamic,NULL,NULL;sysmonitorTask,8,256,SysMonitorTask,Default,NULL,Dynamic,NULL,NULL
FREERTOS.configTOTAL_HEAP_SIZE=8192
File.Version=6
GPIO.groupedBy=Group By Peripherals
I2C1.I2C_Fall_Time=50
I2C1.I2C_Rise_Time=150
I2C1.IPParameters=Timing,I2C_Rise_Time,I2C_Fall_Time
I2C1.Timing=0x10F09CE4
I2C2.IPParameters=Timing
I2C2.Timing=0x10909CEC
IWDG.IPParameters=Prescaler
IWDG.Prescaler=IWDG_PRESCALER_16
KeepUserPlacement=false
Mcu.Family=STM32L4
Mcu.IP0=CAN1
Mcu.IP1=FREERTOS
Mcu.IP10=USART1
Mcu.IP2=I2C1
Mcu.IP3=I2C2
Mcu.IP4=IWDG
Mcu.IP5=NVIC
Mcu.IP6=RCC
Mcu.IP7=SPI2
Mcu.IP8=SYS
Mcu.IP9=TIM16
Mcu.IPNb=11
Mcu.Name=STM32L496R(E-G)Tx
Mcu.Package=LQFP64
Mcu.Pin0=PC13
Mcu.Pin1=PH0-OSC_IN (PH0)
Mcu.Pin10=PB10
Mcu.Pin11=PB11
Mcu.Pin12=PB12
Mcu.Pin13=PB13
Mcu.Pin14=PB14
Mcu.Pin15=PB15
Mcu.Pin16=PA8
Mcu.Pin17=PA9
Mcu.Pin18=PA10
Mcu.Pin19=PA11
Mcu.Pin2=PH1-OSC_OUT (PH1)
Mcu.Pin20=PA12
Mcu.Pin21=PA13 (JTMS/SWDIO)
Mcu.Pin22=PA14 (JTCK/SWCLK)
Mcu.Pin23=PB6
Mcu.Pin24=PB7
Mcu.Pin25=VP_FREERTOS_VS_CMSIS_V2
Mcu.Pin26=VP_IWDG_VS_IWDG
Mcu.Pin27=VP_SYS_VS_tim6
Mcu.Pin28=VP_TIM16_VS_ClockSourceINT
Mcu.Pin3=PA0
Mcu.Pin4=PA1
Mcu.Pin5=PA2
Mcu.Pin6=PA3
Mcu.Pin7=PA4
Mcu.Pin8=PB1
Mcu.Pin9=PB2
Mcu.PinsNb=29
Mcu.ThirdPartyNb=0
Mcu.UserConstants=
Mcu.UserName=STM32L496RETx
MxCube.Version=6.4.0
MxDb.Version=DB.6.0.40
NVIC.BusFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.CAN1_RX0_IRQn=true\:5\:0\:false\:false\:true\:true\:true\:true
NVIC.DebugMonitor_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.EXTI1_IRQn=true\:5\:0\:true\:false\:true\:false\:true\:true
NVIC.EXTI2_IRQn=true\:5\:0\:true\:false\:true\:false\:true\:true
NVIC.ForceEnableDMAVector=true
NVIC.HardFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.MemoryManagement_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.NonMaskableInt_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.PendSV_IRQn=true\:15\:0\:false\:false\:false\:true\:false\:false
NVIC.PriorityGroup=NVIC_PRIORITYGROUP_4
NVIC.SVCall_IRQn=true\:0\:0\:false\:false\:false\:false\:false\:false
NVIC.SavedPendsvIrqHandlerGenerated=true
NVIC.SavedSvcallIrqHandlerGenerated=true
NVIC.SavedSystickIrqHandlerGenerated=true
NVIC.SysTick_IRQn=true\:15\:0\:false\:false\:false\:true\:false\:true
NVIC.TIM1_UP_TIM16_IRQn=true\:5\:0\:false\:false\:true\:true\:true\:true
NVIC.TIM6_DAC_IRQn=true\:15\:0\:false\:false\:true\:false\:false\:true
NVIC.TimeBase=TIM6_DAC_IRQn
NVIC.TimeBaseIP=TIM6
NVIC.USART1_IRQn=true\:5\:0\:false\:false\:true\:true\:true\:true
NVIC.UsageFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
PA0.Locked=true
PA0.Signal=GPIO_Output
PA1.GPIOParameters=GPIO_PuPd
PA1.GPIO_PuPd=GPIO_PULLUP
PA1.Locked=true
PA1.Signal=GPIO_Input
PA10.Mode=Asynchronous
PA10.Signal=USART1_RX
PA11.Mode=CAN_Activate
PA11.Signal=CAN1_RX
PA12.Mode=CAN_Activate
PA12.Signal=CAN1_TX
PA13\ (JTMS/SWDIO).Mode=Serial_Wire
PA13\ (JTMS/SWDIO).Signal=SYS_JTMS-SWDIO
PA14\ (JTCK/SWCLK).Mode=Serial_Wire
PA14\ (JTCK/SWCLK).Signal=SYS_JTCK-SWCLK
PA2.GPIOParameters=GPIO_PuPd
PA2.GPIO_PuPd=GPIO_PULLUP
PA2.Locked=true
PA2.Signal=GPIO_Input
PA3.GPIOParameters=GPIO_PuPd
PA3.GPIO_PuPd=GPIO_PULLUP
PA3.Locked=true
PA3.Signal=GPIO_Input
PA4.GPIOParameters=GPIO_PuPd
PA4.GPIO_PuPd=GPIO_PULLUP
PA4.Locked=true
PA4.Signal=GPIO_Input
PA8.Locked=true
PA8.Signal=GPIO_Output
PA9.Locked=true
PA9.Mode=Asynchronous
PA9.Signal=USART1_TX
PB1.GPIOParameters=GPIO_PuPd
PB1.GPIO_PuPd=GPIO_PULLUP
PB1.Locked=true
PB1.Signal=GPXTI1
PB10.Mode=I2C
PB10.Signal=I2C2_SCL
PB11.Mode=I2C
PB11.Signal=I2C2_SDA
PB12.Locked=true
PB12.Signal=GPIO_Output
PB13.Mode=Full_Duplex_Master
PB13.Signal=SPI2_SCK
PB14.Locked=true
PB14.Mode=Full_Duplex_Master
PB14.Signal=SPI2_MISO
PB15.Locked=true
PB15.Mode=Full_Duplex_Master
PB15.Signal=SPI2_MOSI
PB2.GPIOParameters=GPIO_PuPd
PB2.GPIO_PuPd=GPIO_PULLUP
PB2.Locked=true
PB2.Signal=GPXTI2
PB6.Locked=true
PB6.Mode=I2C
PB6.Signal=I2C1_SCL
PB7.Mode=I2C
PB7.Signal=I2C1_SDA
PC13.Locked=true
PC13.Signal=GPIO_Output
PH0-OSC_IN\ (PH0).Mode=HSE-External-Oscillator
PH0-OSC_IN\ (PH0).Signal=RCC_OSC_IN
PH1-OSC_OUT\ (PH1).Mode=HSE-External-Oscillator
PH1-OSC_OUT\ (PH1).Signal=RCC_OSC_OUT
PinOutPanel.RotationAngle=0
ProjectManager.AskForMigrate=true
ProjectManager.BackupPrevious=false
ProjectManager.CompilerOptimize=6
ProjectManager.ComputerToolchain=false
ProjectManager.CoupleFile=true
ProjectManager.CustomerFirmwarePackage=
ProjectManager.DefaultFWLocation=true
ProjectManager.DeletePrevious=true
ProjectManager.DeviceId=STM32L496RETx
ProjectManager.FirmwarePackage=STM32Cube FW_L4 V1.17.2
ProjectManager.FreePins=false
ProjectManager.HalAssertFull=false
ProjectManager.HeapSize=0x200
ProjectManager.KeepUserCode=true
ProjectManager.LastFirmware=true
ProjectManager.LibraryCopy=1
ProjectManager.MainLocation=Core/Src
ProjectManager.NoMain=false
ProjectManager.PreviousToolchain=
ProjectManager.ProjectBuild=false
ProjectManager.ProjectFileName=PowerGuard-L496.ioc
ProjectManager.ProjectName=PowerGuard-L496
ProjectManager.RegisterCallBack=
ProjectManager.StackSize=0x400
ProjectManager.TargetToolchain=MDK-ARM V5.32
ProjectManager.ToolChainLocation=
ProjectManager.UnderRoot=false
ProjectManager.functionlistsort=1-MX_GPIO_Init-GPIO-false-HAL-true,2-SystemClock_Config-RCC-false-HAL-false,3-MX_CAN1_Init-CAN1-false-HAL-true,4-MX_I2C1_Init-I2C1-false-HAL-true,5-MX_I2C2_Init-I2C2-false-HAL-true,6-MX_SPI2_Init-SPI2-false-HAL-true,7-MX_USART1_UART_Init-USART1-false-HAL-true,8-MX_IWDG_Init-IWDG-false-HAL-true,9-MX_TIM16_Init-TIM16-false-HAL-true
RCC.ADCFreq_Value=32000000
RCC.AHBFreq_Value=80000000
RCC.APB1Freq_Value=80000000
RCC.APB1TimFreq_Value=80000000
RCC.APB2Freq_Value=80000000
RCC.APB2TimFreq_Value=80000000
RCC.CortexFreq_Value=80000000
RCC.DFSDMFreq_Value=80000000
RCC.FCLKCortexFreq_Value=80000000
RCC.FamilyName=M
RCC.HCLKFreq_Value=80000000
RCC.HSE_VALUE=8000000
RCC.HSI48_VALUE=48000000
RCC.HSI_VALUE=16000000
RCC.I2C1Freq_Value=80000000
RCC.I2C2Freq_Value=80000000
RCC.I2C3Freq_Value=80000000
RCC.I2C4Freq_Value=80000000
RCC.IPParameters=ADCFreq_Value,AHBFreq_Value,APB1Freq_Value,APB1TimFreq_Value,APB2Freq_Value,APB2TimFreq_Value,CortexFreq_Value,DFSDMFreq_Value,FCLKCortexFreq_Value,FamilyName,HCLKFreq_Value,HSE_VALUE,HSI48_VALUE,HSI_VALUE,I2C1Freq_Value,I2C2Freq_Value,I2C3Freq_Value,I2C4Freq_Value,LPTIM1Freq_Value,LPTIM2Freq_Value,LPUART1Freq_Value,LSCOPinFreq_Value,LSE_VALUE,LSI_VALUE,MCO1PinFreq_Value,MSI_VALUE,PLLN,PLLPoutputFreq_Value,PLLQoutputFreq_Value,PLLRCLKFreq_Value,PLLSAI1PoutputFreq_Value,PLLSAI1QoutputFreq_Value,PLLSAI1RoutputFreq_Value,PLLSAI2PoutputFreq_Value,PLLSAI2RoutputFreq_Value,PLLSourceVirtual,PWRFreq_Value,RNGFreq_Value,SAI1Freq_Value,SAI2Freq_Value,SDMMCFreq_Value,SWPMI1Freq_Value,SYSCLKFreq_VALUE,SYSCLKSource,UART4Freq_Value,UART5Freq_Value,USART1Freq_Value,USART2Freq_Value,USART3Freq_Value,USBFreq_Value,VCOInputFreq_Value,VCOOutputFreq_Value,VCOSAI1OutputFreq_Value,VCOSAI2OutputFreq_Value
RCC.LPTIM1Freq_Value=80000000
RCC.LPTIM2Freq_Value=80000000
RCC.LPUART1Freq_Value=80000000
RCC.LSCOPinFreq_Value=32000
RCC.LSE_VALUE=32768
RCC.LSI_VALUE=32000
RCC.MCO1PinFreq_Value=80000000
RCC.MSI_VALUE=4000000
RCC.PLLN=20
RCC.PLLPoutputFreq_Value=80000000
RCC.PLLQoutputFreq_Value=80000000
RCC.PLLRCLKFreq_Value=80000000
RCC.PLLSAI1PoutputFreq_Value=32000000
RCC.PLLSAI1QoutputFreq_Value=32000000
RCC.PLLSAI1RoutputFreq_Value=32000000
RCC.PLLSAI2PoutputFreq_Value=32000000
RCC.PLLSAI2RoutputFreq_Value=32000000
RCC.PLLSourceVirtual=RCC_PLLSOURCE_HSE
RCC.PWRFreq_Value=80000000
RCC.RNGFreq_Value=32000000
RCC.SAI1Freq_Value=32000000
RCC.SAI2Freq_Value=32000000
RCC.SDMMCFreq_Value=32000000
RCC.SWPMI1Freq_Value=80000000
RCC.SYSCLKFreq_VALUE=80000000
RCC.SYSCLKSource=RCC_SYSCLKSOURCE_PLLCLK
RCC.UART4Freq_Value=80000000
RCC.UART5Freq_Value=80000000
RCC.USART1Freq_Value=80000000
RCC.USART2Freq_Value=80000000
RCC.USART3Freq_Value=80000000
RCC.USBFreq_Value=32000000
RCC.VCOInputFreq_Value=8000000
RCC.VCOOutputFreq_Value=160000000
RCC.VCOSAI1OutputFreq_Value=64000000
RCC.VCOSAI2OutputFreq_Value=64000000
SH.GPXTI1.0=GPIO_EXTI1
SH.GPXTI1.ConfNb=1
SH.GPXTI2.0=GPIO_EXTI2
SH.GPXTI2.ConfNb=1
SPI2.BaudRatePrescaler=SPI_BAUDRATEPRESCALER_64
SPI2.CalculateBaudRate=1.25 MBits/s
SPI2.DataSize=SPI_DATASIZE_8BIT
SPI2.Direction=SPI_DIRECTION_2LINES
SPI2.IPParameters=VirtualType,Mode,Direction,CalculateBaudRate,DataSize,BaudRatePrescaler
SPI2.Mode=SPI_MODE_MASTER
SPI2.VirtualType=VM_MASTER
TIM16.AutoReloadPreload=TIM_AUTORELOAD_PRELOAD_ENABLE
TIM16.IPParameters=Prescaler,Period,AutoReloadPreload
TIM16.Period=999
TIM16.Prescaler=79
USART1.BaudRate=19200
USART1.IPParameters=VirtualMode-Asynchronous,BaudRate,Parity
USART1.Parity=PARITY_EVEN
USART1.VirtualMode-Asynchronous=VM_ASYNC
VP_FREERTOS_VS_CMSIS_V2.Mode=CMSIS_V2
VP_FREERTOS_VS_CMSIS_V2.Signal=FREERTOS_VS_CMSIS_V2
VP_IWDG_VS_IWDG.Mode=IWDG_Activate
VP_IWDG_VS_IWDG.Signal=IWDG_VS_IWDG
VP_SYS_VS_tim6.Mode=TIM6
VP_SYS_VS_tim6.Signal=SYS_VS_tim6
VP_TIM16_VS_ClockSourceINT.Mode=Enable_Timer
VP_TIM16_VS_ClockSourceINT.Signal=TIM16_VS_ClockSourceINT
board=custom
