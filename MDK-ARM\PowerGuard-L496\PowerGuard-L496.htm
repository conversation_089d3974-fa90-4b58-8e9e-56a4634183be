<!doctype html public "-//w3c//dtd html 4.0 transitional//en">
<html><head>
<title>Static Call Graph - [PowerGuard-L496\PowerGuard-L496.axf]</title></head>
<body><HR>
<H1>Static Call Graph for image PowerGuard-L496\PowerGuard-L496.axf</H1><HR>
<BR><P>#&#060CALLGRAPH&#062# ARM Linker, 5060750: Last Updated: Tue Jun 24 15:40:10 2025
<BR><P>
<H3>Maximum Stack Usage =        280 bytes + Unknown(Functions without stacksize, Cycles, Untraceable Function Pointers)</H3><H3>
Call chain for Maximum Stack Depth:</H3>
__rt_entry_main &rArr; main &rArr; MX_I2C2_Init &rArr; HAL_I2C_Init &rArr; HAL_I2C_MspInit &rArr; HAL_RCCEx_PeriphCLKConfig &rArr; RCCEx_PLLSAI2_Config
<P>
<H3>
Functions with no stack information
</H3><UL>
 <LI><a href="#[9c]">__user_initial_stackheap</a>
</UL>
</UL>
<P>
<H3>
Mutually Recursive functions
</H3> <LI><a href="#[29]">ADC1_2_IRQHandler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[29]">ADC1_2_IRQHandler</a><BR>
 <LI><a href="#[11]">BusFault_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[11]">BusFault_Handler</a><BR>
 <LI><a href="#[76]">CANComTask</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[76]">CANComTask</a><BR>
 <LI><a href="#[74]">EnergyMeterTask</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[74]">EnergyMeterTask</a><BR>
 <LI><a href="#[f]">HardFault_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[f]">HardFault_Handler</a><BR>
 <LI><a href="#[10]">MemManage_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[10]">MemManage_Handler</a><BR>
 <LI><a href="#[e]">NMI_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[e]">NMI_Handler</a><BR>
 <LI><a href="#[77]">SensorsTask</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[77]">SensorsTask</a><BR>
 <LI><a href="#[78]">SysMonitorTask</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[78]">SysMonitorTask</a><BR>
 <LI><a href="#[e2]">UART_EndRxTransfer</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[e2]">UART_EndRxTransfer</a><BR>
 <LI><a href="#[12]">UsageFault_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[12]">UsageFault_Handler</a><BR>
</UL>
<P>
<H3>
Function Pointers
</H3><UL>
 <LI><a href="#[29]">ADC1_2_IRQHandler</a> from startup_stm32l496xx.o(.text) referenced from startup_stm32l496xx.o(RESET)
 <LI><a href="#[46]">ADC3_IRQHandler</a> from startup_stm32l496xx.o(.text) referenced from startup_stm32l496xx.o(RESET)
 <LI><a href="#[11]">BusFault_Handler</a> from stm32l4xx_it.o(i.BusFault_Handler) referenced from startup_stm32l496xx.o(RESET)
 <LI><a href="#[2b]">CAN1_RX0_IRQHandler</a> from stm32l4xx_it.o(i.CAN1_RX0_IRQHandler) referenced from startup_stm32l496xx.o(RESET)
 <LI><a href="#[2c]">CAN1_RX1_IRQHandler</a> from startup_stm32l496xx.o(.text) referenced from startup_stm32l496xx.o(RESET)
 <LI><a href="#[2d]">CAN1_SCE_IRQHandler</a> from startup_stm32l496xx.o(.text) referenced from startup_stm32l496xx.o(RESET)
 <LI><a href="#[2a]">CAN1_TX_IRQHandler</a> from startup_stm32l496xx.o(.text) referenced from startup_stm32l496xx.o(RESET)
 <LI><a href="#[6d]">CAN2_RX0_IRQHandler</a> from startup_stm32l496xx.o(.text) referenced from startup_stm32l496xx.o(RESET)
 <LI><a href="#[6e]">CAN2_RX1_IRQHandler</a> from startup_stm32l496xx.o(.text) referenced from startup_stm32l496xx.o(RESET)
 <LI><a href="#[6f]">CAN2_SCE_IRQHandler</a> from startup_stm32l496xx.o(.text) referenced from startup_stm32l496xx.o(RESET)
 <LI><a href="#[6c]">CAN2_TX_IRQHandler</a> from startup_stm32l496xx.o(.text) referenced from startup_stm32l496xx.o(RESET)
 <LI><a href="#[76]">CANComTask</a> from freertos.o(i.CANComTask) referenced from freertos.o(i.MX_FREERTOS_Init)
 <LI><a href="#[57]">COMP_IRQHandler</a> from startup_stm32l496xx.o(.text) referenced from startup_stm32l496xx.o(RESET)
 <LI><a href="#[68]">CRS_IRQHandler</a> from startup_stm32l496xx.o(.text) referenced from startup_stm32l496xx.o(RESET)
 <LI><a href="#[6b]">DCMI_IRQHandler</a> from startup_stm32l496xx.o(.text) referenced from startup_stm32l496xx.o(RESET)
 <LI><a href="#[54]">DFSDM1_FLT0_IRQHandler</a> from startup_stm32l496xx.o(.text) referenced from startup_stm32l496xx.o(RESET)
 <LI><a href="#[55]">DFSDM1_FLT1_IRQHandler</a> from startup_stm32l496xx.o(.text) referenced from startup_stm32l496xx.o(RESET)
 <LI><a href="#[56]">DFSDM1_FLT2_IRQHandler</a> from startup_stm32l496xx.o(.text) referenced from startup_stm32l496xx.o(RESET)
 <LI><a href="#[41]">DFSDM1_FLT3_IRQHandler</a> from startup_stm32l496xx.o(.text) referenced from startup_stm32l496xx.o(RESET)
 <LI><a href="#[22]">DMA1_Channel1_IRQHandler</a> from startup_stm32l496xx.o(.text) referenced from startup_stm32l496xx.o(RESET)
 <LI><a href="#[23]">DMA1_Channel2_IRQHandler</a> from startup_stm32l496xx.o(.text) referenced from startup_stm32l496xx.o(RESET)
 <LI><a href="#[24]">DMA1_Channel3_IRQHandler</a> from startup_stm32l496xx.o(.text) referenced from startup_stm32l496xx.o(RESET)
 <LI><a href="#[25]">DMA1_Channel4_IRQHandler</a> from startup_stm32l496xx.o(.text) referenced from startup_stm32l496xx.o(RESET)
 <LI><a href="#[26]">DMA1_Channel5_IRQHandler</a> from startup_stm32l496xx.o(.text) referenced from startup_stm32l496xx.o(RESET)
 <LI><a href="#[27]">DMA1_Channel6_IRQHandler</a> from startup_stm32l496xx.o(.text) referenced from startup_stm32l496xx.o(RESET)
 <LI><a href="#[28]">DMA1_Channel7_IRQHandler</a> from startup_stm32l496xx.o(.text) referenced from startup_stm32l496xx.o(RESET)
 <LI><a href="#[70]">DMA2D_IRQHandler</a> from startup_stm32l496xx.o(.text) referenced from startup_stm32l496xx.o(RESET)
 <LI><a href="#[4f]">DMA2_Channel1_IRQHandler</a> from startup_stm32l496xx.o(.text) referenced from startup_stm32l496xx.o(RESET)
 <LI><a href="#[50]">DMA2_Channel2_IRQHandler</a> from startup_stm32l496xx.o(.text) referenced from startup_stm32l496xx.o(RESET)
 <LI><a href="#[51]">DMA2_Channel3_IRQHandler</a> from startup_stm32l496xx.o(.text) referenced from startup_stm32l496xx.o(RESET)
 <LI><a href="#[52]">DMA2_Channel4_IRQHandler</a> from startup_stm32l496xx.o(.text) referenced from startup_stm32l496xx.o(RESET)
 <LI><a href="#[53]">DMA2_Channel5_IRQHandler</a> from startup_stm32l496xx.o(.text) referenced from startup_stm32l496xx.o(RESET)
 <LI><a href="#[5b]">DMA2_Channel6_IRQHandler</a> from startup_stm32l496xx.o(.text) referenced from startup_stm32l496xx.o(RESET)
 <LI><a href="#[5c]">DMA2_Channel7_IRQHandler</a> from startup_stm32l496xx.o(.text) referenced from startup_stm32l496xx.o(RESET)
 <LI><a href="#[14]">DebugMon_Handler</a> from stm32l4xx_it.o(i.DebugMon_Handler) referenced from startup_stm32l496xx.o(RESET)
 <LI><a href="#[1d]">EXTI0_IRQHandler</a> from startup_stm32l496xx.o(.text) referenced from startup_stm32l496xx.o(RESET)
 <LI><a href="#[3f]">EXTI15_10_IRQHandler</a> from startup_stm32l496xx.o(.text) referenced from startup_stm32l496xx.o(RESET)
 <LI><a href="#[1e]">EXTI1_IRQHandler</a> from stm32l4xx_it.o(i.EXTI1_IRQHandler) referenced from startup_stm32l496xx.o(RESET)
 <LI><a href="#[1f]">EXTI2_IRQHandler</a> from stm32l4xx_it.o(i.EXTI2_IRQHandler) referenced from startup_stm32l496xx.o(RESET)
 <LI><a href="#[20]">EXTI3_IRQHandler</a> from startup_stm32l496xx.o(.text) referenced from startup_stm32l496xx.o(RESET)
 <LI><a href="#[21]">EXTI4_IRQHandler</a> from startup_stm32l496xx.o(.text) referenced from startup_stm32l496xx.o(RESET)
 <LI><a href="#[2e]">EXTI9_5_IRQHandler</a> from startup_stm32l496xx.o(.text) referenced from startup_stm32l496xx.o(RESET)
 <LI><a href="#[74]">EnergyMeterTask</a> from freertos.o(i.EnergyMeterTask) referenced from freertos.o(i.MX_FREERTOS_Init)
 <LI><a href="#[1b]">FLASH_IRQHandler</a> from startup_stm32l496xx.o(.text) referenced from startup_stm32l496xx.o(RESET)
 <LI><a href="#[47]">FMC_IRQHandler</a> from startup_stm32l496xx.o(.text) referenced from startup_stm32l496xx.o(RESET)
 <LI><a href="#[67]">FPU_IRQHandler</a> from startup_stm32l496xx.o(.text) referenced from startup_stm32l496xx.o(RESET)
 <LI><a href="#[f]">HardFault_Handler</a> from stm32l4xx_it.o(i.HardFault_Handler) referenced from startup_stm32l496xx.o(RESET)
 <LI><a href="#[37]">I2C1_ER_IRQHandler</a> from startup_stm32l496xx.o(.text) referenced from startup_stm32l496xx.o(RESET)
 <LI><a href="#[36]">I2C1_EV_IRQHandler</a> from startup_stm32l496xx.o(.text) referenced from startup_stm32l496xx.o(RESET)
 <LI><a href="#[39]">I2C2_ER_IRQHandler</a> from startup_stm32l496xx.o(.text) referenced from startup_stm32l496xx.o(RESET)
 <LI><a href="#[38]">I2C2_EV_IRQHandler</a> from startup_stm32l496xx.o(.text) referenced from startup_stm32l496xx.o(RESET)
 <LI><a href="#[60]">I2C3_ER_IRQHandler</a> from startup_stm32l496xx.o(.text) referenced from startup_stm32l496xx.o(RESET)
 <LI><a href="#[5f]">I2C3_EV_IRQHandler</a> from startup_stm32l496xx.o(.text) referenced from startup_stm32l496xx.o(RESET)
 <LI><a href="#[6a]">I2C4_ER_IRQHandler</a> from startup_stm32l496xx.o(.text) referenced from startup_stm32l496xx.o(RESET)
 <LI><a href="#[69]">I2C4_EV_IRQHandler</a> from startup_stm32l496xx.o(.text) referenced from startup_stm32l496xx.o(RESET)
 <LI><a href="#[65]">LCD_IRQHandler</a> from startup_stm32l496xx.o(.text) referenced from startup_stm32l496xx.o(RESET)
 <LI><a href="#[58]">LPTIM1_IRQHandler</a> from startup_stm32l496xx.o(.text) referenced from startup_stm32l496xx.o(RESET)
 <LI><a href="#[59]">LPTIM2_IRQHandler</a> from startup_stm32l496xx.o(.text) referenced from startup_stm32l496xx.o(RESET)
 <LI><a href="#[5d]">LPUART1_IRQHandler</a> from startup_stm32l496xx.o(.text) referenced from startup_stm32l496xx.o(RESET)
 <LI><a href="#[10]">MemManage_Handler</a> from stm32l4xx_it.o(i.MemManage_Handler) referenced from startup_stm32l496xx.o(RESET)
 <LI><a href="#[75]">ModbusComTask</a> from freertos.o(i.ModbusComTask) referenced from freertos.o(i.MX_FREERTOS_Init)
 <LI><a href="#[e]">NMI_Handler</a> from stm32l4xx_it.o(i.NMI_Handler) referenced from startup_stm32l496xx.o(RESET)
 <LI><a href="#[5a]">OTG_FS_IRQHandler</a> from startup_stm32l496xx.o(.text) referenced from startup_stm32l496xx.o(RESET)
 <LI><a href="#[18]">PVD_PVM_IRQHandler</a> from startup_stm32l496xx.o(.text) referenced from startup_stm32l496xx.o(RESET)
 <LI><a href="#[15]">PendSV_Handler</a> from port.o(.emb_text) referenced from startup_stm32l496xx.o(RESET)
 <LI><a href="#[5e]">QUADSPI_IRQHandler</a> from startup_stm32l496xx.o(.text) referenced from startup_stm32l496xx.o(RESET)
 <LI><a href="#[1c]">RCC_IRQHandler</a> from startup_stm32l496xx.o(.text) referenced from startup_stm32l496xx.o(RESET)
 <LI><a href="#[66]">RNG_IRQHandler</a> from startup_stm32l496xx.o(.text) referenced from startup_stm32l496xx.o(RESET)
 <LI><a href="#[40]">RTC_Alarm_IRQHandler</a> from startup_stm32l496xx.o(.text) referenced from startup_stm32l496xx.o(RESET)
 <LI><a href="#[1a]">RTC_WKUP_IRQHandler</a> from startup_stm32l496xx.o(.text) referenced from startup_stm32l496xx.o(RESET)
 <LI><a href="#[d]">Reset_Handler</a> from startup_stm32l496xx.o(.text) referenced from startup_stm32l496xx.o(RESET)
 <LI><a href="#[61]">SAI1_IRQHandler</a> from startup_stm32l496xx.o(.text) referenced from startup_stm32l496xx.o(RESET)
 <LI><a href="#[62]">SAI2_IRQHandler</a> from startup_stm32l496xx.o(.text) referenced from startup_stm32l496xx.o(RESET)
 <LI><a href="#[48]">SDMMC1_IRQHandler</a> from startup_stm32l496xx.o(.text) referenced from startup_stm32l496xx.o(RESET)
 <LI><a href="#[3a]">SPI1_IRQHandler</a> from startup_stm32l496xx.o(.text) referenced from startup_stm32l496xx.o(RESET)
 <LI><a href="#[3b]">SPI2_IRQHandler</a> from startup_stm32l496xx.o(.text) referenced from startup_stm32l496xx.o(RESET)
 <LI><a href="#[4a]">SPI3_IRQHandler</a> from startup_stm32l496xx.o(.text) referenced from startup_stm32l496xx.o(RESET)
 <LI><a href="#[13]">SVC_Handler</a> from port.o(.emb_text) referenced from startup_stm32l496xx.o(RESET)
 <LI><a href="#[63]">SWPMI1_IRQHandler</a> from startup_stm32l496xx.o(.text) referenced from startup_stm32l496xx.o(RESET)
 <LI><a href="#[77]">SensorsTask</a> from freertos.o(i.SensorsTask) referenced from freertos.o(i.MX_FREERTOS_Init)
 <LI><a href="#[78]">SysMonitorTask</a> from freertos.o(i.SysMonitorTask) referenced from freertos.o(i.MX_FREERTOS_Init)
 <LI><a href="#[16]">SysTick_Handler</a> from cmsis_os2.o(i.SysTick_Handler) referenced from startup_stm32l496xx.o(RESET)
 <LI><a href="#[71]">SystemInit</a> from system_stm32l4xx.o(i.SystemInit) referenced from startup_stm32l496xx.o(.text)
 <LI><a href="#[19]">TAMP_STAMP_IRQHandler</a> from startup_stm32l496xx.o(.text) referenced from startup_stm32l496xx.o(RESET)
 <LI><a href="#[2f]">TIM1_BRK_TIM15_IRQHandler</a> from startup_stm32l496xx.o(.text) referenced from startup_stm32l496xx.o(RESET)
 <LI><a href="#[32]">TIM1_CC_IRQHandler</a> from startup_stm32l496xx.o(.text) referenced from startup_stm32l496xx.o(RESET)
 <LI><a href="#[31]">TIM1_TRG_COM_TIM17_IRQHandler</a> from startup_stm32l496xx.o(.text) referenced from startup_stm32l496xx.o(RESET)
 <LI><a href="#[30]">TIM1_UP_TIM16_IRQHandler</a> from stm32l4xx_it.o(i.TIM1_UP_TIM16_IRQHandler) referenced from startup_stm32l496xx.o(RESET)
 <LI><a href="#[33]">TIM2_IRQHandler</a> from startup_stm32l496xx.o(.text) referenced from startup_stm32l496xx.o(RESET)
 <LI><a href="#[34]">TIM3_IRQHandler</a> from startup_stm32l496xx.o(.text) referenced from startup_stm32l496xx.o(RESET)
 <LI><a href="#[35]">TIM4_IRQHandler</a> from startup_stm32l496xx.o(.text) referenced from startup_stm32l496xx.o(RESET)
 <LI><a href="#[49]">TIM5_IRQHandler</a> from startup_stm32l496xx.o(.text) referenced from startup_stm32l496xx.o(RESET)
 <LI><a href="#[4d]">TIM6_DAC_IRQHandler</a> from stm32l4xx_it.o(i.TIM6_DAC_IRQHandler) referenced from startup_stm32l496xx.o(RESET)
 <LI><a href="#[4e]">TIM7_IRQHandler</a> from startup_stm32l496xx.o(.text) referenced from startup_stm32l496xx.o(RESET)
 <LI><a href="#[42]">TIM8_BRK_IRQHandler</a> from startup_stm32l496xx.o(.text) referenced from startup_stm32l496xx.o(RESET)
 <LI><a href="#[45]">TIM8_CC_IRQHandler</a> from startup_stm32l496xx.o(.text) referenced from startup_stm32l496xx.o(RESET)
 <LI><a href="#[44]">TIM8_TRG_COM_IRQHandler</a> from startup_stm32l496xx.o(.text) referenced from startup_stm32l496xx.o(RESET)
 <LI><a href="#[43]">TIM8_UP_IRQHandler</a> from startup_stm32l496xx.o(.text) referenced from startup_stm32l496xx.o(RESET)
 <LI><a href="#[64]">TSC_IRQHandler</a> from startup_stm32l496xx.o(.text) referenced from startup_stm32l496xx.o(RESET)
 <LI><a href="#[4b]">UART4_IRQHandler</a> from startup_stm32l496xx.o(.text) referenced from startup_stm32l496xx.o(RESET)
 <LI><a href="#[4c]">UART5_IRQHandler</a> from startup_stm32l496xx.o(.text) referenced from startup_stm32l496xx.o(RESET)
 <LI><a href="#[73]">UART_DMAAbortOnError</a> from stm32l4xx_hal_uart.o(i.UART_DMAAbortOnError) referenced from stm32l4xx_hal_uart.o(i.HAL_UART_IRQHandler)
 <LI><a href="#[3c]">USART1_IRQHandler</a> from stm32l4xx_it.o(i.USART1_IRQHandler) referenced from startup_stm32l496xx.o(RESET)
 <LI><a href="#[3d]">USART2_IRQHandler</a> from startup_stm32l496xx.o(.text) referenced from startup_stm32l496xx.o(RESET)
 <LI><a href="#[3e]">USART3_IRQHandler</a> from startup_stm32l496xx.o(.text) referenced from startup_stm32l496xx.o(RESET)
 <LI><a href="#[12]">UsageFault_Handler</a> from stm32l4xx_it.o(i.UsageFault_Handler) referenced from startup_stm32l496xx.o(RESET)
 <LI><a href="#[17]">WWDG_IRQHandler</a> from startup_stm32l496xx.o(.text) referenced from startup_stm32l496xx.o(RESET)
 <LI><a href="#[83]">__main</a> from __main.o(!!!main) referenced from startup_stm32l496xx.o(.text)
 <LI><a href="#[9]">eMBFuncReadCoils</a> from mbfunccoils.o(i.eMBFuncReadCoils) referenced 2 times from mb.o(.data)
 <LI><a href="#[c]">eMBFuncReadDiscreteInputs</a> from mbfuncdisc.o(i.eMBFuncReadDiscreteInputs) referenced 2 times from mb.o(.data)
 <LI><a href="#[5]">eMBFuncReadHoldingRegister</a> from mbfuncholding.o(i.eMBFuncReadHoldingRegister) referenced 2 times from mb.o(.data)
 <LI><a href="#[4]">eMBFuncReadInputRegister</a> from mbfuncinput.o(i.eMBFuncReadInputRegister) referenced 2 times from mb.o(.data)
 <LI><a href="#[8]">eMBFuncReadWriteMultipleHoldingRegister</a> from mbfuncholding.o(i.eMBFuncReadWriteMultipleHoldingRegister) referenced 2 times from mb.o(.data)
 <LI><a href="#[3]">eMBFuncReportSlaveID</a> from mbfuncother.o(i.eMBFuncReportSlaveID) referenced 2 times from mb.o(.data)
 <LI><a href="#[a]">eMBFuncWriteCoil</a> from mbfunccoils.o(i.eMBFuncWriteCoil) referenced 2 times from mb.o(.data)
 <LI><a href="#[7]">eMBFuncWriteHoldingRegister</a> from mbfuncholding.o(i.eMBFuncWriteHoldingRegister) referenced 2 times from mb.o(.data)
 <LI><a href="#[b]">eMBFuncWriteMultipleCoils</a> from mbfunccoils.o(i.eMBFuncWriteMultipleCoils) referenced 2 times from mb.o(.data)
 <LI><a href="#[6]">eMBFuncWriteMultipleHoldingRegister</a> from mbfuncholding.o(i.eMBFuncWriteMultipleHoldingRegister) referenced 2 times from mb.o(.data)
 <LI><a href="#[7c]">eMBRTUReceive</a> from mbrtu.o(i.eMBRTUReceive) referenced from mb.o(i.eMBInit)
 <LI><a href="#[7b]">eMBRTUSend</a> from mbrtu.o(i.eMBRTUSend) referenced from mb.o(i.eMBInit)
 <LI><a href="#[79]">eMBRTUStart</a> from mbrtu.o(i.eMBRTUStart) referenced from mb.o(i.eMBInit)
 <LI><a href="#[7a]">eMBRTUStop</a> from mbrtu.o(i.eMBRTUStop) referenced from mb.o(i.eMBInit)
 <LI><a href="#[81]">prvIdleTask</a> from tasks.o(i.prvIdleTask) referenced from tasks.o(i.vTaskStartScheduler)
 <LI><a href="#[80]">prvTaskExitError</a> from port.o(i.prvTaskExitError) referenced from port.o(i.pxPortInitialiseStack)
 <LI><a href="#[82]">prvTimerTask</a> from timers.o(i.prvTimerTask) referenced from timers.o(i.xTimerCreateTimerTask)
 <LI><a href="#[7d]">xMBRTUReceiveFSM</a> from mbrtu.o(i.xMBRTUReceiveFSM) referenced from mb.o(i.eMBInit)
 <LI><a href="#[7f]">xMBRTUTimerT35Expired</a> from mbrtu.o(i.xMBRTUTimerT35Expired) referenced from mb.o(i.eMBInit)
 <LI><a href="#[7e]">xMBRTUTransmitFSM</a> from mbrtu.o(i.xMBRTUTransmitFSM) referenced from mb.o(i.eMBInit)
</UL>
<P>
<H3>
Global Symbols
</H3>
<P><STRONG><a name="[83]"></a>__main</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, __main.o(!!!main))
<BR><BR>[Calls]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[84]"></a>__scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __scatter.o(!!!scatter))
<BR><BR>[Called By]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main
</UL>

<P><STRONG><a name="[86]"></a>__scatterload_rt2</STRONG> (Thumb, 44 bytes, Stack size unknown bytes, __scatter.o(!!!scatter), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry
</UL>

<P><STRONG><a name="[170]"></a>__scatterload_rt2_thumb_only</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __scatter.o(!!!scatter), UNUSED)

<P><STRONG><a name="[171]"></a>__scatterload_null</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __scatter.o(!!!scatter), UNUSED)

<P><STRONG><a name="[172]"></a>__decompress</STRONG> (Thumb, 90 bytes, Stack size unknown bytes, __dczerorl2.o(!!dczerorl2), UNUSED)

<P><STRONG><a name="[173]"></a>__decompress1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __dczerorl2.o(!!dczerorl2), UNUSED)

<P><STRONG><a name="[174]"></a>__scatterload_zeroinit</STRONG> (Thumb, 28 bytes, Stack size unknown bytes, __scatter_zi.o(!!handler_zi), UNUSED)

<P><STRONG><a name="[8c]"></a>__rt_lib_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit.o(.ARM.Collect$$libinit$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_li
</UL>

<P><STRONG><a name="[87]"></a>__rt_lib_init_fp_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000001))
<BR><BR>[Calls]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_init
</UL>

<P><STRONG><a name="[175]"></a>__rt_lib_init_alloca_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000002E))

<P><STRONG><a name="[176]"></a>__rt_lib_init_argv_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000002C))

<P><STRONG><a name="[177]"></a>__rt_lib_init_atexit_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000001B))

<P><STRONG><a name="[178]"></a>__rt_lib_init_clock_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000021))

<P><STRONG><a name="[179]"></a>__rt_lib_init_cpp_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000032))

<P><STRONG><a name="[17a]"></a>__rt_lib_init_exceptions_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000030))

<P><STRONG><a name="[17b]"></a>__rt_lib_init_fp_trap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000001F))

<P><STRONG><a name="[17c]"></a>__rt_lib_init_getenv_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000023))

<P><STRONG><a name="[17d]"></a>__rt_lib_init_heap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000000A))

<P><STRONG><a name="[17e]"></a>__rt_lib_init_lc_collate_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000011))

<P><STRONG><a name="[17f]"></a>__rt_lib_init_lc_ctype_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000013))

<P><STRONG><a name="[180]"></a>__rt_lib_init_lc_monetary_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000015))

<P><STRONG><a name="[181]"></a>__rt_lib_init_lc_numeric_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000017))

<P><STRONG><a name="[182]"></a>__rt_lib_init_lc_time_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000019))

<P><STRONG><a name="[183]"></a>__rt_lib_init_preinit_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000004))

<P><STRONG><a name="[184]"></a>__rt_lib_init_rand_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000000E))

<P><STRONG><a name="[185]"></a>__rt_lib_init_return</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000033))

<P><STRONG><a name="[186]"></a>__rt_lib_init_signal_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000001D))

<P><STRONG><a name="[187]"></a>__rt_lib_init_stdio_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000025))

<P><STRONG><a name="[188]"></a>__rt_lib_init_user_alloc_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000000C))

<P><STRONG><a name="[91]"></a>__rt_lib_shutdown</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown.o(.ARM.Collect$$libshutdown$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_exit_ls
</UL>

<P><STRONG><a name="[189]"></a>__rt_lib_shutdown_cpp_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000002))

<P><STRONG><a name="[18a]"></a>__rt_lib_shutdown_fp_trap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000007))

<P><STRONG><a name="[18b]"></a>__rt_lib_shutdown_heap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F))

<P><STRONG><a name="[18c]"></a>__rt_lib_shutdown_return</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000010))

<P><STRONG><a name="[18d]"></a>__rt_lib_shutdown_signal_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A))

<P><STRONG><a name="[18e]"></a>__rt_lib_shutdown_stdio_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000004))

<P><STRONG><a name="[18f]"></a>__rt_lib_shutdown_user_alloc_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C))

<P><STRONG><a name="[85]"></a>__rt_entry</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry.o(.ARM.Collect$$rtentry$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload_rt2
</UL>

<P><STRONG><a name="[190]"></a>__rt_entry_presh_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$00000002))

<P><STRONG><a name="[89]"></a>__rt_entry_sh</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry4.o(.ARM.Collect$$rtentry$$00000004))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = __rt_entry_sh &rArr; __user_setup_stackheap
</UL>
<BR>[Calls]<UL><LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_setup_stackheap
</UL>

<P><STRONG><a name="[8b]"></a>__rt_entry_li</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$0000000A))
<BR><BR>[Calls]<UL><LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_init
</UL>

<P><STRONG><a name="[191]"></a>__rt_entry_postsh_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$00000009))

<P><STRONG><a name="[8d]"></a>__rt_entry_main</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$0000000D))
<BR><BR>[Stack]<UL><LI>Max Depth = 280 + Unknown Stack Size
<LI>Call Chain = __rt_entry_main &rArr; main &rArr; MX_I2C2_Init &rArr; HAL_I2C_Init &rArr; HAL_I2C_MspInit &rArr; HAL_RCCEx_PeriphCLKConfig &rArr; RCCEx_PLLSAI2_Config
</UL>
<BR>[Calls]<UL><LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exit
</UL>

<P><STRONG><a name="[192]"></a>__rt_entry_postli_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$0000000C))

<P><STRONG><a name="[9d]"></a>__rt_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit.o(.ARM.Collect$$rtexit$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exit
</UL>

<P><STRONG><a name="[90]"></a>__rt_exit_ls</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit2.o(.ARM.Collect$$rtexit$$00000003))
<BR><BR>[Calls]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_shutdown
</UL>

<P><STRONG><a name="[193]"></a>__rt_exit_prels_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit2.o(.ARM.Collect$$rtexit$$00000002))

<P><STRONG><a name="[92]"></a>__rt_exit_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit2.o(.ARM.Collect$$rtexit$$00000004))
<BR><BR>[Calls]<UL><LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_sys_exit
</UL>

<P><STRONG><a name="[13]"></a>SVC_Handler</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, port.o(.emb_text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l496xx.o(RESET)
</UL>
<P><STRONG><a name="[168]"></a>__asm___6_port_c_39a90d8d__prvStartFirstTask</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, port.o(.emb_text))
<BR><BR>[Called By]<UL><LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xPortStartScheduler
</UL>

<P><STRONG><a name="[167]"></a>__asm___6_port_c_39a90d8d__prvEnableVFP</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, port.o(.emb_text))
<BR><BR>[Called By]<UL><LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xPortStartScheduler
</UL>

<P><STRONG><a name="[15]"></a>PendSV_Handler</STRONG> (Thumb, 88 bytes, Stack size 0 bytes, port.o(.emb_text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = PendSV_Handler &rArr; vTaskSwitchContext
</UL>
<BR>[Calls]<UL><LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskSwitchContext
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l496xx.o(RESET)
</UL>
<P><STRONG><a name="[15e]"></a>vPortGetIPSR</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, port.o(.emb_text))
<BR><BR>[Called By]<UL><LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortValidateInterruptPriority
</UL>

<P><STRONG><a name="[d]"></a>Reset_Handler</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, startup_stm32l496xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l496xx.o(RESET)
</UL>
<P><STRONG><a name="[29]"></a>ADC1_2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l496xx.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[29]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC1_2_IRQHandler
</UL>
<BR>[Called By]<UL><LI><a href="#[29]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC1_2_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l496xx.o(RESET)
</UL>
<P><STRONG><a name="[46]"></a>ADC3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l496xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l496xx.o(RESET)
</UL>
<P><STRONG><a name="[2c]"></a>CAN1_RX1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l496xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l496xx.o(RESET)
</UL>
<P><STRONG><a name="[2d]"></a>CAN1_SCE_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l496xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l496xx.o(RESET)
</UL>
<P><STRONG><a name="[2a]"></a>CAN1_TX_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l496xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l496xx.o(RESET)
</UL>
<P><STRONG><a name="[6d]"></a>CAN2_RX0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l496xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l496xx.o(RESET)
</UL>
<P><STRONG><a name="[6e]"></a>CAN2_RX1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l496xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l496xx.o(RESET)
</UL>
<P><STRONG><a name="[6f]"></a>CAN2_SCE_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l496xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l496xx.o(RESET)
</UL>
<P><STRONG><a name="[6c]"></a>CAN2_TX_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l496xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l496xx.o(RESET)
</UL>
<P><STRONG><a name="[57]"></a>COMP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l496xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l496xx.o(RESET)
</UL>
<P><STRONG><a name="[68]"></a>CRS_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l496xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l496xx.o(RESET)
</UL>
<P><STRONG><a name="[6b]"></a>DCMI_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l496xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l496xx.o(RESET)
</UL>
<P><STRONG><a name="[54]"></a>DFSDM1_FLT0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l496xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l496xx.o(RESET)
</UL>
<P><STRONG><a name="[55]"></a>DFSDM1_FLT1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l496xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l496xx.o(RESET)
</UL>
<P><STRONG><a name="[56]"></a>DFSDM1_FLT2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l496xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l496xx.o(RESET)
</UL>
<P><STRONG><a name="[41]"></a>DFSDM1_FLT3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l496xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l496xx.o(RESET)
</UL>
<P><STRONG><a name="[22]"></a>DMA1_Channel1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l496xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l496xx.o(RESET)
</UL>
<P><STRONG><a name="[23]"></a>DMA1_Channel2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l496xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l496xx.o(RESET)
</UL>
<P><STRONG><a name="[24]"></a>DMA1_Channel3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l496xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l496xx.o(RESET)
</UL>
<P><STRONG><a name="[25]"></a>DMA1_Channel4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l496xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l496xx.o(RESET)
</UL>
<P><STRONG><a name="[26]"></a>DMA1_Channel5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l496xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l496xx.o(RESET)
</UL>
<P><STRONG><a name="[27]"></a>DMA1_Channel6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l496xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l496xx.o(RESET)
</UL>
<P><STRONG><a name="[28]"></a>DMA1_Channel7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l496xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l496xx.o(RESET)
</UL>
<P><STRONG><a name="[70]"></a>DMA2D_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l496xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l496xx.o(RESET)
</UL>
<P><STRONG><a name="[4f]"></a>DMA2_Channel1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l496xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l496xx.o(RESET)
</UL>
<P><STRONG><a name="[50]"></a>DMA2_Channel2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l496xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l496xx.o(RESET)
</UL>
<P><STRONG><a name="[51]"></a>DMA2_Channel3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l496xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l496xx.o(RESET)
</UL>
<P><STRONG><a name="[52]"></a>DMA2_Channel4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l496xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l496xx.o(RESET)
</UL>
<P><STRONG><a name="[53]"></a>DMA2_Channel5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l496xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l496xx.o(RESET)
</UL>
<P><STRONG><a name="[5b]"></a>DMA2_Channel6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l496xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l496xx.o(RESET)
</UL>
<P><STRONG><a name="[5c]"></a>DMA2_Channel7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l496xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l496xx.o(RESET)
</UL>
<P><STRONG><a name="[1d]"></a>EXTI0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l496xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l496xx.o(RESET)
</UL>
<P><STRONG><a name="[3f]"></a>EXTI15_10_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l496xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l496xx.o(RESET)
</UL>
<P><STRONG><a name="[20]"></a>EXTI3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l496xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l496xx.o(RESET)
</UL>
<P><STRONG><a name="[21]"></a>EXTI4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l496xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l496xx.o(RESET)
</UL>
<P><STRONG><a name="[2e]"></a>EXTI9_5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l496xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l496xx.o(RESET)
</UL>
<P><STRONG><a name="[1b]"></a>FLASH_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l496xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l496xx.o(RESET)
</UL>
<P><STRONG><a name="[47]"></a>FMC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l496xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l496xx.o(RESET)
</UL>
<P><STRONG><a name="[67]"></a>FPU_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l496xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l496xx.o(RESET)
</UL>
<P><STRONG><a name="[37]"></a>I2C1_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l496xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l496xx.o(RESET)
</UL>
<P><STRONG><a name="[36]"></a>I2C1_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l496xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l496xx.o(RESET)
</UL>
<P><STRONG><a name="[39]"></a>I2C2_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l496xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l496xx.o(RESET)
</UL>
<P><STRONG><a name="[38]"></a>I2C2_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l496xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l496xx.o(RESET)
</UL>
<P><STRONG><a name="[60]"></a>I2C3_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l496xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l496xx.o(RESET)
</UL>
<P><STRONG><a name="[5f]"></a>I2C3_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l496xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l496xx.o(RESET)
</UL>
<P><STRONG><a name="[6a]"></a>I2C4_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l496xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l496xx.o(RESET)
</UL>
<P><STRONG><a name="[69]"></a>I2C4_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l496xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l496xx.o(RESET)
</UL>
<P><STRONG><a name="[65]"></a>LCD_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l496xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l496xx.o(RESET)
</UL>
<P><STRONG><a name="[58]"></a>LPTIM1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l496xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l496xx.o(RESET)
</UL>
<P><STRONG><a name="[59]"></a>LPTIM2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l496xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l496xx.o(RESET)
</UL>
<P><STRONG><a name="[5d]"></a>LPUART1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l496xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l496xx.o(RESET)
</UL>
<P><STRONG><a name="[5a]"></a>OTG_FS_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l496xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l496xx.o(RESET)
</UL>
<P><STRONG><a name="[18]"></a>PVD_PVM_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l496xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l496xx.o(RESET)
</UL>
<P><STRONG><a name="[5e]"></a>QUADSPI_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l496xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l496xx.o(RESET)
</UL>
<P><STRONG><a name="[1c]"></a>RCC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l496xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l496xx.o(RESET)
</UL>
<P><STRONG><a name="[66]"></a>RNG_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l496xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l496xx.o(RESET)
</UL>
<P><STRONG><a name="[40]"></a>RTC_Alarm_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l496xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l496xx.o(RESET)
</UL>
<P><STRONG><a name="[1a]"></a>RTC_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l496xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l496xx.o(RESET)
</UL>
<P><STRONG><a name="[61]"></a>SAI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l496xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l496xx.o(RESET)
</UL>
<P><STRONG><a name="[62]"></a>SAI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l496xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l496xx.o(RESET)
</UL>
<P><STRONG><a name="[48]"></a>SDMMC1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l496xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l496xx.o(RESET)
</UL>
<P><STRONG><a name="[3a]"></a>SPI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l496xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l496xx.o(RESET)
</UL>
<P><STRONG><a name="[3b]"></a>SPI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l496xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l496xx.o(RESET)
</UL>
<P><STRONG><a name="[4a]"></a>SPI3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l496xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l496xx.o(RESET)
</UL>
<P><STRONG><a name="[63]"></a>SWPMI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l496xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l496xx.o(RESET)
</UL>
<P><STRONG><a name="[19]"></a>TAMP_STAMP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l496xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l496xx.o(RESET)
</UL>
<P><STRONG><a name="[2f]"></a>TIM1_BRK_TIM15_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l496xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l496xx.o(RESET)
</UL>
<P><STRONG><a name="[32]"></a>TIM1_CC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l496xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l496xx.o(RESET)
</UL>
<P><STRONG><a name="[31]"></a>TIM1_TRG_COM_TIM17_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l496xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l496xx.o(RESET)
</UL>
<P><STRONG><a name="[33]"></a>TIM2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l496xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l496xx.o(RESET)
</UL>
<P><STRONG><a name="[34]"></a>TIM3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l496xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l496xx.o(RESET)
</UL>
<P><STRONG><a name="[35]"></a>TIM4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l496xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l496xx.o(RESET)
</UL>
<P><STRONG><a name="[49]"></a>TIM5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l496xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l496xx.o(RESET)
</UL>
<P><STRONG><a name="[4e]"></a>TIM7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l496xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l496xx.o(RESET)
</UL>
<P><STRONG><a name="[42]"></a>TIM8_BRK_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l496xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l496xx.o(RESET)
</UL>
<P><STRONG><a name="[45]"></a>TIM8_CC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l496xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l496xx.o(RESET)
</UL>
<P><STRONG><a name="[44]"></a>TIM8_TRG_COM_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l496xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l496xx.o(RESET)
</UL>
<P><STRONG><a name="[43]"></a>TIM8_UP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l496xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l496xx.o(RESET)
</UL>
<P><STRONG><a name="[64]"></a>TSC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l496xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l496xx.o(RESET)
</UL>
<P><STRONG><a name="[4b]"></a>UART4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l496xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l496xx.o(RESET)
</UL>
<P><STRONG><a name="[4c]"></a>UART5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l496xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l496xx.o(RESET)
</UL>
<P><STRONG><a name="[3d]"></a>USART2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l496xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l496xx.o(RESET)
</UL>
<P><STRONG><a name="[3e]"></a>USART3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l496xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l496xx.o(RESET)
</UL>
<P><STRONG><a name="[17]"></a>WWDG_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l496xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l496xx.o(RESET)
</UL>
<P><STRONG><a name="[9c]"></a>__user_initial_stackheap</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, startup_stm32l496xx.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_setup_stackheap
</UL>

<P><STRONG><a name="[110]"></a>__aeabi_uldivmod</STRONG> (Thumb, 0 bytes, Stack size 48 bytes, lludivv7m.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = __aeabi_uldivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_SetConfig
</UL>

<P><STRONG><a name="[194]"></a>_ll_udiv</STRONG> (Thumb, 238 bytes, Stack size 48 bytes, lludivv7m.o(.text), UNUSED)

<P><STRONG><a name="[119]"></a>__aeabi_memcpy</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, rt_memcpy_v6.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvCopyDataToQueue
<LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvCopyDataFromQueue
<LI><a href="#[3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;eMBFuncReportSlaveID
</UL>

<P><STRONG><a name="[95]"></a>__rt_memcpy</STRONG> (Thumb, 138 bytes, Stack size 0 bytes, rt_memcpy_v6.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>

<P><STRONG><a name="[195]"></a>_memcpy_lastbytes</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rt_memcpy_v6.o(.text), UNUSED)

<P><STRONG><a name="[97]"></a>__aeabi_memset</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, aeabi_memset.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_memset
</UL>
<BR>[Called By]<UL><LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvInitialiseNewTask
</UL>

<P><STRONG><a name="[196]"></a>__aeabi_memclr</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, rt_memclr.o(.text), UNUSED)

<P><STRONG><a name="[99]"></a>__rt_memclr</STRONG> (Thumb, 68 bytes, Stack size 0 bytes, rt_memclr.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_memset_w
</UL>

<P><STRONG><a name="[98]"></a>_memset</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rt_memclr.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
</UL>

<P><STRONG><a name="[b3]"></a>__aeabi_memclr4</STRONG> (Thumb, 0 bytes, Stack size 4 bytes, rt_memclr_w.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = __aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_MspInit
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_MspInit
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_CAN_MspInit
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RS485_Init
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_GPIO_Init
<LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>

<P><STRONG><a name="[197]"></a>__aeabi_memclr8</STRONG> (Thumb, 0 bytes, Stack size 4 bytes, rt_memclr_w.o(.text), UNUSED)

<P><STRONG><a name="[198]"></a>__rt_memclr_w</STRONG> (Thumb, 78 bytes, Stack size 4 bytes, rt_memclr_w.o(.text), UNUSED)

<P><STRONG><a name="[9a]"></a>_memset_w</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rt_memclr_w.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_memclr
</UL>

<P><STRONG><a name="[199]"></a>__use_two_region_memory</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, heapauxi.o(.text), UNUSED)

<P><STRONG><a name="[19a]"></a>__rt_heap_escrow$2region</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, heapauxi.o(.text), UNUSED)

<P><STRONG><a name="[19b]"></a>__rt_heap_expand$2region</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, heapauxi.o(.text), UNUSED)

<P><STRONG><a name="[96]"></a>__aeabi_memcpy4</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, rt_memcpy_w.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_memcpy
</UL>

<P><STRONG><a name="[19c]"></a>__aeabi_memcpy8</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, rt_memcpy_w.o(.text), UNUSED)

<P><STRONG><a name="[19d]"></a>__rt_memcpy_w</STRONG> (Thumb, 100 bytes, Stack size 8 bytes, rt_memcpy_w.o(.text), UNUSED)

<P><STRONG><a name="[19e]"></a>_memcpy_lastbytes_aligned</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rt_memcpy_w.o(.text), UNUSED)

<P><STRONG><a name="[8a]"></a>__user_setup_stackheap</STRONG> (Thumb, 74 bytes, Stack size 8 bytes, sys_stackheap_outer.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = __user_setup_stackheap
</UL>
<BR>[Calls]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_initial_stackheap
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_perproc_libspace
</UL>
<BR>[Called By]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_sh
</UL>

<P><STRONG><a name="[8f]"></a>exit</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, exit.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = exit
</UL>
<BR>[Calls]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_exit
</UL>
<BR>[Called By]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_main
</UL>

<P><STRONG><a name="[19f]"></a>__user_libspace</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, libspace.o(.text), UNUSED)

<P><STRONG><a name="[9b]"></a>__user_perproc_libspace</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, libspace.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_setup_stackheap
</UL>

<P><STRONG><a name="[1a0]"></a>__user_perthread_libspace</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, libspace.o(.text), UNUSED)

<P><STRONG><a name="[93]"></a>_sys_exit</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, sys_exit.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_exit_exit
</UL>

<P><STRONG><a name="[1a1]"></a>__I$use$semihosting</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, use_no_semi.o(.text), UNUSED)

<P><STRONG><a name="[1a2]"></a>__use_no_semihosting_swi</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, use_no_semi.o(.text), UNUSED)

<P><STRONG><a name="[11]"></a>BusFault_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32l4xx_it.o(i.BusFault_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[11]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BusFault_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[11]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BusFault_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l496xx.o(RESET)
</UL>
<P><STRONG><a name="[1a3]"></a>__semihosting_library_function</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, indicate_semi.o(.text), UNUSED)

<P><STRONG><a name="[2b]"></a>CAN1_RX0_IRQHandler</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32l4xx_it.o(i.CAN1_RX0_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = CAN1_RX0_IRQHandler &rArr; HAL_CAN_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_CAN_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l496xx.o(RESET)
</UL>
<P><STRONG><a name="[76]"></a>CANComTask</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, freertos.o(i.CANComTask))
<BR><BR>[Stack]<UL><LI>Max Depth = 64 + In Cycle
<LI>Call Chain = CANComTask &rArr;  CANComTask (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;osDelay
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CANComTask
</UL>
<BR>[Called By]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CANComTask
</UL>
<BR>[Address Reference Count : 1]<UL><LI> freertos.o(i.MX_FREERTOS_Init)
</UL>
<P><STRONG><a name="[14]"></a>DebugMon_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32l4xx_it.o(i.DebugMon_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l496xx.o(RESET)
</UL>
<P><STRONG><a name="[1e]"></a>EXTI1_IRQHandler</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32l4xx_it.o(i.EXTI1_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = EXTI1_IRQHandler &rArr; HAL_GPIO_EXTI_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_EXTI_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l496xx.o(RESET)
</UL>
<P><STRONG><a name="[1f]"></a>EXTI2_IRQHandler</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32l4xx_it.o(i.EXTI2_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = EXTI2_IRQHandler &rArr; HAL_GPIO_EXTI_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_EXTI_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l496xx.o(RESET)
</UL>
<P><STRONG><a name="[74]"></a>EnergyMeterTask</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, freertos.o(i.EnergyMeterTask))
<BR><BR>[Stack]<UL><LI>Max Depth = 208 + In Cycle
<LI>Call Chain = EnergyMeterTask &rArr;  EnergyMeterTask (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;test_read_voltage
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;test_read_LostVoltage
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;osDelay
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EnergyMeterTask
</UL>
<BR>[Called By]<UL><LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EnergyMeterTask
</UL>
<BR>[Address Reference Count : 1]<UL><LI> freertos.o(i.MX_FREERTOS_Init)
</UL>
<P><STRONG><a name="[bc]"></a>Error_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, main.o(i.Error_Handler))
<BR><BR>[Called By]<UL><LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_MspInit
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART1_UART_Init
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM16_Init
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_SPI2_Init
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_I2C2_Init
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_I2C1_Init
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_CAN1_Init
<LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>

<P><STRONG><a name="[af]"></a>HAL_CAN_ErrorCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32l4xx_hal_can.o(i.HAL_CAN_ErrorCallback))
<BR><BR>[Called By]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_CAN_IRQHandler
</UL>

<P><STRONG><a name="[9e]"></a>HAL_CAN_IRQHandler</STRONG> (Thumb, 508 bytes, Stack size 40 bytes, stm32l4xx_hal_can.o(i.HAL_CAN_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = HAL_CAN_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_CAN_WakeUpFromRxMsgCallback
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_CAN_TxMailbox2CompleteCallback
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_CAN_TxMailbox2AbortCallback
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_CAN_TxMailbox1CompleteCallback
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_CAN_TxMailbox1AbortCallback
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_CAN_TxMailbox0CompleteCallback
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_CAN_TxMailbox0AbortCallback
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_CAN_SleepCallback
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_CAN_RxFifo1MsgPendingCallback
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_CAN_RxFifo1FullCallback
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_CAN_RxFifo0MsgPendingCallback
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_CAN_RxFifo0FullCallback
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_CAN_ErrorCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[2b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CAN1_RX0_IRQHandler
</UL>

<P><STRONG><a name="[b0]"></a>HAL_CAN_Init</STRONG> (Thumb, 338 bytes, Stack size 16 bytes, stm32l4xx_hal_can.o(i.HAL_CAN_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = HAL_CAN_Init &rArr; HAL_CAN_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_CAN_MspInit
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_CAN1_Init
</UL>

<P><STRONG><a name="[b1]"></a>HAL_CAN_MspInit</STRONG> (Thumb, 106 bytes, Stack size 32 bytes, can.o(i.HAL_CAN_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = HAL_CAN_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_EnableIRQ
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_CAN_Init
</UL>

<P><STRONG><a name="[a9]"></a>HAL_CAN_RxFifo0FullCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32l4xx_hal_can.o(i.HAL_CAN_RxFifo0FullCallback))
<BR><BR>[Called By]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_CAN_IRQHandler
</UL>

<P><STRONG><a name="[aa]"></a>HAL_CAN_RxFifo0MsgPendingCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32l4xx_hal_can.o(i.HAL_CAN_RxFifo0MsgPendingCallback))
<BR><BR>[Called By]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_CAN_IRQHandler
</UL>

<P><STRONG><a name="[ab]"></a>HAL_CAN_RxFifo1FullCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32l4xx_hal_can.o(i.HAL_CAN_RxFifo1FullCallback))
<BR><BR>[Called By]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_CAN_IRQHandler
</UL>

<P><STRONG><a name="[ac]"></a>HAL_CAN_RxFifo1MsgPendingCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32l4xx_hal_can.o(i.HAL_CAN_RxFifo1MsgPendingCallback))
<BR><BR>[Called By]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_CAN_IRQHandler
</UL>

<P><STRONG><a name="[ad]"></a>HAL_CAN_SleepCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32l4xx_hal_can.o(i.HAL_CAN_SleepCallback))
<BR><BR>[Called By]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_CAN_IRQHandler
</UL>

<P><STRONG><a name="[a4]"></a>HAL_CAN_TxMailbox0AbortCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32l4xx_hal_can.o(i.HAL_CAN_TxMailbox0AbortCallback))
<BR><BR>[Called By]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_CAN_IRQHandler
</UL>

<P><STRONG><a name="[a3]"></a>HAL_CAN_TxMailbox0CompleteCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32l4xx_hal_can.o(i.HAL_CAN_TxMailbox0CompleteCallback))
<BR><BR>[Called By]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_CAN_IRQHandler
</UL>

<P><STRONG><a name="[a6]"></a>HAL_CAN_TxMailbox1AbortCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32l4xx_hal_can.o(i.HAL_CAN_TxMailbox1AbortCallback))
<BR><BR>[Called By]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_CAN_IRQHandler
</UL>

<P><STRONG><a name="[a5]"></a>HAL_CAN_TxMailbox1CompleteCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32l4xx_hal_can.o(i.HAL_CAN_TxMailbox1CompleteCallback))
<BR><BR>[Called By]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_CAN_IRQHandler
</UL>

<P><STRONG><a name="[a8]"></a>HAL_CAN_TxMailbox2AbortCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32l4xx_hal_can.o(i.HAL_CAN_TxMailbox2AbortCallback))
<BR><BR>[Called By]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_CAN_IRQHandler
</UL>

<P><STRONG><a name="[a7]"></a>HAL_CAN_TxMailbox2CompleteCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32l4xx_hal_can.o(i.HAL_CAN_TxMailbox2CompleteCallback))
<BR><BR>[Called By]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_CAN_IRQHandler
</UL>

<P><STRONG><a name="[ae]"></a>HAL_CAN_WakeUpFromRxMsgCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32l4xx_hal_can.o(i.HAL_CAN_WakeUpFromRxMsgCallback))
<BR><BR>[Called By]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_CAN_IRQHandler
</UL>

<P><STRONG><a name="[e5]"></a>HAL_DMA_Abort</STRONG> (Thumb, 74 bytes, Stack size 8 bytes, stm32l4xx_hal_dma.o(i.HAL_DMA_Abort))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_DMA_Abort
</UL>
<BR>[Called By]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
</UL>

<P><STRONG><a name="[e3]"></a>HAL_DMA_Abort_IT</STRONG> (Thumb, 78 bytes, Stack size 8 bytes, stm32l4xx_hal_dma.o(i.HAL_DMA_Abort_IT))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_DMA_Abort_IT
</UL>
<BR>[Called By]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
</UL>

<P><STRONG><a name="[b7]"></a>HAL_Delay</STRONG> (Thumb, 32 bytes, Stack size 16 bytes, stm32l4xx_hal.o(i.HAL_Delay))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = HAL_Delay
</UL>
<BR>[Calls]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysMonitorTask
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ModbusComTask
</UL>

<P><STRONG><a name="[b8]"></a>HAL_GPIO_EXTI_Callback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32l4xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback))
<BR><BR>[Called By]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_EXTI_IRQHandler
</UL>

<P><STRONG><a name="[a0]"></a>HAL_GPIO_EXTI_IRQHandler</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, stm32l4xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_GPIO_EXTI_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_EXTI_Callback
</UL>
<BR>[Called By]<UL><LI><a href="#[1f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI2_IRQHandler
<LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI1_IRQHandler
</UL>

<P><STRONG><a name="[b4]"></a>HAL_GPIO_Init</STRONG> (Thumb, 420 bytes, Stack size 40 bytes, stm32l4xx_hal_gpio.o(i.HAL_GPIO_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = HAL_GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_MspInit
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_MspInit
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_CAN_MspInit
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RS485_Init
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_GPIO_Init
</UL>

<P><STRONG><a name="[ff]"></a>HAL_GPIO_TogglePin</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, stm32l4xx_hal_gpio.o(i.HAL_GPIO_TogglePin))
<BR><BR>[Called By]<UL><LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Modbus_Test_LED_Indicate
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ModbusComTask
<LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvvTIMERExpiredISR
</UL>

<P><STRONG><a name="[f4]"></a>HAL_GPIO_WritePin</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, stm32l4xx_hal_gpio.o(i.HAL_GPIO_WritePin))
<BR><BR>[Called By]<UL><LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RS485_Init
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RS485_Enter_TX_Mode
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RS485_Enter_RX_Mode
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Modbus_Test_LED_Indicate
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ModbusComTask
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_GPIO_Init
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RN7326_ReadReg
</UL>

<P><STRONG><a name="[b2]"></a>HAL_GetTick</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32l4xx_hal.o(i.HAL_GetTick))
<BR><BR>[Called By]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCCEx_PeriphCLKConfig
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_CAN_Init
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_OscConfig
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_WaitOnFlagUntilTimeout
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_CheckIdleState
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCCEx_PLLSAI2_Config
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCCEx_PLLSAI1_Config
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_TransmitReceive
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_Transmit
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_Receive
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_WaitFlagStateUntilTimeout
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_WaitFifoStateUntilTimeout
</UL>

<P><STRONG><a name="[f6]"></a>HAL_I2CEx_ConfigAnalogFilter</STRONG> (Thumb, 90 bytes, Stack size 0 bytes, stm32l4xx_hal_i2c_ex.o(i.HAL_I2CEx_ConfigAnalogFilter))
<BR><BR>[Called By]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_I2C2_Init
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_I2C1_Init
</UL>

<P><STRONG><a name="[f7]"></a>HAL_I2CEx_ConfigDigitalFilter</STRONG> (Thumb, 86 bytes, Stack size 0 bytes, stm32l4xx_hal_i2c_ex.o(i.HAL_I2CEx_ConfigDigitalFilter))
<BR><BR>[Called By]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_I2C2_Init
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_I2C1_Init
</UL>

<P><STRONG><a name="[b9]"></a>HAL_I2C_Init</STRONG> (Thumb, 186 bytes, Stack size 16 bytes, stm32l4xx_hal_i2c.o(i.HAL_I2C_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 272<LI>Call Chain = HAL_I2C_Init &rArr; HAL_I2C_MspInit &rArr; HAL_RCCEx_PeriphCLKConfig &rArr; RCCEx_PLLSAI2_Config
</UL>
<BR>[Calls]<UL><LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_MspInit
</UL>
<BR>[Called By]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_I2C2_Init
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_I2C1_Init
</UL>

<P><STRONG><a name="[ba]"></a>HAL_I2C_MspInit</STRONG> (Thumb, 208 bytes, Stack size 192 bytes, i2c.o(i.HAL_I2C_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 256<LI>Call Chain = HAL_I2C_MspInit &rArr; HAL_RCCEx_PeriphCLKConfig &rArr; RCCEx_PLLSAI2_Config
</UL>
<BR>[Calls]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCCEx_PeriphCLKConfig
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Init
</UL>

<P><STRONG><a name="[e0]"></a>HAL_IncTick</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, stm32l4xx_hal.o(i.HAL_IncTick))
<BR><BR>[Called By]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_PeriodElapsedCallback
</UL>

<P><STRONG><a name="[bd]"></a>HAL_Init</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, stm32l4xx_hal.o(i.HAL_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 84<LI>Call Chain = HAL_Init &rArr; HAL_InitTick &rArr; HAL_TIM_Base_Init &rArr; TIM_Base_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriorityGrouping
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_MspInit
</UL>
<BR>[Called By]<UL><LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[bf]"></a>HAL_InitTick</STRONG> (Thumb, 98 bytes, Stack size 32 bytes, stm32l4xx_hal_timebase_tim.o(i.HAL_InitTick))
<BR><BR>[Stack]<UL><LI>Max Depth = 76<LI>Call Chain = HAL_InitTick &rArr; HAL_TIM_Base_Init &rArr; TIM_Base_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_Init
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_EnableIRQ
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_Start_IT
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetPCLK1Freq
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetClockConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_OscConfig
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
</UL>

<P><STRONG><a name="[c0]"></a>HAL_MspInit</STRONG> (Thumb, 48 bytes, Stack size 8 bytes, stm32l4xx_hal_msp.o(i.HAL_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = HAL_MspInit &rArr; HAL_NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
</UL>

<P><STRONG><a name="[113]"></a>HAL_NVIC_ClearPendingIRQ</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, stm32l4xx_hal_cortex.o(i.HAL_NVIC_ClearPendingIRQ))
<BR><BR>[Called By]<UL><LI><a href="#[3c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART1_IRQHandler
</UL>

<P><STRONG><a name="[b6]"></a>HAL_NVIC_EnableIRQ</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, stm32l4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ))
<BR><BR>[Called By]<UL><LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_MspInit
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_CAN_MspInit
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_GPIO_Init
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
</UL>

<P><STRONG><a name="[b5]"></a>HAL_NVIC_SetPriority</STRONG> (Thumb, 60 bytes, Stack size 16 bytes, stm32l4xx_hal_cortex.o(i.HAL_NVIC_SetPriority))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = HAL_NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_MspInit
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_CAN_MspInit
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_GPIO_Init
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_MspInit
</UL>

<P><STRONG><a name="[be]"></a>HAL_NVIC_SetPriorityGrouping</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, stm32l4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping))
<BR><BR>[Called By]<UL><LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
</UL>

<P><STRONG><a name="[10d]"></a>HAL_PWREx_ControlVoltageScaling</STRONG> (Thumb, 96 bytes, Stack size 0 bytes, stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling))
<BR><BR>[Called By]<UL><LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>

<P><STRONG><a name="[106]"></a>HAL_PWREx_GetVoltageRange</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_GetVoltageRange))
<BR><BR>[Called By]<UL><LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_SetFlashLatencyFromMSIRange
</UL>

<P><STRONG><a name="[bb]"></a>HAL_RCCEx_PeriphCLKConfig</STRONG> (Thumb, 962 bytes, Stack size 40 bytes, stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = HAL_RCCEx_PeriphCLKConfig &rArr; RCCEx_PLLSAI2_Config
</UL>
<BR>[Calls]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCCEx_PLLSAI2_Config
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCCEx_PLLSAI1_Config
</UL>
<BR>[Called By]<UL><LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_MspInit
</UL>

<P><STRONG><a name="[c8]"></a>HAL_RCC_ClockConfig</STRONG> (Thumb, 346 bytes, Stack size 32 bytes, stm32l4xx_hal_rcc.o(i.HAL_RCC_ClockConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 108<LI>Call Chain = HAL_RCC_ClockConfig &rArr; HAL_InitTick &rArr; HAL_TIM_Base_Init &rArr; TIM_Base_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetSysClockFreq
</UL>
<BR>[Called By]<UL><LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>

<P><STRONG><a name="[c1]"></a>HAL_RCC_GetClockConfig</STRONG> (Thumb, 52 bytes, Stack size 0 bytes, stm32l4xx_hal_rcc.o(i.HAL_RCC_GetClockConfig))
<BR><BR>[Called By]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
</UL>

<P><STRONG><a name="[ca]"></a>HAL_RCC_GetHCLKFreq</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32l4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq))
<BR><BR>[Called By]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetPCLK1Freq
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetPCLK2Freq
</UL>

<P><STRONG><a name="[c2]"></a>HAL_RCC_GetPCLK1Freq</STRONG> (Thumb, 26 bytes, Stack size 4 bytes, stm32l4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = HAL_RCC_GetPCLK1Freq
</UL>
<BR>[Calls]<UL><LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetHCLKFreq
</UL>
<BR>[Called By]<UL><LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_SetConfig
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
</UL>

<P><STRONG><a name="[cb]"></a>HAL_RCC_GetPCLK2Freq</STRONG> (Thumb, 26 bytes, Stack size 4 bytes, stm32l4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = HAL_RCC_GetPCLK2Freq
</UL>
<BR>[Calls]<UL><LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetHCLKFreq
</UL>
<BR>[Called By]<UL><LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_SetConfig
</UL>

<P><STRONG><a name="[c9]"></a>HAL_RCC_GetSysClockFreq</STRONG> (Thumb, 150 bytes, Stack size 16 bytes, stm32l4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = HAL_RCC_GetSysClockFreq
</UL>
<BR>[Called By]<UL><LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_OscConfig
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_SetConfig
</UL>

<P><STRONG><a name="[cc]"></a>HAL_RCC_OscConfig</STRONG> (Thumb, 1466 bytes, Stack size 32 bytes, stm32l4xx_hal_rcc.o(i.HAL_RCC_OscConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 108<LI>Call Chain = HAL_RCC_OscConfig &rArr; HAL_InitTick &rArr; HAL_TIM_Base_Init &rArr; TIM_Base_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetSysClockFreq
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_SetFlashLatencyFromMSIRange
</UL>
<BR>[Called By]<UL><LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>

<P><STRONG><a name="[ce]"></a>HAL_SPI_Init</STRONG> (Thumb, 226 bytes, Stack size 16 bytes, stm32l4xx_hal_spi.o(i.HAL_SPI_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = HAL_SPI_Init &rArr; HAL_SPI_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_MspInit
</UL>
<BR>[Called By]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_SPI2_Init
</UL>

<P><STRONG><a name="[cf]"></a>HAL_SPI_MspInit</STRONG> (Thumb, 90 bytes, Stack size 32 bytes, spi.o(i.HAL_SPI_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = HAL_SPI_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_Init
</UL>

<P><STRONG><a name="[d0]"></a>HAL_SPI_Receive</STRONG> (Thumb, 390 bytes, Stack size 40 bytes, stm32l4xx_hal_spi.o(i.HAL_SPI_Receive))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = HAL_SPI_Receive &rArr; HAL_SPI_TransmitReceive &rArr; SPI_EndRxTxTransaction &rArr; SPI_WaitFifoStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_TransmitReceive
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_EndRxTransaction
</UL>
<BR>[Called By]<UL><LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RN7326_ReadReg
</UL>

<P><STRONG><a name="[d3]"></a>HAL_SPI_Transmit</STRONG> (Thumb, 444 bytes, Stack size 40 bytes, stm32l4xx_hal_spi.o(i.HAL_SPI_Transmit))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = HAL_SPI_Transmit &rArr; SPI_EndRxTxTransaction &rArr; SPI_WaitFifoStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_EndRxTxTransaction
</UL>
<BR>[Called By]<UL><LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RN7326_ReadReg
</UL>

<P><STRONG><a name="[d1]"></a>HAL_SPI_TransmitReceive</STRONG> (Thumb, 620 bytes, Stack size 40 bytes, stm32l4xx_hal_spi.o(i.HAL_SPI_TransmitReceive))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = HAL_SPI_TransmitReceive &rArr; SPI_EndRxTxTransaction &rArr; SPI_WaitFifoStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_EndRxTxTransaction
</UL>
<BR>[Called By]<UL><LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_Receive
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RN7326_ReadReg
</UL>

<P><STRONG><a name="[dd]"></a>HAL_TIMEx_Break2Callback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_Break2Callback))
<BR><BR>[Called By]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>

<P><STRONG><a name="[dc]"></a>HAL_TIMEx_BreakCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback))
<BR><BR>[Called By]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>

<P><STRONG><a name="[df]"></a>HAL_TIMEx_CommutCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback))
<BR><BR>[Called By]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>

<P><STRONG><a name="[c3]"></a>HAL_TIM_Base_Init</STRONG> (Thumb, 78 bytes, Stack size 8 bytes, stm32l4xx_hal_tim.o(i.HAL_TIM_Base_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = HAL_TIM_Base_Init &rArr; TIM_Base_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_MspInit
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_Base_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM16_Init
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
</UL>

<P><STRONG><a name="[d5]"></a>HAL_TIM_Base_MspInit</STRONG> (Thumb, 50 bytes, Stack size 8 bytes, tim.o(i.HAL_TIM_Base_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = HAL_TIM_Base_MspInit &rArr; HAL_NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_EnableIRQ
</UL>
<BR>[Called By]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_Init
</UL>

<P><STRONG><a name="[c4]"></a>HAL_TIM_Base_Start_IT</STRONG> (Thumb, 110 bytes, Stack size 0 bytes, stm32l4xx_hal_tim.o(i.HAL_TIM_Base_Start_IT))
<BR><BR>[Called By]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
</UL>

<P><STRONG><a name="[d8]"></a>HAL_TIM_IC_CaptureCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32l4xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback))
<BR><BR>[Called By]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>

<P><STRONG><a name="[d7]"></a>HAL_TIM_IRQHandler</STRONG> (Thumb, 396 bytes, Stack size 16 bytes, stm32l4xx_hal_tim.o(i.HAL_TIM_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = HAL_TIM_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_PeriodElapsedCallback
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_TriggerCallback
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_PWM_PulseFinishedCallback
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_OC_DelayElapsedCallback
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IC_CaptureCallback
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIMEx_CommutCallback
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIMEx_BreakCallback
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIMEx_Break2Callback
</UL>
<BR>[Called By]<UL><LI><a href="#[4d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM6_DAC_IRQHandler
<LI><a href="#[30]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM1_UP_TIM16_IRQHandler
</UL>

<P><STRONG><a name="[d9]"></a>HAL_TIM_OC_DelayElapsedCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32l4xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback))
<BR><BR>[Called By]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>

<P><STRONG><a name="[da]"></a>HAL_TIM_PWM_PulseFinishedCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback))
<BR><BR>[Called By]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>

<P><STRONG><a name="[db]"></a>HAL_TIM_PeriodElapsedCallback</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, main.o(i.HAL_TIM_PeriodElapsedCallback))
<BR><BR>[Calls]<UL><LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_IncTick
</UL>
<BR>[Called By]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>

<P><STRONG><a name="[de]"></a>HAL_TIM_TriggerCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32l4xx_hal_tim.o(i.HAL_TIM_TriggerCallback))
<BR><BR>[Called By]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>

<P><STRONG><a name="[e6]"></a>HAL_UARTEx_RxEventCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32l4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback))
<BR><BR>[Called By]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
</UL>

<P><STRONG><a name="[e7]"></a>HAL_UARTEx_WakeupCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32l4xx_hal_uart_ex.o(i.HAL_UARTEx_WakeupCallback))
<BR><BR>[Called By]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
</UL>

<P><STRONG><a name="[e4]"></a>HAL_UART_ErrorCallback</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, portserial.o(i.HAL_UART_ErrorCallback))
<BR><BR>[Called By]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_DMAAbortOnError
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
</UL>

<P><STRONG><a name="[e1]"></a>HAL_UART_IRQHandler</STRONG> (Thumb, 666 bytes, Stack size 24 bytes, stm32l4xx_hal_uart.o(i.HAL_UART_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = HAL_UART_IRQHandler &rArr; UART_EndTransmit_IT &rArr; HAL_UART_TxCpltCallback &rArr; RS485_Enter_RX_Mode
</UL>
<BR>[Calls]<UL><LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_RxEventCallback
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_WakeupCallback
<LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_EndTransmit_IT
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_EndRxTransfer
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Abort
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Abort_IT
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_ErrorCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[3c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART1_IRQHandler
</UL>

<P><STRONG><a name="[e9]"></a>HAL_UART_Init</STRONG> (Thumb, 108 bytes, Stack size 8 bytes, stm32l4xx_hal_uart.o(i.HAL_UART_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 248<LI>Call Chain = HAL_UART_Init &rArr; HAL_UART_MspInit &rArr; HAL_RCCEx_PeriphCLKConfig &rArr; RCCEx_PLLSAI2_Config
</UL>
<BR>[Calls]<UL><LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_SetConfig
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_CheckIdleState
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_AdvFeatureConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART1_UART_Init
</UL>

<P><STRONG><a name="[ea]"></a>HAL_UART_MspInit</STRONG> (Thumb, 134 bytes, Stack size 176 bytes, usart.o(i.HAL_UART_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 240<LI>Call Chain = HAL_UART_MspInit &rArr; HAL_RCCEx_PeriphCLKConfig &rArr; RCCEx_PLLSAI2_Config
</UL>
<BR>[Calls]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCCEx_PeriphCLKConfig
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_EnableIRQ
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Init
</UL>

<P><STRONG><a name="[ee]"></a>HAL_UART_TxCpltCallback</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, portserial.o(i.HAL_UART_TxCpltCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_UART_TxCpltCallback &rArr; RS485_Enter_RX_Mode
</UL>
<BR>[Calls]<UL><LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RS485_Enter_RX_Mode
</UL>
<BR>[Called By]<UL><LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_EndTransmit_IT
</UL>

<P><STRONG><a name="[f]"></a>HardFault_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32l4xx_it.o(i.HardFault_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HardFault_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HardFault_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l496xx.o(RESET)
</UL>
<P><STRONG><a name="[f0]"></a>MX_CAN1_Init</STRONG> (Thumb, 66 bytes, Stack size 8 bytes, can.o(i.MX_CAN1_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = MX_CAN1_Init &rArr; HAL_CAN_Init &rArr; HAL_CAN_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_CAN_Init
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[f1]"></a>MX_FREERTOS_Init</STRONG> (Thumb, 74 bytes, Stack size 8 bytes, freertos.o(i.MX_FREERTOS_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 184 + Unknown Stack Size
<LI>Call Chain = MX_FREERTOS_Init &rArr; osThreadNew &rArr; xTaskCreate &rArr; pvPortMalloc &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;osThreadNew
</UL>
<BR>[Called By]<UL><LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[f3]"></a>MX_GPIO_Init</STRONG> (Thumb, 258 bytes, Stack size 56 bytes, gpio.o(i.MX_GPIO_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = MX_GPIO_Init &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_EnableIRQ
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[f5]"></a>MX_I2C1_Init</STRONG> (Thumb, 76 bytes, Stack size 8 bytes, i2c.o(i.MX_I2C1_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 280<LI>Call Chain = MX_I2C1_Init &rArr; HAL_I2C_Init &rArr; HAL_I2C_MspInit &rArr; HAL_RCCEx_PeriphCLKConfig &rArr; RCCEx_PLLSAI2_Config
</UL>
<BR>[Calls]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Init
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2CEx_ConfigDigitalFilter
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2CEx_ConfigAnalogFilter
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[f8]"></a>MX_I2C2_Init</STRONG> (Thumb, 76 bytes, Stack size 8 bytes, i2c.o(i.MX_I2C2_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 280<LI>Call Chain = MX_I2C2_Init &rArr; HAL_I2C_Init &rArr; HAL_I2C_MspInit &rArr; HAL_RCCEx_PeriphCLKConfig &rArr; RCCEx_PLLSAI2_Config
</UL>
<BR>[Calls]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Init
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2CEx_ConfigDigitalFilter
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2CEx_ConfigAnalogFilter
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[f9]"></a>MX_SPI2_Init</STRONG> (Thumb, 72 bytes, Stack size 8 bytes, spi.o(i.MX_SPI2_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = MX_SPI2_Init &rArr; HAL_SPI_Init &rArr; HAL_SPI_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_Init
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[fa]"></a>MX_TIM16_Init</STRONG> (Thumb, 48 bytes, Stack size 8 bytes, tim.o(i.MX_TIM16_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = MX_TIM16_Init &rArr; HAL_TIM_Base_Init &rArr; TIM_Base_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_Init
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[fb]"></a>MX_USART1_UART_Init</STRONG> (Thumb, 56 bytes, Stack size 8 bytes, usart.o(i.MX_USART1_UART_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 256<LI>Call Chain = MX_USART1_UART_Init &rArr; HAL_UART_Init &rArr; HAL_UART_MspInit &rArr; HAL_RCCEx_PeriphCLKConfig &rArr; RCCEx_PLLSAI2_Config
</UL>
<BR>[Calls]<UL><LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Init
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[10]"></a>MemManage_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32l4xx_it.o(i.MemManage_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[10]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MemManage_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[10]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MemManage_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l496xx.o(RESET)
</UL>
<P><STRONG><a name="[75]"></a>ModbusComTask</STRONG> (Thumb, 314 bytes, Stack size 16 bytes, freertos.o(i.ModbusComTask))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = ModbusComTask &rArr; RS485_Init &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;osDelay
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;eMBPoll
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;eMBInit
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;eMBEnable
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RS485_Init
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RS485_Enter_TX_Mode
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RS485_Enter_RX_Mode
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Modbus_Test_Update_Registers
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Modbus_Test_LED_Indicate
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Modbus_Test_Init
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Modbus_Test_Inc_Poll_Count
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_TogglePin
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
</UL>
<BR>[Address Reference Count : 1]<UL><LI> freertos.o(i.MX_FREERTOS_Init)
</UL>
<P><STRONG><a name="[127]"></a>Modbus_Test_Get_Holding_Regs</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, modbus_test.o(i.Modbus_Test_Get_Holding_Regs))
<BR><BR>[Called By]<UL><LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;eMBRegHoldingCB
</UL>

<P><STRONG><a name="[126]"></a>Modbus_Test_Inc_Callback_Count</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, modbus_test.o(i.Modbus_Test_Inc_Callback_Count))
<BR><BR>[Called By]<UL><LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;eMBRegHoldingCB
</UL>

<P><STRONG><a name="[102]"></a>Modbus_Test_Inc_Poll_Count</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, modbus_test.o(i.Modbus_Test_Inc_Poll_Count))
<BR><BR>[Called By]<UL><LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ModbusComTask
</UL>

<P><STRONG><a name="[158]"></a>Modbus_Test_Inc_Rx_Count</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, modbus_test.o(i.Modbus_Test_Inc_Rx_Count))
<BR><BR>[Called By]<UL><LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvvUARTRxISR
</UL>

<P><STRONG><a name="[157]"></a>Modbus_Test_Inc_Timer_Count</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, modbus_test.o(i.Modbus_Test_Inc_Timer_Count))
<BR><BR>[Called By]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvvTIMERExpiredISR
</UL>

<P><STRONG><a name="[159]"></a>Modbus_Test_Inc_Tx_Count</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, modbus_test.o(i.Modbus_Test_Inc_Tx_Count))
<BR><BR>[Called By]<UL><LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvvUARTTxReadyISR
</UL>

<P><STRONG><a name="[fc]"></a>Modbus_Test_Init</STRONG> (Thumb, 42 bytes, Stack size 0 bytes, modbus_test.o(i.Modbus_Test_Init))
<BR><BR>[Called By]<UL><LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ModbusComTask
</UL>

<P><STRONG><a name="[105]"></a>Modbus_Test_LED_Indicate</STRONG> (Thumb, 52 bytes, Stack size 0 bytes, modbus_test.o(i.Modbus_Test_LED_Indicate))
<BR><BR>[Calls]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_TogglePin
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
</UL>
<BR>[Called By]<UL><LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ModbusComTask
</UL>

<P><STRONG><a name="[103]"></a>Modbus_Test_Update_Registers</STRONG> (Thumb, 54 bytes, Stack size 0 bytes, modbus_test.o(i.Modbus_Test_Update_Registers))
<BR><BR>[Called By]<UL><LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ModbusComTask
</UL>

<P><STRONG><a name="[e]"></a>NMI_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32l4xx_it.o(i.NMI_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NMI_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NMI_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l496xx.o(RESET)
</UL>
<P><STRONG><a name="[107]"></a>RN7326_ReadReg</STRONG> (Thumb, 230 bytes, Stack size 48 bytes, rn7326.o(i.RN7326_ReadReg))
<BR><BR>[Stack]<UL><LI>Max Depth = 200<LI>Call Chain = RN7326_ReadReg &rArr; HAL_SPI_Receive &rArr; HAL_SPI_TransmitReceive &rArr; SPI_EndRxTxTransaction &rArr; SPI_WaitFifoStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;osDelay
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_TransmitReceive
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_Transmit
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_Receive
</UL>
<BR>[Called By]<UL><LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;test_read_voltage
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;test_read_LostVoltage
</UL>

<P><STRONG><a name="[ef]"></a>RS485_Enter_RX_Mode</STRONG> (Thumb, 36 bytes, Stack size 8 bytes, rs485.o(i.RS485_Enter_RX_Mode))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = RS485_Enter_RX_Mode
</UL>
<BR>[Calls]<UL><LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
</UL>
<BR>[Called By]<UL><LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ModbusComTask
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_TxCpltCallback
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vMBPortSerialEnable
</UL>

<P><STRONG><a name="[104]"></a>RS485_Enter_TX_Mode</STRONG> (Thumb, 34 bytes, Stack size 8 bytes, rs485.o(i.RS485_Enter_TX_Mode))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = RS485_Enter_TX_Mode
</UL>
<BR>[Calls]<UL><LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
</UL>
<BR>[Called By]<UL><LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ModbusComTask
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vMBPortSerialEnable
</UL>

<P><STRONG><a name="[fd]"></a>RS485_Init</STRONG> (Thumb, 56 bytes, Stack size 32 bytes, rs485.o(i.RS485_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = RS485_Init &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ModbusComTask
</UL>

<P><STRONG><a name="[77]"></a>SensorsTask</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, freertos.o(i.SensorsTask))
<BR><BR>[Stack]<UL><LI>Max Depth = 64 + In Cycle
<LI>Call Chain = SensorsTask &rArr;  SensorsTask (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;osDelay
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SensorsTask
</UL>
<BR>[Called By]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SensorsTask
</UL>
<BR>[Address Reference Count : 1]<UL><LI> freertos.o(i.MX_FREERTOS_Init)
</UL>
<P><STRONG><a name="[78]"></a>SysMonitorTask</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, freertos.o(i.SysMonitorTask))
<BR><BR>[Stack]<UL><LI>Max Depth = 16 + In Cycle
<LI>Call Chain = SysMonitorTask &rArr;  SysMonitorTask (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysMonitorTask
</UL>
<BR>[Called By]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysMonitorTask
</UL>
<BR>[Address Reference Count : 1]<UL><LI> freertos.o(i.MX_FREERTOS_Init)
</UL>
<P><STRONG><a name="[16]"></a>SysTick_Handler</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, cmsis_os2.o(i.SysTick_Handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = SysTick_Handler &rArr; xPortSysTickHandler &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskGetSchedulerState
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xPortSysTickHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l496xx.o(RESET)
</UL>
<P><STRONG><a name="[10c]"></a>SystemClock_Config</STRONG> (Thumb, 114 bytes, Stack size 96 bytes, main.o(i.SystemClock_Config))
<BR><BR>[Stack]<UL><LI>Max Depth = 204<LI>Call Chain = SystemClock_Config &rArr; HAL_RCC_OscConfig &rArr; HAL_InitTick &rArr; HAL_TIM_Base_Init &rArr; TIM_Base_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_OscConfig
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PWREx_ControlVoltageScaling
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[71]"></a>SystemInit</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, system_stm32l4xx.o(i.SystemInit))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l496xx.o(.text)
</UL>
<P><STRONG><a name="[30]"></a>TIM1_UP_TIM16_IRQHandler</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, stm32l4xx_it.o(i.TIM1_UP_TIM16_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = TIM1_UP_TIM16_IRQHandler &rArr; HAL_TIM_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvvTIMERExpiredISR
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l496xx.o(RESET)
</UL>
<P><STRONG><a name="[4d]"></a>TIM6_DAC_IRQHandler</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32l4xx_it.o(i.TIM6_DAC_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = TIM6_DAC_IRQHandler &rArr; HAL_TIM_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l496xx.o(RESET)
</UL>
<P><STRONG><a name="[d6]"></a>TIM_Base_SetConfig</STRONG> (Thumb, 160 bytes, Stack size 36 bytes, stm32l4xx_hal_tim.o(i.TIM_Base_SetConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = TIM_Base_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_Init
</UL>

<P><STRONG><a name="[ec]"></a>UART_AdvFeatureConfig</STRONG> (Thumb, 200 bytes, Stack size 0 bytes, stm32l4xx_hal_uart.o(i.UART_AdvFeatureConfig))
<BR><BR>[Called By]<UL><LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Init
</UL>

<P><STRONG><a name="[ed]"></a>UART_CheckIdleState</STRONG> (Thumb, 96 bytes, Stack size 24 bytes, stm32l4xx_hal_uart.o(i.UART_CheckIdleState))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = UART_CheckIdleState &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_WaitOnFlagUntilTimeout
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Init
</UL>

<P><STRONG><a name="[eb]"></a>UART_SetConfig</STRONG> (Thumb, 662 bytes, Stack size 24 bytes, stm32l4xx_hal_uart.o(i.UART_SetConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = UART_SetConfig &rArr; __aeabi_uldivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetPCLK1Freq
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetSysClockFreq
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetPCLK2Freq
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Init
</UL>

<P><STRONG><a name="[10f]"></a>UART_WaitOnFlagUntilTimeout</STRONG> (Thumb, 190 bytes, Stack size 32 bytes, stm32l4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_CheckIdleState
</UL>

<P><STRONG><a name="[3c]"></a>USART1_IRQHandler</STRONG> (Thumb, 80 bytes, Stack size 8 bytes, stm32l4xx_it.o(i.USART1_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = USART1_IRQHandler &rArr; HAL_UART_IRQHandler &rArr; UART_EndTransmit_IT &rArr; HAL_UART_TxCpltCallback &rArr; RS485_Enter_RX_Mode
</UL>
<BR>[Calls]<UL><LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvvUARTTxReadyISR
<LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvvUARTRxISR
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_ClearPendingIRQ
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l496xx.o(RESET)
</UL>
<P><STRONG><a name="[12]"></a>UsageFault_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32l4xx_it.o(i.UsageFault_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[12]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsageFault_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[12]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsageFault_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l496xx.o(RESET)
</UL>
<P><STRONG><a name="[100]"></a>eMBEnable</STRONG> (Thumb, 28 bytes, Stack size 16 bytes, mb.o(i.eMBEnable))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = eMBEnable
</UL>
<BR>[Called By]<UL><LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ModbusComTask
</UL>

<P><STRONG><a name="[9]"></a>eMBFuncReadCoils</STRONG> (Thumb, 124 bytes, Stack size 24 bytes, mbfunccoils.o(i.eMBFuncReadCoils))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = eMBFuncReadCoils &rArr; eMBRegCoilsCB &rArr; xMBUtilSetBits
</UL>
<BR>[Calls]<UL><LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prveMBError2Exception
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;eMBRegCoilsCB
</UL>
<BR>[Address Reference Count : 1]<UL><LI> mb.o(.data)
</UL>
<P><STRONG><a name="[c]"></a>eMBFuncReadDiscreteInputs</STRONG> (Thumb, 120 bytes, Stack size 16 bytes, mbfuncdisc.o(i.eMBFuncReadDiscreteInputs))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = eMBFuncReadDiscreteInputs &rArr; eMBRegDiscreteCB &rArr; xMBUtilGetBits
</UL>
<BR>[Calls]<UL><LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;eMBRegDiscreteCB
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prveMBError2Exception
</UL>
<BR>[Address Reference Count : 1]<UL><LI> mb.o(.data)
</UL>
<P><STRONG><a name="[5]"></a>eMBFuncReadHoldingRegister</STRONG> (Thumb, 102 bytes, Stack size 16 bytes, mbfuncholding.o(i.eMBFuncReadHoldingRegister))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = eMBFuncReadHoldingRegister &rArr; eMBRegHoldingCB
</UL>
<BR>[Calls]<UL><LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;eMBRegHoldingCB
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prveMBError2Exception
</UL>
<BR>[Address Reference Count : 1]<UL><LI> mb.o(.data)
</UL>
<P><STRONG><a name="[4]"></a>eMBFuncReadInputRegister</STRONG> (Thumb, 100 bytes, Stack size 16 bytes, mbfuncinput.o(i.eMBFuncReadInputRegister))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = eMBFuncReadInputRegister &rArr; eMBRegInputCB
</UL>
<BR>[Calls]<UL><LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;eMBRegInputCB
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prveMBError2Exception
</UL>
<BR>[Address Reference Count : 1]<UL><LI> mb.o(.data)
</UL>
<P><STRONG><a name="[8]"></a>eMBFuncReadWriteMultipleHoldingRegister</STRONG> (Thumb, 156 bytes, Stack size 24 bytes, mbfuncholding.o(i.eMBFuncReadWriteMultipleHoldingRegister))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = eMBFuncReadWriteMultipleHoldingRegister &rArr; eMBRegHoldingCB
</UL>
<BR>[Calls]<UL><LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;eMBRegHoldingCB
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prveMBError2Exception
</UL>
<BR>[Address Reference Count : 1]<UL><LI> mb.o(.data)
</UL>
<P><STRONG><a name="[3]"></a>eMBFuncReportSlaveID</STRONG> (Thumb, 26 bytes, Stack size 16 bytes, mbfuncother.o(i.eMBFuncReportSlaveID))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = eMBFuncReportSlaveID
</UL>
<BR>[Calls]<UL><LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Address Reference Count : 1]<UL><LI> mb.o(.data)
</UL>
<P><STRONG><a name="[a]"></a>eMBFuncWriteCoil</STRONG> (Thumb, 90 bytes, Stack size 16 bytes, mbfunccoils.o(i.eMBFuncWriteCoil))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = eMBFuncWriteCoil &rArr; eMBRegCoilsCB &rArr; xMBUtilSetBits
</UL>
<BR>[Calls]<UL><LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prveMBError2Exception
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;eMBRegCoilsCB
</UL>
<BR>[Address Reference Count : 1]<UL><LI> mb.o(.data)
</UL>
<P><STRONG><a name="[7]"></a>eMBFuncWriteHoldingRegister</STRONG> (Thumb, 50 bytes, Stack size 8 bytes, mbfuncholding.o(i.eMBFuncWriteHoldingRegister))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = eMBFuncWriteHoldingRegister &rArr; eMBRegHoldingCB
</UL>
<BR>[Calls]<UL><LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;eMBRegHoldingCB
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prveMBError2Exception
</UL>
<BR>[Address Reference Count : 1]<UL><LI> mb.o(.data)
</UL>
<P><STRONG><a name="[b]"></a>eMBFuncWriteMultipleCoils</STRONG> (Thumb, 102 bytes, Stack size 24 bytes, mbfunccoils.o(i.eMBFuncWriteMultipleCoils))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = eMBFuncWriteMultipleCoils &rArr; eMBRegCoilsCB &rArr; xMBUtilSetBits
</UL>
<BR>[Calls]<UL><LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prveMBError2Exception
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;eMBRegCoilsCB
</UL>
<BR>[Address Reference Count : 1]<UL><LI> mb.o(.data)
</UL>
<P><STRONG><a name="[6]"></a>eMBFuncWriteMultipleHoldingRegister</STRONG> (Thumb, 82 bytes, Stack size 16 bytes, mbfuncholding.o(i.eMBFuncWriteMultipleHoldingRegister))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = eMBFuncWriteMultipleHoldingRegister &rArr; eMBRegHoldingCB
</UL>
<BR>[Calls]<UL><LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;eMBRegHoldingCB
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prveMBError2Exception
</UL>
<BR>[Address Reference Count : 1]<UL><LI> mb.o(.data)
</UL>
<P><STRONG><a name="[fe]"></a>eMBInit</STRONG> (Thumb, 102 bytes, Stack size 24 bytes, mb.o(i.eMBInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = eMBInit &rArr; eMBRTUInit &rArr; xMBPortTimersInit
</UL>
<BR>[Calls]<UL><LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xMBPortEventInit
<LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;eMBRTUInit
</UL>
<BR>[Called By]<UL><LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ModbusComTask
</UL>

<P><STRONG><a name="[101]"></a>eMBPoll</STRONG> (Thumb, 174 bytes, Stack size 24 bytes, mb.o(i.eMBPoll))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = eMBPoll
</UL>
<BR>[Calls]<UL><LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xMBPortEventGet
<LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xMBPortEventPost
</UL>
<BR>[Called By]<UL><LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ModbusComTask
</UL>

<P><STRONG><a name="[11a]"></a>eMBRTUInit</STRONG> (Thumb, 68 bytes, Stack size 24 bytes, mbrtu.o(i.eMBRTUInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = eMBRTUInit &rArr; xMBPortTimersInit
</UL>
<BR>[Calls]<UL><LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xMBPortTimersInit
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xMBPortSerialInit
</UL>
<BR>[Called By]<UL><LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;eMBInit
</UL>

<P><STRONG><a name="[7c]"></a>eMBRTUReceive</STRONG> (Thumb, 64 bytes, Stack size 24 bytes, mbrtu.o(i.eMBRTUReceive))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = eMBRTUReceive &rArr; usMBCRC16
</UL>
<BR>[Calls]<UL><LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usMBCRC16
</UL>
<BR>[Address Reference Count : 1]<UL><LI> mb.o(i.eMBInit)
</UL>
<P><STRONG><a name="[7b]"></a>eMBRTUSend</STRONG> (Thumb, 106 bytes, Stack size 24 bytes, mbrtu.o(i.eMBRTUSend))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = eMBRTUSend &rArr; vMBPortSerialEnable &rArr; RS485_Enter_TX_Mode
</UL>
<BR>[Calls]<UL><LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vMBPortSerialEnable
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usMBCRC16
</UL>
<BR>[Address Reference Count : 1]<UL><LI> mb.o(i.eMBInit)
</UL>
<P><STRONG><a name="[79]"></a>eMBRTUStart</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, mbrtu.o(i.eMBRTUStart))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = eMBRTUStart &rArr; vMBPortSerialEnable &rArr; RS485_Enter_TX_Mode
</UL>
<BR>[Calls]<UL><LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vMBPortTimersEnable
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vMBPortSerialEnable
</UL>
<BR>[Address Reference Count : 1]<UL><LI> mb.o(i.eMBInit)
</UL>
<P><STRONG><a name="[7a]"></a>eMBRTUStop</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, mbrtu.o(i.eMBRTUStop))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = eMBRTUStop &rArr; vMBPortSerialEnable &rArr; RS485_Enter_TX_Mode
</UL>
<BR>[Calls]<UL><LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vMBPortTimersDisable
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vMBPortSerialEnable
</UL>
<BR>[Address Reference Count : 1]<UL><LI> mb.o(i.eMBInit)
</UL>
<P><STRONG><a name="[114]"></a>eMBRegCoilsCB</STRONG> (Thumb, 110 bytes, Stack size 24 bytes, mdcb.o(i.eMBRegCoilsCB))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = eMBRegCoilsCB &rArr; xMBUtilSetBits
</UL>
<BR>[Calls]<UL><LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xMBUtilSetBits
<LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xMBUtilGetBits
</UL>
<BR>[Called By]<UL><LI><a href="#[b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;eMBFuncWriteMultipleCoils
<LI><a href="#[a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;eMBFuncWriteCoil
<LI><a href="#[9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;eMBFuncReadCoils
</UL>

<P><STRONG><a name="[116]"></a>eMBRegDiscreteCB</STRONG> (Thumb, 72 bytes, Stack size 24 bytes, mdcb.o(i.eMBRegDiscreteCB))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = eMBRegDiscreteCB &rArr; xMBUtilGetBits
</UL>
<BR>[Calls]<UL><LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xMBUtilGetBits
</UL>
<BR>[Called By]<UL><LI><a href="#[c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;eMBFuncReadDiscreteInputs
</UL>

<P><STRONG><a name="[117]"></a>eMBRegHoldingCB</STRONG> (Thumb, 132 bytes, Stack size 32 bytes, mdcb.o(i.eMBRegHoldingCB))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = eMBRegHoldingCB
</UL>
<BR>[Calls]<UL><LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Modbus_Test_Inc_Callback_Count
<LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Modbus_Test_Get_Holding_Regs
</UL>
<BR>[Called By]<UL><LI><a href="#[6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;eMBFuncWriteMultipleHoldingRegister
<LI><a href="#[7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;eMBFuncWriteHoldingRegister
<LI><a href="#[8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;eMBFuncReadWriteMultipleHoldingRegister
<LI><a href="#[5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;eMBFuncReadHoldingRegister
</UL>

<P><STRONG><a name="[118]"></a>eMBRegInputCB</STRONG> (Thumb, 56 bytes, Stack size 12 bytes, mdcb.o(i.eMBRegInputCB))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = eMBRegInputCB
</UL>
<BR>[Called By]<UL><LI><a href="#[4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;eMBFuncReadInputRegister
</UL>

<P><STRONG><a name="[8e]"></a>main</STRONG> (Thumb, 50 bytes, Stack size 0 bytes, main.o(i.main))
<BR><BR>[Stack]<UL><LI>Max Depth = 280 + Unknown Stack Size
<LI>Call Chain = main &rArr; MX_I2C2_Init &rArr; HAL_I2C_Init &rArr; HAL_I2C_MspInit &rArr; HAL_RCCEx_PeriphCLKConfig &rArr; RCCEx_PLLSAI2_Config
</UL>
<BR>[Calls]<UL><LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;osKernelStart
<LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;osKernelInitialize
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART1_UART_Init
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM16_Init
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_SPI2_Init
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_I2C2_Init
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_I2C1_Init
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_GPIO_Init
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_FREERTOS_Init
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_CAN1_Init
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
<LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>
<BR>[Called By]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_main
</UL>

<P><STRONG><a name="[9f]"></a>osDelay</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, cmsis_os2.o(i.osDelay))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = osDelay &rArr; vTaskDelay &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskDelay
</UL>
<BR>[Called By]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SensorsTask
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ModbusComTask
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EnergyMeterTask
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CANComTask
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RN7326_ReadReg
</UL>

<P><STRONG><a name="[128]"></a>osKernelInitialize</STRONG> (Thumb, 34 bytes, Stack size 0 bytes, cmsis_os2.o(i.osKernelInitialize))
<BR><BR>[Called By]<UL><LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[129]"></a>osKernelStart</STRONG> (Thumb, 48 bytes, Stack size 8 bytes, cmsis_os2.o(i.osKernelStart))
<BR><BR>[Stack]<UL><LI>Max Depth = 168 + Unknown Stack Size
<LI>Call Chain = osKernelStart &rArr; vTaskStartScheduler &rArr; xTimerCreateTimerTask &rArr; prvCheckForValidListAndQueue &rArr; xQueueGenericCreateStatic &rArr; prvInitialiseNewQueue &rArr; xQueueGenericReset &rArr; xTaskRemoveFromEventList
</UL>
<BR>[Calls]<UL><LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskStartScheduler
</UL>
<BR>[Called By]<UL><LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[f2]"></a>osThreadNew</STRONG> (Thumb, 186 bytes, Stack size 48 bytes, cmsis_os2.o(i.osThreadNew))
<BR><BR>[Stack]<UL><LI>Max Depth = 176 + Unknown Stack Size
<LI>Call Chain = osThreadNew &rArr; xTaskCreate &rArr; pvPortMalloc &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCreateStatic
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCreate
</UL>
<BR>[Called By]<UL><LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_FREERTOS_Init
</UL>

<P><STRONG><a name="[115]"></a>prveMBError2Exception</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, mbutils.o(i.prveMBError2Exception))
<BR><BR>[Called By]<UL><LI><a href="#[4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;eMBFuncReadInputRegister
<LI><a href="#[6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;eMBFuncWriteMultipleHoldingRegister
<LI><a href="#[7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;eMBFuncWriteHoldingRegister
<LI><a href="#[8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;eMBFuncReadWriteMultipleHoldingRegister
<LI><a href="#[5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;eMBFuncReadHoldingRegister
<LI><a href="#[c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;eMBFuncReadDiscreteInputs
<LI><a href="#[b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;eMBFuncWriteMultipleCoils
<LI><a href="#[a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;eMBFuncWriteCoil
<LI><a href="#[9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;eMBFuncReadCoils
</UL>

<P><STRONG><a name="[10e]"></a>prvvTIMERExpiredISR</STRONG> (Thumb, 46 bytes, Stack size 8 bytes, porttimer.o(i.prvvTIMERExpiredISR))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = prvvTIMERExpiredISR
</UL>
<BR>[Calls]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_TogglePin
<LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Modbus_Test_Inc_Timer_Count
</UL>
<BR>[Called By]<UL><LI><a href="#[30]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM1_UP_TIM16_IRQHandler
</UL>

<P><STRONG><a name="[112]"></a>prvvUARTRxISR</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, portserial.o(i.prvvUARTRxISR))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = prvvUARTRxISR
</UL>
<BR>[Calls]<UL><LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Modbus_Test_Inc_Rx_Count
</UL>
<BR>[Called By]<UL><LI><a href="#[3c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART1_IRQHandler
</UL>

<P><STRONG><a name="[111]"></a>prvvUARTTxReadyISR</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, portserial.o(i.prvvUARTTxReadyISR))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = prvvUARTTxReadyISR
</UL>
<BR>[Calls]<UL><LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Modbus_Test_Inc_Tx_Count
</UL>
<BR>[Called By]<UL><LI><a href="#[3c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART1_IRQHandler
</UL>

<P><STRONG><a name="[15a]"></a>pvPortMalloc</STRONG> (Thumb, 214 bytes, Stack size 24 bytes, heap_4.o(i.pvPortMalloc))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = pvPortMalloc &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskResumeAll
<LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskSuspendAll
<LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvInsertBlockIntoFreeList
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvHeapInit
</UL>
<BR>[Called By]<UL><LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCreate
</UL>

<P><STRONG><a name="[144]"></a>pxPortInitialiseStack</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, port.o(i.pxPortInitialiseStack))
<BR><BR>[Called By]<UL><LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvInitialiseNewTask
</UL>

<P><STRONG><a name="[a1]"></a>test_read_LostVoltage</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, rn7326.o(i.test_read_LostVoltage))
<BR><BR>[Stack]<UL><LI>Max Depth = 208<LI>Call Chain = test_read_LostVoltage &rArr; RN7326_ReadReg &rArr; HAL_SPI_Receive &rArr; HAL_SPI_TransmitReceive &rArr; SPI_EndRxTxTransaction &rArr; SPI_WaitFifoStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RN7326_ReadReg
</UL>
<BR>[Called By]<UL><LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EnergyMeterTask
</UL>

<P><STRONG><a name="[a2]"></a>test_read_voltage</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, rn7326.o(i.test_read_voltage))
<BR><BR>[Stack]<UL><LI>Max Depth = 208<LI>Call Chain = test_read_voltage &rArr; RN7326_ReadReg &rArr; HAL_SPI_Receive &rArr; HAL_SPI_TransmitReceive &rArr; SPI_EndRxTxTransaction &rArr; SPI_WaitFifoStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RN7326_ReadReg
</UL>
<BR>[Called By]<UL><LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EnergyMeterTask
</UL>

<P><STRONG><a name="[120]"></a>usMBCRC16</STRONG> (Thumb, 38 bytes, Stack size 16 bytes, mbcrc.o(i.usMBCRC16))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = usMBCRC16
</UL>
<BR>[Called By]<UL><LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;eMBRTUSend
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;eMBRTUReceive
</UL>

<P><STRONG><a name="[12f]"></a>uxListRemove</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, list.o(i.uxListRemove))
<BR><BR>[Called By]<UL><LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskRemoveFromEventList
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskPriorityDisinherit
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskResumeAll
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvSwitchTimerLists
<LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessReceivedCommands
<LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessExpiredTimer
<LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskIncrementTick
<LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvCheckTasksWaitingTermination
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvAddCurrentTaskToDelayedList
</UL>

<P><STRONG><a name="[161]"></a>vApplicationGetIdleTaskMemory</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, cmsis_os2.o(i.vApplicationGetIdleTaskMemory))
<BR><BR>[Called By]<UL><LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskStartScheduler
</UL>

<P><STRONG><a name="[16f]"></a>vApplicationGetTimerTaskMemory</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, cmsis_os2.o(i.vApplicationGetTimerTaskMemory))
<BR><BR>[Called By]<UL><LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTimerCreateTimerTask
</UL>

<P><STRONG><a name="[137]"></a>vListInitialise</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, list.o(i.vListInitialise))
<BR><BR>[Called By]<UL><LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericReset
<LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvCheckForValidListAndQueue
<LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvInitialiseTaskLists
</UL>

<P><STRONG><a name="[143]"></a>vListInitialiseItem</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, list.o(i.vListInitialiseItem))
<BR><BR>[Called By]<UL><LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvInitialiseNewTask
</UL>

<P><STRONG><a name="[131]"></a>vListInsert</STRONG> (Thumb, 48 bytes, Stack size 12 bytes, list.o(i.vListInsert))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = vListInsert
</UL>
<BR>[Called By]<UL><LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskPlaceOnEventList
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvSwitchTimerLists
<LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvInsertTimerInActiveList
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvAddCurrentTaskToDelayedList
</UL>

<P><STRONG><a name="[130]"></a>vListInsertEnd</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, list.o(i.vListInsertEnd))
<BR><BR>[Called By]<UL><LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskRemoveFromEventList
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskPriorityDisinherit
<LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskPlaceOnEventListRestricted
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskResumeAll
<LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskIncrementTick
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvAddNewTaskToReadyList
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvAddCurrentTaskToDelayedList
</UL>

<P><STRONG><a name="[121]"></a>vMBPortSerialEnable</STRONG> (Thumb, 76 bytes, Stack size 16 bytes, portserial.o(i.vMBPortSerialEnable))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = vMBPortSerialEnable &rArr; RS485_Enter_TX_Mode
</UL>
<BR>[Calls]<UL><LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RS485_Enter_TX_Mode
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RS485_Enter_RX_Mode
</UL>
<BR>[Called By]<UL><LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xMBRTUTransmitFSM
<LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;eMBRTUStop
<LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;eMBRTUStart
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;eMBRTUSend
</UL>

<P><STRONG><a name="[123]"></a>vMBPortTimersDisable</STRONG> (Thumb, 56 bytes, Stack size 0 bytes, porttimer.o(i.vMBPortTimersDisable))
<BR><BR>[Called By]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xMBRTUTimerT35Expired
<LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;eMBRTUStop
</UL>

<P><STRONG><a name="[122]"></a>vMBPortTimersEnable</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, porttimer.o(i.vMBPortTimersEnable))
<BR><BR>[Called By]<UL><LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xMBRTUReceiveFSM
<LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;eMBRTUStart
</UL>

<P><STRONG><a name="[133]"></a>vPortEnterCritical</STRONG> (Thumb, 54 bytes, Stack size 0 bytes, port.o(i.vPortEnterCritical))
<BR><BR>[Called By]<UL><LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCheckForTimeOut
<LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceive
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSend
<LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericReset
<LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vQueueWaitForMessageRestricted
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvUnlockQueue
<LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvIsQueueFull
<LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvIsQueueEmpty
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskResumeAll
<LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvCheckForValidListAndQueue
<LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvCheckTasksWaitingTermination
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvAddNewTaskToReadyList
</UL>

<P><STRONG><a name="[135]"></a>vPortExitCritical</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, port.o(i.vPortExitCritical))
<BR><BR>[Called By]<UL><LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCheckForTimeOut
<LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceive
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSend
<LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericReset
<LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vQueueWaitForMessageRestricted
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvUnlockQueue
<LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvIsQueueFull
<LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvIsQueueEmpty
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskResumeAll
<LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvCheckForValidListAndQueue
<LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvCheckTasksWaitingTermination
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvAddNewTaskToReadyList
</UL>

<P><STRONG><a name="[13f]"></a>vPortFree</STRONG> (Thumb, 94 bytes, Stack size 16 bytes, heap_4.o(i.vPortFree))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = vPortFree &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskResumeAll
<LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskSuspendAll
<LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvInsertBlockIntoFreeList
</UL>
<BR>[Called By]<UL><LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessReceivedCommands
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCreate
<LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvDeleteTCB
</UL>

<P><STRONG><a name="[166]"></a>vPortSetupTimerInterrupt</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, port.o(i.vPortSetupTimerInterrupt))
<BR><BR>[Called By]<UL><LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xPortStartScheduler
</UL>

<P><STRONG><a name="[15d]"></a>vPortValidateInterruptPriority</STRONG> (Thumb, 74 bytes, Stack size 8 bytes, port.o(i.vPortValidateInterruptPriority))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = vPortValidateInterruptPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortGetIPSR
</UL>
<BR>[Called By]<UL><LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSendFromISR
</UL>

<P><STRONG><a name="[139]"></a>vQueueAddToRegistry</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, queue.o(i.vQueueAddToRegistry))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = vQueueAddToRegistry
</UL>
<BR>[Called By]<UL><LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvCheckForValidListAndQueue
</UL>

<P><STRONG><a name="[150]"></a>vQueueWaitForMessageRestricted</STRONG> (Thumb, 70 bytes, Stack size 16 bytes, queue.o(i.vQueueWaitForMessageRestricted))
<BR><BR>[Stack]<UL><LI>Max Depth = 68<LI>Call Chain = vQueueWaitForMessageRestricted &rArr; vTaskPlaceOnEventListRestricted &rArr; prvAddCurrentTaskToDelayedList &rArr; vListInsert
</UL>
<BR>[Calls]<UL><LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskPlaceOnEventListRestricted
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvUnlockQueue
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
</UL>
<BR>[Called By]<UL><LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessTimerOrBlockTask
</UL>

<P><STRONG><a name="[12a]"></a>vTaskDelay</STRONG> (Thumb, 70 bytes, Stack size 8 bytes, tasks.o(i.vTaskDelay))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = vTaskDelay &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskResumeAll
<LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskSuspendAll
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvAddCurrentTaskToDelayedList
</UL>
<BR>[Called By]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;osDelay
</UL>

<P><STRONG><a name="[16b]"></a>vTaskInternalSetTimeOutState</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, tasks.o(i.vTaskInternalSetTimeOutState))
<BR><BR>[Called By]<UL><LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCheckForTimeOut
<LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceive
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSend
</UL>

<P><STRONG><a name="[156]"></a>vTaskMissedYield</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, tasks.o(i.vTaskMissedYield))
<BR><BR>[Called By]<UL><LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvUnlockQueue
</UL>

<P><STRONG><a name="[160]"></a>vTaskPlaceOnEventList</STRONG> (Thumb, 46 bytes, Stack size 8 bytes, tasks.o(i.vTaskPlaceOnEventList))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = vTaskPlaceOnEventList &rArr; prvAddCurrentTaskToDelayedList &rArr; vListInsert
</UL>
<BR>[Calls]<UL><LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInsert
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvAddCurrentTaskToDelayedList
</UL>
<BR>[Called By]<UL><LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceive
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSend
</UL>

<P><STRONG><a name="[15f]"></a>vTaskPlaceOnEventListRestricted</STRONG> (Thumb, 54 bytes, Stack size 16 bytes, tasks.o(i.vTaskPlaceOnEventListRestricted))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = vTaskPlaceOnEventListRestricted &rArr; prvAddCurrentTaskToDelayedList &rArr; vListInsert
</UL>
<BR>[Calls]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInsertEnd
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvAddCurrentTaskToDelayedList
</UL>
<BR>[Called By]<UL><LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vQueueWaitForMessageRestricted
</UL>

<P><STRONG><a name="[12b]"></a>vTaskStartScheduler</STRONG> (Thumb, 126 bytes, Stack size 40 bytes, tasks.o(i.vTaskStartScheduler))
<BR><BR>[Stack]<UL><LI>Max Depth = 160 + Unknown Stack Size
<LI>Call Chain = vTaskStartScheduler &rArr; xTimerCreateTimerTask &rArr; prvCheckForValidListAndQueue &rArr; xQueueGenericCreateStatic &rArr; prvInitialiseNewQueue &rArr; xQueueGenericReset &rArr; xTaskRemoveFromEventList
</UL>
<BR>[Calls]<UL><LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTimerCreateTimerTask
<LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xPortStartScheduler
<LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vApplicationGetIdleTaskMemory
<LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCreateStatic
</UL>
<BR>[Called By]<UL><LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;osKernelStart
</UL>

<P><STRONG><a name="[14e]"></a>vTaskSuspendAll</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, tasks.o(i.vTaskSuspendAll))
<BR><BR>[Called By]<UL><LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceive
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSend
<LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortFree
<LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pvPortMalloc
<LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessTimerOrBlockTask
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskDelay
</UL>

<P><STRONG><a name="[94]"></a>vTaskSwitchContext</STRONG> (Thumb, 94 bytes, Stack size 8 bytes, tasks.o(i.vTaskSwitchContext))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = vTaskSwitchContext
</UL>
<BR>[Called By]<UL><LI><a href="#[15]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PendSV_Handler
</UL>

<P><STRONG><a name="[11c]"></a>xMBPortEventGet</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, portevent.o(i.xMBPortEventGet))
<BR><BR>[Called By]<UL><LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;eMBPoll
</UL>

<P><STRONG><a name="[11b]"></a>xMBPortEventInit</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, portevent.o(i.xMBPortEventInit))
<BR><BR>[Called By]<UL><LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;eMBInit
</UL>

<P><STRONG><a name="[11d]"></a>xMBPortEventPost</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, portevent.o(i.xMBPortEventPost))
<BR><BR>[Called By]<UL><LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;eMBPoll
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xMBRTUTransmitFSM
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xMBRTUTimerT35Expired
</UL>

<P><STRONG><a name="[164]"></a>xMBPortSerialGetByte</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, portserial.o(i.xMBPortSerialGetByte))
<BR><BR>[Called By]<UL><LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xMBRTUReceiveFSM
</UL>

<P><STRONG><a name="[11e]"></a>xMBPortSerialInit</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, portserial.o(i.xMBPortSerialInit))
<BR><BR>[Called By]<UL><LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;eMBRTUInit
</UL>

<P><STRONG><a name="[165]"></a>xMBPortSerialPutByte</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, portserial.o(i.xMBPortSerialPutByte))
<BR><BR>[Called By]<UL><LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xMBRTUTransmitFSM
</UL>

<P><STRONG><a name="[11f]"></a>xMBPortTimersInit</STRONG> (Thumb, 78 bytes, Stack size 8 bytes, porttimer.o(i.xMBPortTimersInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = xMBPortTimersInit
</UL>
<BR>[Called By]<UL><LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;eMBRTUInit
</UL>

<P><STRONG><a name="[7d]"></a>xMBRTUReceiveFSM</STRONG> (Thumb, 102 bytes, Stack size 16 bytes, mbrtu.o(i.xMBRTUReceiveFSM))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = xMBRTUReceiveFSM
</UL>
<BR>[Calls]<UL><LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xMBPortSerialGetByte
<LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vMBPortTimersEnable
</UL>
<BR>[Address Reference Count : 1]<UL><LI> mb.o(i.eMBInit)
</UL>
<P><STRONG><a name="[7f]"></a>xMBRTUTimerT35Expired</STRONG> (Thumb, 48 bytes, Stack size 16 bytes, mbrtu.o(i.xMBRTUTimerT35Expired))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = xMBRTUTimerT35Expired
</UL>
<BR>[Calls]<UL><LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xMBPortEventPost
<LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vMBPortTimersDisable
</UL>
<BR>[Address Reference Count : 1]<UL><LI> mb.o(i.eMBInit)
</UL>
<P><STRONG><a name="[7e]"></a>xMBRTUTransmitFSM</STRONG> (Thumb, 110 bytes, Stack size 16 bytes, mbrtu.o(i.xMBRTUTransmitFSM))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = xMBRTUTransmitFSM &rArr; vMBPortSerialEnable &rArr; RS485_Enter_TX_Mode
</UL>
<BR>[Calls]<UL><LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xMBPortSerialPutByte
<LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xMBPortEventPost
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vMBPortSerialEnable
</UL>
<BR>[Address Reference Count : 1]<UL><LI> mb.o(i.eMBInit)
</UL>
<P><STRONG><a name="[124]"></a>xMBUtilGetBits</STRONG> (Thumb, 34 bytes, Stack size 8 bytes, mbutils.o(i.xMBUtilGetBits))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = xMBUtilGetBits
</UL>
<BR>[Called By]<UL><LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;eMBRegDiscreteCB
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;eMBRegCoilsCB
</UL>

<P><STRONG><a name="[125]"></a>xMBUtilSetBits</STRONG> (Thumb, 42 bytes, Stack size 16 bytes, mbutils.o(i.xMBUtilSetBits))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = xMBUtilSetBits
</UL>
<BR>[Called By]<UL><LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;eMBRegCoilsCB
</UL>

<P><STRONG><a name="[163]"></a>xPortStartScheduler</STRONG> (Thumb, 222 bytes, Stack size 16 bytes, port.o(i.xPortStartScheduler))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = xPortStartScheduler
</UL>
<BR>[Calls]<UL><LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortSetupTimerInterrupt
<LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__asm___6_port_c_39a90d8d__prvEnableVFP
<LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__asm___6_port_c_39a90d8d__prvStartFirstTask
</UL>
<BR>[Called By]<UL><LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskStartScheduler
</UL>

<P><STRONG><a name="[10b]"></a>xPortSysTickHandler</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, port.o(i.xPortSysTickHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = xPortSysTickHandler &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskIncrementTick
</UL>
<BR>[Called By]<UL><LI><a href="#[16]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Handler
</UL>

<P><STRONG><a name="[138]"></a>xQueueGenericCreateStatic</STRONG> (Thumb, 108 bytes, Stack size 16 bytes, queue.o(i.xQueueGenericCreateStatic))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = xQueueGenericCreateStatic &rArr; prvInitialiseNewQueue &rArr; xQueueGenericReset &rArr; xTaskRemoveFromEventList
</UL>
<BR>[Calls]<UL><LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvInitialiseNewQueue
</UL>
<BR>[Called By]<UL><LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvCheckForValidListAndQueue
</UL>

<P><STRONG><a name="[141]"></a>xQueueGenericReset</STRONG> (Thumb, 134 bytes, Stack size 16 bytes, queue.o(i.xQueueGenericReset))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = xQueueGenericReset &rArr; xTaskRemoveFromEventList
</UL>
<BR>[Calls]<UL><LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskRemoveFromEventList
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
<LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInitialise
</UL>
<BR>[Called By]<UL><LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvInitialiseNewQueue
</UL>

<P><STRONG><a name="[16a]"></a>xQueueGenericSend</STRONG> (Thumb, 352 bytes, Stack size 56 bytes, queue.o(i.xQueueGenericSend))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = xQueueGenericSend &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskRemoveFromEventList
<LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCheckForTimeOut
<LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskPlaceOnEventList
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskInternalSetTimeOutState
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvUnlockQueue
<LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvIsQueueFull
<LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvCopyDataToQueue
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskResumeAll
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskGetSchedulerState
<LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskSuspendAll
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
</UL>
<BR>[Called By]<UL><LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTimerGenericCommand
</UL>

<P><STRONG><a name="[16d]"></a>xQueueGenericSendFromISR</STRONG> (Thumb, 192 bytes, Stack size 32 bytes, queue.o(i.xQueueGenericSendFromISR))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = xQueueGenericSendFromISR &rArr; prvCopyDataToQueue &rArr; xTaskPriorityDisinherit
</UL>
<BR>[Calls]<UL><LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskRemoveFromEventList
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortValidateInterruptPriority
<LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvCopyDataToQueue
</UL>
<BR>[Called By]<UL><LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTimerGenericCommand
</UL>

<P><STRONG><a name="[14c]"></a>xQueueReceive</STRONG> (Thumb, 310 bytes, Stack size 56 bytes, queue.o(i.xQueueReceive))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = xQueueReceive &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskRemoveFromEventList
<LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCheckForTimeOut
<LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskPlaceOnEventList
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskInternalSetTimeOutState
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvUnlockQueue
<LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvIsQueueEmpty
<LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvCopyDataFromQueue
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskResumeAll
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskGetSchedulerState
<LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskSuspendAll
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
</UL>
<BR>[Called By]<UL><LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessReceivedCommands
</UL>

<P><STRONG><a name="[16c]"></a>xTaskCheckForTimeOut</STRONG> (Thumb, 118 bytes, Stack size 24 bytes, tasks.o(i.xTaskCheckForTimeOut))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = xTaskCheckForTimeOut
</UL>
<BR>[Calls]<UL><LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskInternalSetTimeOutState
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
</UL>
<BR>[Called By]<UL><LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceive
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSend
</UL>

<P><STRONG><a name="[12d]"></a>xTaskCreate</STRONG> (Thumb, 100 bytes, Stack size 56 bytes, tasks.o(i.xTaskCreate))
<BR><BR>[Stack]<UL><LI>Max Depth = 128 + Unknown Stack Size
<LI>Call Chain = xTaskCreate &rArr; pvPortMalloc &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortFree
<LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pvPortMalloc
<LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvInitialiseNewTask
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvAddNewTaskToReadyList
</UL>
<BR>[Called By]<UL><LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;osThreadNew
</UL>

<P><STRONG><a name="[12c]"></a>xTaskCreateStatic</STRONG> (Thumb, 92 bytes, Stack size 40 bytes, tasks.o(i.xTaskCreateStatic))
<BR><BR>[Stack]<UL><LI>Max Depth = 80 + Unknown Stack Size
<LI>Call Chain = xTaskCreateStatic &rArr; prvInitialiseNewTask
</UL>
<BR>[Calls]<UL><LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvInitialiseNewTask
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvAddNewTaskToReadyList
</UL>
<BR>[Called By]<UL><LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;osThreadNew
<LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTimerCreateTimerTask
<LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskStartScheduler
</UL>

<P><STRONG><a name="[10a]"></a>xTaskGetSchedulerState</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, tasks.o(i.xTaskGetSchedulerState))
<BR><BR>[Called By]<UL><LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceive
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSend
<LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTimerGenericCommand
<LI><a href="#[16]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Handler
</UL>

<P><STRONG><a name="[151]"></a>xTaskGetTickCount</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, tasks.o(i.xTaskGetTickCount))
<BR><BR>[Called By]<UL><LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvSampleTimeNow
</UL>

<P><STRONG><a name="[169]"></a>xTaskIncrementTick</STRONG> (Thumb, 196 bytes, Stack size 24 bytes, tasks.o(i.xTaskIncrementTick))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInsertEnd
<LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxListRemove
<LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvResetNextTaskUnblockTime
</UL>
<BR>[Called By]<UL><LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskResumeAll
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xPortSysTickHandler
</UL>

<P><STRONG><a name="[13e]"></a>xTaskPriorityDisinherit</STRONG> (Thumb, 116 bytes, Stack size 16 bytes, tasks.o(i.xTaskPriorityDisinherit))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = xTaskPriorityDisinherit
</UL>
<BR>[Calls]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInsertEnd
<LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxListRemove
</UL>
<BR>[Called By]<UL><LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvCopyDataToQueue
</UL>

<P><STRONG><a name="[155]"></a>xTaskRemoveFromEventList</STRONG> (Thumb, 106 bytes, Stack size 16 bytes, tasks.o(i.xTaskRemoveFromEventList))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = xTaskRemoveFromEventList
</UL>
<BR>[Calls]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInsertEnd
<LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxListRemove
</UL>
<BR>[Called By]<UL><LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceive
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSendFromISR
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSend
<LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericReset
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvUnlockQueue
</UL>

<P><STRONG><a name="[14f]"></a>xTaskResumeAll</STRONG> (Thumb, 184 bytes, Stack size 24 bytes, tasks.o(i.xTaskResumeAll))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInsertEnd
<LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxListRemove
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
<LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskIncrementTick
<LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvResetNextTaskUnblockTime
</UL>
<BR>[Called By]<UL><LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceive
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSend
<LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortFree
<LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pvPortMalloc
<LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessTimerOrBlockTask
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskDelay
</UL>

<P><STRONG><a name="[162]"></a>xTimerCreateTimerTask</STRONG> (Thumb, 86 bytes, Stack size 40 bytes, timers.o(i.xTimerCreateTimerTask))
<BR><BR>[Stack]<UL><LI>Max Depth = 120 + Unknown Stack Size
<LI>Call Chain = xTimerCreateTimerTask &rArr; prvCheckForValidListAndQueue &rArr; xQueueGenericCreateStatic &rArr; prvInitialiseNewQueue &rArr; xQueueGenericReset &rArr; xTaskRemoveFromEventList
</UL>
<BR>[Calls]<UL><LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vApplicationGetTimerTaskMemory
<LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvCheckForValidListAndQueue
<LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCreateStatic
</UL>
<BR>[Called By]<UL><LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskStartScheduler
</UL>

<P><STRONG><a name="[149]"></a>xTimerGenericCommand</STRONG> (Thumb, 104 bytes, Stack size 40 bytes, timers.o(i.xTimerGenericCommand))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = xTimerGenericCommand &rArr; xQueueGenericSend &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSendFromISR
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSend
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskGetSchedulerState
</UL>
<BR>[Called By]<UL><LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvSwitchTimerLists
<LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessReceivedCommands
<LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessExpiredTimer
</UL>

<P><STRONG><a name="[88]"></a>_fp_init</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, fpinit.o(x$fpl$fpinit))
<BR><BR>[Called By]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_init_fp_1
</UL>

<P><STRONG><a name="[1a4]"></a>__fplib_config_fpu_vfp</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, fpinit.o(x$fpl$fpinit), UNUSED)

<P><STRONG><a name="[1a5]"></a>__fplib_config_pureend_doubles</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, fpinit.o(x$fpl$fpinit), UNUSED)
<P>
<H3>
Local Symbols
</H3>
<P><STRONG><a name="[cd]"></a>RCC_SetFlashLatencyFromMSIRange</STRONG> (Thumb, 122 bytes, Stack size 24 bytes, stm32l4xx_hal_rcc.o(i.RCC_SetFlashLatencyFromMSIRange))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = RCC_SetFlashLatencyFromMSIRange
</UL>
<BR>[Calls]<UL><LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PWREx_GetVoltageRange
</UL>
<BR>[Called By]<UL><LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_OscConfig
</UL>

<P><STRONG><a name="[c6]"></a>RCCEx_PLLSAI1_Config</STRONG> (Thumb, 304 bytes, Stack size 24 bytes, stm32l4xx_hal_rcc_ex.o(i.RCCEx_PLLSAI1_Config))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = RCCEx_PLLSAI1_Config
</UL>
<BR>[Calls]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCCEx_PeriphCLKConfig
</UL>

<P><STRONG><a name="[c7]"></a>RCCEx_PLLSAI2_Config</STRONG> (Thumb, 270 bytes, Stack size 24 bytes, stm32l4xx_hal_rcc_ex.o(i.RCCEx_PLLSAI2_Config))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = RCCEx_PLLSAI2_Config
</UL>
<BR>[Calls]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCCEx_PeriphCLKConfig
</UL>

<P><STRONG><a name="[c5]"></a>__NVIC_SetPriority</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, stm32l4xx_hal_cortex.o(i.__NVIC_SetPriority))
<BR><BR>[Called By]<UL><LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
</UL>

<P><STRONG><a name="[d2]"></a>SPI_EndRxTransaction</STRONG> (Thumb, 126 bytes, Stack size 32 bytes, stm32l4xx_hal_spi.o(i.SPI_EndRxTransaction))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = SPI_EndRxTransaction &rArr; SPI_WaitFifoStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_WaitFlagStateUntilTimeout
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_WaitFifoStateUntilTimeout
</UL>
<BR>[Called By]<UL><LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_Receive
</UL>

<P><STRONG><a name="[d4]"></a>SPI_EndRxTxTransaction</STRONG> (Thumb, 100 bytes, Stack size 24 bytes, stm32l4xx_hal_spi.o(i.SPI_EndRxTxTransaction))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = SPI_EndRxTxTransaction &rArr; SPI_WaitFifoStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_WaitFlagStateUntilTimeout
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_WaitFifoStateUntilTimeout
</UL>
<BR>[Called By]<UL><LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_TransmitReceive
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_Transmit
</UL>

<P><STRONG><a name="[109]"></a>SPI_WaitFifoStateUntilTimeout</STRONG> (Thumb, 218 bytes, Stack size 48 bytes, stm32l4xx_hal_spi.o(i.SPI_WaitFifoStateUntilTimeout))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = SPI_WaitFifoStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_EndRxTxTransaction
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_EndRxTransaction
</UL>

<P><STRONG><a name="[108]"></a>SPI_WaitFlagStateUntilTimeout</STRONG> (Thumb, 184 bytes, Stack size 32 bytes, stm32l4xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_EndRxTxTransaction
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_EndRxTransaction
</UL>

<P><STRONG><a name="[73]"></a>UART_DMAAbortOnError</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, stm32l4xx_hal_uart.o(i.UART_DMAAbortOnError))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = UART_DMAAbortOnError
</UL>
<BR>[Calls]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_ErrorCallback
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32l4xx_hal_uart.o(i.HAL_UART_IRQHandler)
</UL>
<P><STRONG><a name="[e2]"></a>UART_EndRxTransfer</STRONG> (Thumb, 74 bytes, Stack size 0 bytes, stm32l4xx_hal_uart.o(i.UART_EndRxTransfer))
<BR><BR>[Calls]<UL><LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_EndRxTransfer
</UL>
<BR>[Called By]<UL><LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_EndRxTransfer
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
</UL>

<P><STRONG><a name="[e8]"></a>UART_EndTransmit_IT</STRONG> (Thumb, 34 bytes, Stack size 8 bytes, stm32l4xx_hal_uart.o(i.UART_EndTransmit_IT))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = UART_EndTransmit_IT &rArr; HAL_UART_TxCpltCallback &rArr; RS485_Enter_RX_Mode
</UL>
<BR>[Calls]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_TxCpltCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
</UL>

<P><STRONG><a name="[13c]"></a>prvCopyDataFromQueue</STRONG> (Thumb, 40 bytes, Stack size 8 bytes, queue.o(i.prvCopyDataFromQueue))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = prvCopyDataFromQueue
</UL>
<BR>[Calls]<UL><LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceive
</UL>

<P><STRONG><a name="[13d]"></a>prvCopyDataToQueue</STRONG> (Thumb, 110 bytes, Stack size 24 bytes, queue.o(i.prvCopyDataToQueue))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = prvCopyDataToQueue &rArr; xTaskPriorityDisinherit
</UL>
<BR>[Calls]<UL><LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskPriorityDisinherit
<LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSendFromISR
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSend
</UL>

<P><STRONG><a name="[140]"></a>prvInitialiseNewQueue</STRONG> (Thumb, 34 bytes, Stack size 16 bytes, queue.o(i.prvInitialiseNewQueue))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = prvInitialiseNewQueue &rArr; xQueueGenericReset &rArr; xTaskRemoveFromEventList
</UL>
<BR>[Calls]<UL><LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericReset
</UL>
<BR>[Called By]<UL><LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericCreateStatic
</UL>

<P><STRONG><a name="[146]"></a>prvIsQueueEmpty</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, queue.o(i.prvIsQueueEmpty))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = prvIsQueueEmpty
</UL>
<BR>[Calls]<UL><LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
</UL>
<BR>[Called By]<UL><LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceive
</UL>

<P><STRONG><a name="[147]"></a>prvIsQueueFull</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, queue.o(i.prvIsQueueFull))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = prvIsQueueFull
</UL>
<BR>[Calls]<UL><LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
</UL>
<BR>[Called By]<UL><LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSend
</UL>

<P><STRONG><a name="[154]"></a>prvUnlockQueue</STRONG> (Thumb, 106 bytes, Stack size 24 bytes, queue.o(i.prvUnlockQueue))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = prvUnlockQueue &rArr; xTaskRemoveFromEventList
</UL>
<BR>[Calls]<UL><LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskRemoveFromEventList
<LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskMissedYield
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
</UL>
<BR>[Called By]<UL><LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceive
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSend
<LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vQueueWaitForMessageRestricted
</UL>

<P><STRONG><a name="[12e]"></a>prvAddCurrentTaskToDelayedList</STRONG> (Thumb, 86 bytes, Stack size 24 bytes, tasks.o(i.prvAddCurrentTaskToDelayedList))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = prvAddCurrentTaskToDelayedList &rArr; vListInsert
</UL>
<BR>[Calls]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInsertEnd
<LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInsert
<LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxListRemove
</UL>
<BR>[Called By]<UL><LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskPlaceOnEventListRestricted
<LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskPlaceOnEventList
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskDelay
</UL>

<P><STRONG><a name="[132]"></a>prvAddNewTaskToReadyList</STRONG> (Thumb, 124 bytes, Stack size 16 bytes, tasks.o(i.prvAddNewTaskToReadyList))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = prvAddNewTaskToReadyList &rArr; prvInitialiseTaskLists
</UL>
<BR>[Calls]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInsertEnd
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
<LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvInitialiseTaskLists
</UL>
<BR>[Called By]<UL><LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCreateStatic
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCreate
</UL>

<P><STRONG><a name="[13a]"></a>prvCheckTasksWaitingTermination</STRONG> (Thumb, 52 bytes, Stack size 16 bytes, tasks.o(i.prvCheckTasksWaitingTermination))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = prvCheckTasksWaitingTermination &rArr; prvDeleteTCB &rArr; vPortFree &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxListRemove
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
<LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvDeleteTCB
</UL>
<BR>[Called By]<UL><LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvIdleTask
</UL>

<P><STRONG><a name="[13b]"></a>prvDeleteTCB</STRONG> (Thumb, 64 bytes, Stack size 8 bytes, tasks.o(i.prvDeleteTCB))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = prvDeleteTCB &rArr; vPortFree &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortFree
</UL>
<BR>[Called By]<UL><LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvCheckTasksWaitingTermination
</UL>

<P><STRONG><a name="[81]"></a>prvIdleTask</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, tasks.o(i.prvIdleTask))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = prvIdleTask &rArr; prvCheckTasksWaitingTermination &rArr; prvDeleteTCB &rArr; vPortFree &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvCheckTasksWaitingTermination
</UL>
<BR>[Address Reference Count : 1]<UL><LI> tasks.o(i.vTaskStartScheduler)
</UL>
<P><STRONG><a name="[142]"></a>prvInitialiseNewTask</STRONG> (Thumb, 168 bytes, Stack size 40 bytes, tasks.o(i.prvInitialiseNewTask))
<BR><BR>[Stack]<UL><LI>Max Depth = 40 + Unknown Stack Size
<LI>Call Chain = prvInitialiseNewTask
</UL>
<BR>[Calls]<UL><LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInitialiseItem
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pxPortInitialiseStack
<LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
</UL>
<BR>[Called By]<UL><LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCreateStatic
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCreate
</UL>

<P><STRONG><a name="[134]"></a>prvInitialiseTaskLists</STRONG> (Thumb, 74 bytes, Stack size 16 bytes, tasks.o(i.prvInitialiseTaskLists))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = prvInitialiseTaskLists
</UL>
<BR>[Calls]<UL><LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInitialise
</UL>
<BR>[Called By]<UL><LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvAddNewTaskToReadyList
</UL>

<P><STRONG><a name="[16e]"></a>prvResetNextTaskUnblockTime</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, tasks.o(i.prvResetNextTaskUnblockTime))
<BR><BR>[Called By]<UL><LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskResumeAll
<LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskIncrementTick
</UL>

<P><STRONG><a name="[136]"></a>prvCheckForValidListAndQueue</STRONG> (Thumb, 72 bytes, Stack size 16 bytes, timers.o(i.prvCheckForValidListAndQueue))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = prvCheckForValidListAndQueue &rArr; xQueueGenericCreateStatic &rArr; prvInitialiseNewQueue &rArr; xQueueGenericReset &rArr; xTaskRemoveFromEventList
</UL>
<BR>[Calls]<UL><LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericCreateStatic
<LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vQueueAddToRegistry
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
<LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInitialise
</UL>
<BR>[Called By]<UL><LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTimerCreateTimerTask
</UL>

<P><STRONG><a name="[153]"></a>prvGetNextExpireTime</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, timers.o(i.prvGetNextExpireTime))
<BR><BR>[Called By]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTimerTask
</UL>

<P><STRONG><a name="[145]"></a>prvInsertTimerInActiveList</STRONG> (Thumb, 60 bytes, Stack size 16 bytes, timers.o(i.prvInsertTimerInActiveList))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = prvInsertTimerInActiveList &rArr; vListInsert
</UL>
<BR>[Calls]<UL><LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInsert
</UL>
<BR>[Called By]<UL><LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessReceivedCommands
<LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessExpiredTimer
</UL>

<P><STRONG><a name="[148]"></a>prvProcessExpiredTimer</STRONG> (Thumb, 98 bytes, Stack size 24 bytes, timers.o(i.prvProcessExpiredTimer))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = prvProcessExpiredTimer &rArr; xTimerGenericCommand &rArr; xQueueGenericSend &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxListRemove
<LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTimerGenericCommand
<LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvInsertTimerInActiveList
</UL>
<BR>[Called By]<UL><LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessTimerOrBlockTask
</UL>

<P><STRONG><a name="[14a]"></a>prvProcessReceivedCommands</STRONG> (Thumb, 248 bytes, Stack size 40 bytes, timers.o(i.prvProcessReceivedCommands))
<BR><BR>[Stack]<UL><LI>Max Depth = 224<LI>Call Chain = prvProcessReceivedCommands &rArr; prvSampleTimeNow &rArr; prvSwitchTimerLists &rArr; xTimerGenericCommand &rArr; xQueueGenericSend &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceive
<LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxListRemove
<LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortFree
<LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTimerGenericCommand
<LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvSampleTimeNow
<LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvInsertTimerInActiveList
</UL>
<BR>[Called By]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTimerTask
</UL>

<P><STRONG><a name="[14d]"></a>prvProcessTimerOrBlockTask</STRONG> (Thumb, 100 bytes, Stack size 24 bytes, timers.o(i.prvProcessTimerOrBlockTask))
<BR><BR>[Stack]<UL><LI>Max Depth = 208<LI>Call Chain = prvProcessTimerOrBlockTask &rArr; prvSampleTimeNow &rArr; prvSwitchTimerLists &rArr; xTimerGenericCommand &rArr; xQueueGenericSend &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vQueueWaitForMessageRestricted
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskResumeAll
<LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskSuspendAll
<LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvSampleTimeNow
<LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessExpiredTimer
</UL>
<BR>[Called By]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTimerTask
</UL>

<P><STRONG><a name="[14b]"></a>prvSampleTimeNow</STRONG> (Thumb, 38 bytes, Stack size 16 bytes, timers.o(i.prvSampleTimeNow))
<BR><BR>[Stack]<UL><LI>Max Depth = 184<LI>Call Chain = prvSampleTimeNow &rArr; prvSwitchTimerLists &rArr; xTimerGenericCommand &rArr; xQueueGenericSend &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvSwitchTimerLists
<LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskGetTickCount
</UL>
<BR>[Called By]<UL><LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessTimerOrBlockTask
<LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessReceivedCommands
</UL>

<P><STRONG><a name="[152]"></a>prvSwitchTimerLists</STRONG> (Thumb, 106 bytes, Stack size 24 bytes, timers.o(i.prvSwitchTimerLists))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = prvSwitchTimerLists &rArr; xTimerGenericCommand &rArr; xQueueGenericSend &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInsert
<LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxListRemove
<LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTimerGenericCommand
</UL>
<BR>[Called By]<UL><LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvSampleTimeNow
</UL>

<P><STRONG><a name="[82]"></a>prvTimerTask</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, timers.o(i.prvTimerTask))
<BR><BR>[Stack]<UL><LI>Max Depth = 232<LI>Call Chain = prvTimerTask &rArr; prvProcessReceivedCommands &rArr; prvSampleTimeNow &rArr; prvSwitchTimerLists &rArr; xTimerGenericCommand &rArr; xQueueGenericSend &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessTimerOrBlockTask
<LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessReceivedCommands
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvGetNextExpireTime
</UL>
<BR>[Address Reference Count : 1]<UL><LI> timers.o(i.xTimerCreateTimerTask)
</UL>
<P><STRONG><a name="[15b]"></a>prvHeapInit</STRONG> (Thumb, 68 bytes, Stack size 0 bytes, heap_4.o(i.prvHeapInit))
<BR><BR>[Called By]<UL><LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pvPortMalloc
</UL>

<P><STRONG><a name="[15c]"></a>prvInsertBlockIntoFreeList</STRONG> (Thumb, 78 bytes, Stack size 12 bytes, heap_4.o(i.prvInsertBlockIntoFreeList))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = prvInsertBlockIntoFreeList
</UL>
<BR>[Called By]<UL><LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortFree
<LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pvPortMalloc
</UL>

<P><STRONG><a name="[80]"></a>prvTaskExitError</STRONG> (Thumb, 40 bytes, Stack size 0 bytes, port.o(i.prvTaskExitError))
<BR>[Address Reference Count : 1]<UL><LI> port.o(i.pxPortInitialiseStack)
</UL><P>
<H3>
Undefined Global Symbols
</H3><HR></body></html>
