/*
 * FreeModbus Libary: BARE Port
 * Copyright (C) 2006 <PERSON> <<EMAIL>>
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA  02110-1301  USA
 *
 * File: $Id$
 */

/* ----------------------- Platform includes --------------------------------*/
#include "tim.h"
#include "port.h"

/* ----------------------- Modbus includes ----------------------------------*/
#include "mb.h"
#include "mbport.h"

/* ----------------------- Test includes ------------------------------------*/
#include "modbus_test.h"

/* ----------------------- static functions ---------------------------------*/
void prvvTIMERExpiredISR( void );

/* ----------------------- Start implementation -----------------------------*/
BOOL
xMBPortTimersInit( USHORT usTim1Timerout50us )
{
    // 计算定时器的重载值
    // usTim1Timerout50us 是以50us为单位的超时时间
    // 根据tim.c配置：Prescaler = 79 (80-1), Period = 999 (1000-1)
    // 系统时钟80MHz，预分频后为1MHz，每个tick = 1us
    // 所以需要乘以50转换为微秒
    uint32_t timer_period = usTim1Timerout50us * 50; // 转换为微秒

    // 调试：记录定时器配置
    static uint32_t debug_timer_init_count = 0;
    debug_timer_init_count++;

    // 停止定时器
    __HAL_TIM_DISABLE(&htim16);

    // 设置定时器周期
    // 由于定时器频率是1MHz，timer_period直接就是微秒数
    // 但要确保不超过最大值65535
    if (timer_period > 65535) {
        timer_period = 65535;
    }
    __HAL_TIM_SET_AUTORELOAD(&htim16, timer_period - 1);
    
    // 清除计数器
    __HAL_TIM_SET_COUNTER(&htim16, 0);
    
    // 清除中断标志
    __HAL_TIM_CLEAR_IT(&htim16, TIM_IT_UPDATE);
    
    return TRUE;
}


inline void
vMBPortTimersEnable(  )
{
    /* Enable the timer with the timeout passed to xMBPortTimersInit( ) */
	__HAL_TIM_CLEAR_IT(&htim16,TIM_IT_UPDATE);
	__HAL_TIM_ENABLE_IT(&htim16,TIM_IT_UPDATE);
	__HAL_TIM_SET_COUNTER(&htim16,0);
	__HAL_TIM_ENABLE(&htim16);

}

inline void
vMBPortTimersDisable(  )
{
    /* Disable any pending timers. */
	__HAL_TIM_DISABLE(&htim16);
	__HAL_TIM_SET_COUNTER(&htim16,0);
	__HAL_TIM_DISABLE_IT(&htim16,TIM_IT_UPDATE);
	__HAL_TIM_CLEAR_IT(&htim16,TIM_IT_UPDATE);

}

/* Create an ISR which is called whenever the timer has expired. This function
 * must then call pxMBPortCBTimerExpired( ) to notify the protocol stack that
 * the timer has expired.
 */
void prvvTIMERExpiredISR( void )
{
    // 添加调试计数
    Modbus_Test_Inc_Timer_Count();

    // 添加调试指示
    static uint32_t timer_count = 0;
    timer_count++;

    // 每100次定时器中断闪烁一次LED (用于调试)
    if (timer_count % 100 == 0)
    {
        HAL_GPIO_TogglePin(LED_GPIO_Port, LED_Pin); // 取消注释用于调试
    }

    ( void )pxMBPortCBTimerExpired(  );
}

