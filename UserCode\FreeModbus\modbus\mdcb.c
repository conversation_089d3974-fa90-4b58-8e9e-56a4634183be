#include "mb.h"
#include "modbus_test.h"
#include "stm32l4xx_hal.h"

// ??????????????
#define REG_INPUT_START 0x0000
// ????????????
#define REG_INPUT_NREGS 8
// ??????????????
#define REG_HOLDING_START 0x0000
// ????????????
#define REG_HOLDING_NREGS 64

// ?????????
#define REG_COILS_START 0x0000
// ???????
#define REG_COILS_SIZE 16

// ??????????????
#define REG_DISCRETE_START 0x0000
// ????????????
#define REG_DISCRETE_SIZE 16

/* Private variables ---------------------------------------------------------*/
// ????????????
uint16_t usRegInputBuf[REG_INPUT_NREGS] = {0x1234, 0x5678, 0x9abc, 0xdef0, 0x147b, 0x3f8e, 0x147b, 0x3f8e};
// ??????????????
uint16_t usRegInputStart = REG_INPUT_START;

// ????????????
uint16_t usRegHoldingBuf[REG_HOLDING_NREGS] = {0xAABB, 0xCCDD, 0xEEFF, 0x0011, 0x2233, 0x4455, 0x6677, 0x8899};
// ??????????????
uint16_t usRegHoldingStart = REG_HOLDING_START;

// ?????
uint8_t ucRegCoilsBuf[REG_COILS_SIZE / 8] = {0x01, 0x02};
// ??????????
uint8_t ucRegDiscreteBuf[REG_DISCRETE_SIZE / 8] = {0x01, 0x02};

/**
 * @brief ???????????????????????? 04 eMBFuncReadInputRegister.
 * @note ???????????? ??????:
 * SlaveAddr(1 Byte)+FuncCode(1 Byte)
 * +StartAddrHiByte(1 Byte)+StartAddrLoByte(1 Byte)
 * +LenAddrHiByte(1 Byte)+LenAddrLoByte(1 Byte)+
 * +CRCAddrHiByte(1 Byte)+CRCAddrLoByte(1 Byte)
 * @param pucRegBuffer ??????????????????????
 * @param usAddress ????????
 * @param usNRegs ??????????????
 * @return eMBErrorCode
 */
eMBErrorCode
eMBRegInputCB(UCHAR *pucRegBuffer, USHORT usAddress, USHORT usNRegs)
{
    eMBErrorCode eStatus = MB_ENOERR;
    int iRegIndex;

    if ((usAddress >= REG_INPUT_START) && (usAddress + usNRegs <= REG_INPUT_START + REG_INPUT_NREGS))
    {
        iRegIndex = (int)(usAddress - usRegInputStart);
        while (usNRegs > 0)
        {
            *pucRegBuffer++ = (UCHAR)(usRegInputBuf[iRegIndex] >> 8);
            *pucRegBuffer++ = (UCHAR)(usRegInputBuf[iRegIndex] & 0xFF);
            iRegIndex++;
            usNRegs--;
        }
    }
    else
    {
        eStatus = MB_ENOREG;
    }
    return eStatus;
}

/**
 * @brief ?????????????
 * 06 ?????????? eMBFuncWriteHoldingRegister
 * 16 ????????????? eMBFuncWriteMultipleHoldingRegister
 * 03 ?????????? eMBFuncReadHoldingRegister
 * 23 ??????????????? eMBFuncReadWriteMultipleHoldingRegister
 *
 * @param pucRegBuffer ??????????????????????
 * @param usAddress ????????
 * @param usNRegs ???????????????
 * @param eMode ??????
 * @return eMBErrorCode
 */
eMBErrorCode
eMBRegHoldingCB(UCHAR *pucRegBuffer, USHORT usAddress, USHORT usNRegs, eMBRegisterMode eMode)
{
    eMBErrorCode eStatus = MB_ENOERR;
    int iRegIndex = 0;

    // 添加回调函数调用计数
    Modbus_Test_Inc_Callback_Count();
	//???????????
    if ((usAddress >= REG_HOLDING_START) &&
        ((usAddress + usNRegs) <= (REG_HOLDING_START + REG_HOLDING_NREGS)))
    {
        iRegIndex = (int)(usAddress - usRegHoldingStart);
		//????????????????
        switch (eMode)
        {
        case MB_REG_READ: // ?????? MB_REG_READ = 0
            // ???????????????
            uint16_t* test_regs = Modbus_Test_Get_Holding_Regs();
            while (usNRegs > 0)
            {
                uint16_t reg_value;
                // ??????????????????0-9???????????????
                if (iRegIndex < 10) {
                    reg_value = test_regs[iRegIndex];
                } else {
                    reg_value = usRegHoldingBuf[iRegIndex];
                }
                *pucRegBuffer++ = (uint8_t)(reg_value >> 8);
                *pucRegBuffer++ = (uint8_t)(reg_value & 0xFF);
                iRegIndex++;
                usNRegs--;
            }
            break;
        case MB_REG_WRITE: // ?????? MB_REG_WRITE = 1
            while (usNRegs > 0)
            {
                usRegHoldingBuf[iRegIndex] = *pucRegBuffer++ << 8;  //??8?????
                usRegHoldingBuf[iRegIndex] |= *pucRegBuffer++;  //??8?????
                iRegIndex++;
                usNRegs--;
            }
        }
    }
    else  //??????
    {
        eStatus = MB_ENOREG;
    }
    return eStatus;
}

extern void xMBUtilSetBits(UCHAR *ucByteBuf, USHORT usBitOffset, UCHAR ucNBits,
                           UCHAR ucValue);
extern UCHAR xMBUtilGetBits(UCHAR *ucByteBuf, USHORT usBitOffset, UCHAR ucNBits);
/**
 * @brief ?????????????01 ????? eMBFuncReadCoils
 * 05 ????? eMBFuncWriteCoil
 * 15 ???????? eMBFuncWriteMultipleCoils
 *
 * @note ??????
 *
 * @param pucRegBuffer ??????????????????????
 * @param usAddress ??????
 * @param usNCoils ??????????????
 * @param eMode ??????
 * @return eMBErrorCode
 */
eMBErrorCode
eMBRegCoilsCB(UCHAR *pucRegBuffer, USHORT usAddress, USHORT usNCoils,
              eMBRegisterMode eMode)
{
    // ??????
    eMBErrorCode eStatus = MB_ENOERR;
    // ?????????
    int16_t iNCoils = (int16_t)usNCoils;
    // ??????????
    int16_t usBitOffset;

    // ?????????????????????
    if (((int16_t)usAddress >= REG_COILS_START) &&
        (usAddress + usNCoils <= REG_COILS_START + REG_COILS_SIZE))
    {
        // ?????????????
        usBitOffset = (int16_t)(usAddress - REG_COILS_START);
        switch (eMode)
        {
            // ??????
        case MB_REG_READ:
            while (iNCoils > 0)
            {
                *pucRegBuffer++ = xMBUtilGetBits(ucRegCoilsBuf, usBitOffset,
                                                 (uint8_t)(iNCoils > 8 ? 8 : iNCoils));
                iNCoils -= 8;
                usBitOffset += 8;
            }
            break;

            // ??????
        case MB_REG_WRITE:
            while (iNCoils > 0)
            {
                xMBUtilSetBits(ucRegCoilsBuf, usBitOffset,
                               (uint8_t)(iNCoils > 8 ? 8 : iNCoils),
                               *pucRegBuffer++);
                iNCoils -= 8;
            }
            break;
        }
    }
    else
    {
        eStatus = MB_ENOREG;
    }
    return eStatus;
}

/**
 * @brief ??????????????????????????
 *  02 ?????????? eMBFuncReadDiscreteInputs
 *
 * @param pucRegBuffer ??????????????????????
 *
 * @param usAddress ????????
 * @param usNDiscrete ??????????????
 * @return eMBErrorCode
 */
eMBErrorCode
eMBRegDiscreteCB(UCHAR *pucRegBuffer, USHORT usAddress, USHORT usNDiscrete)
{
    // ??????
    eMBErrorCode eStatus = MB_ENOERR;
    // ?????????????
    int16_t iNDiscrete = (int16_t)usNDiscrete;
    // ?????
    uint16_t usBitOffset;

    // ??????????????????????
    if (((int16_t)usAddress >= REG_DISCRETE_START) &&
        (usAddress + usNDiscrete <= REG_DISCRETE_START + REG_DISCRETE_SIZE))
    {
        // ????????
        usBitOffset = (uint16_t)(usAddress - REG_DISCRETE_START);

        while (iNDiscrete > 0)
        {
            *pucRegBuffer++ = xMBUtilGetBits(ucRegDiscreteBuf, usBitOffset,
                                             (uint8_t)(iNDiscrete > 8 ? 8 : iNDiscrete));
            iNDiscrete -= 8;
            usBitOffset += 8;
        }
    }
    else
    {
        eStatus = MB_ENOREG;
    }
    return eStatus;
}

