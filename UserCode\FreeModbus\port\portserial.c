/*
 * FreeModbus Libary: BARE Port
 * Copyright (C) 2006 <PERSON> <<EMAIL>>
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA  02110-1301  USA
 *
 * File: $Id$
 */

#include "usart.h"
#include "rs485.h"
#include "port.h"

/* ----------------------- Modbus includes ----------------------------------*/
#include "mb.h"
#include "mbport.h"

/* ----------------------- Test includes ------------------------------------*/
#include "modbus_test.h"

/* ----------------------- HAL includes -------------------------------------*/
#include "stm32l4xx_hal.h"

#define UART_BAUD_RATE          19200

extern UART_HandleTypeDef huart1; 
uint8_t ucRxByte;                  // 全局接收缓冲区
static BOOL xTxEnabled = FALSE;  // 添加发送状态标志

/* ----------------------- static functions ---------------------------------*/
void prvvUARTTxReadyISR( void );
void prvvUARTRxISR( void );

/* ----------------------- Start implementation -----------------------------*/
void
vMBPortSerialEnable( BOOL xRxEnable, BOOL xTxEnable )
{
    /* If xRXEnable enable serial receive interrupts. If xTxENable enable
     * transmitter empty interrupts.
     */
	if (xRxEnable)
	{
		RS485_Enter_RX_Mode();  //设置 RS485 接收
		__HAL_UART_ENABLE_IT(&huart1, UART_IT_RXNE); //使能接收中断
		// 注意：不需要调用HAL_UART_Receive_IT，因为我们在中断中直接读取数据
	}
	else
	{
		__HAL_UART_DISABLE_IT(&huart1, UART_IT_RXNE); // 失能接收中断
	}
	if (xTxEnable)
	{
        RS485_Enter_TX_Mode();  // 设置 RS485 发送模式
        xTxEnabled = TRUE;
		__HAL_UART_ENABLE_IT(&huart1, UART_IT_TXE); //使能发送中断
	}
	else
	{
		__HAL_UART_DISABLE_IT(&huart1, UART_IT_TXE); //失能发送中断
        xTxEnabled = FALSE;

        // 关键修复：发送完成后切换到接收模式
        // 使用TC中断来确保最后一个字节真正发送完成
        if (!xRxEnable) {
            __HAL_UART_ENABLE_IT(&huart1, UART_IT_TC);  // 使能传输完成中断
        }
	}

}

BOOL
xMBPortSerialInit( UCHAR ucPORT, ULONG ulBaudRate, UCHAR ucDataBits, eMBParity eParity, UCHAR ucStopBits )
{
    // 重新配置UART参数以匹配FreeModbus要求
    HAL_UART_DeInit(&huart1);

    // 重新设置参数
    huart1.Instance = USART1;
    huart1.Init.BaudRate = ulBaudRate;
    huart1.Init.WordLength = (ucDataBits == 8) ? UART_WORDLENGTH_8B : UART_WORDLENGTH_9B;
    huart1.Init.StopBits = (ucStopBits == 1) ? UART_STOPBITS_1 : UART_STOPBITS_2;

    // 设置奇偶校验
    switch (eParity) {
        case MB_PAR_NONE:
            huart1.Init.Parity = UART_PARITY_NONE;
            break;
        case MB_PAR_ODD:
            huart1.Init.Parity = UART_PARITY_ODD;
            break;
        case MB_PAR_EVEN:
            huart1.Init.Parity = UART_PARITY_EVEN;
            break;
        default:
            huart1.Init.Parity = UART_PARITY_EVEN;
            break;
    }

    huart1.Init.Mode = UART_MODE_TX_RX;
    huart1.Init.HwFlowCtl = UART_HWCONTROL_NONE;
    huart1.Init.OverSampling = UART_OVERSAMPLING_16;
    huart1.Init.OneBitSampling = UART_ONE_BIT_SAMPLE_DISABLE;
    huart1.AdvancedInit.AdvFeatureInit = UART_ADVFEATURE_NO_INIT;

    // 重新初始化UART
    if (HAL_UART_Init(&huart1) != HAL_OK) {
        return FALSE;
    }

    return TRUE;
}

// 调试：记录发送的数据
static uint8_t debug_tx_buffer[20];
static uint8_t debug_tx_count = 0;

// 调试函数：重置发送数据记录
void Debug_Reset_TX_Buffer(void)
{
    debug_tx_count = 0;
    memset(debug_tx_buffer, 0, sizeof(debug_tx_buffer));
}

// 调试函数：获取发送的数据
uint8_t Debug_Get_TX_Count(void)
{
    return debug_tx_count;
}

uint8_t* Debug_Get_TX_Buffer(void)
{
    return debug_tx_buffer;
}

BOOL
xMBPortSerialPutByte( CHAR ucByte )
{
    /* Put a byte in the UARTs transmit buffer. This function is called
     * by the protocol stack if pxMBFrameCBTransmitterEmpty( ) has been
     * called. */
    // 直接发送数据，不做任何修改
    uint8_t byteToSend = (uint8_t)ucByte;

    // 调试：记录发送的数据
    if (debug_tx_count < sizeof(debug_tx_buffer)) {
        debug_tx_buffer[debug_tx_count++] = byteToSend;
    }

    // 调试：每发送一个字节闪烁LED
    static uint8_t send_count = 0;
    if (++send_count >= 3) {  // 每3个字节闪烁一次
        send_count = 0;
        HAL_GPIO_TogglePin(GPIOC, GPIO_PIN_13);
    }

    // 直接写入发送寄存器，不使用HAL_UART_Transmit_IT
    huart1.Instance->TDR = byteToSend;
    return TRUE;
}

BOOL
xMBPortSerialGetByte( CHAR * pucByte )
{
    /* Return the byte in the UARTs receive buffer. This function is called
     * by the protocol stack after pxMBFrameCBByteReceived( ) has been called.
     */
    *pucByte = ucRxByte;
    return TRUE;
}

/* Create an interrupt handler for the transmit buffer empty interrupt
 * (or an equivalent) for your target processor. This function should then
 * call pxMBFrameCBTransmitterEmpty( ) which tells the protocol stack that
 * a new character can be sent. The protocol stack will then call 
 * xMBPortSerialPutByte( ) to send the character.
 */
void prvvUARTTxReadyISR( void )
{
    Modbus_Test_Inc_Tx_Count();  // 调试计数
    pxMBFrameCBTransmitterEmpty(  );
}

/* Create an interrupt handler for the receive interrupt for your target
 * processor. This function should then call pxMBFrameCBByteReceived( ). The
 * protocol stack will then call xMBPortSerialGetByte( ) to retrieve the
 * character.
 */
void prvvUARTRxISR( void )
{
    Modbus_Test_Inc_Rx_Count();  // 调试计数
    pxMBFrameCBByteReceived(  );
}

// HAL UART回调函数
void HAL_UART_RxCpltCallback(UART_HandleTypeDef *huart)
{
    if (huart->Instance == USART1)
    {
        prvvUARTRxISR();  // 调用FreeModbus的接收中断处理函数
    }
}

void HAL_UART_TxCpltCallback(UART_HandleTypeDef *huart)
{
    if (huart->Instance == USART1)
    {
        prvvUARTTxReadyISR();  // 调用FreeModbus的发送完成处理函数
    }
}

// HAL库错误回调
void HAL_UART_ErrorCallback(UART_HandleTypeDef *huart)
{
    if (huart->Instance == USART1)
    {
        // 清除错误标志并重新启动接收
        __HAL_UART_CLEAR_PEFLAG(&huart1);
        __HAL_UART_CLEAR_FEFLAG(&huart1);
        __HAL_UART_CLEAR_NEFLAG(&huart1);
        __HAL_UART_CLEAR_OREFLAG(&huart1);
        
        // 清除错误后，接收会自动继续
    }
}

#ifdef RTS_ENABLE
SIGNAL( SIG_UART_TRANS )
{
    RTS_LOW;
}
#endif