{"configurations": [{"name": "PowerGuard-L496", "includePath": ["e:\\项目\\电能计量模块\\程序\\PowerGuard-L496\\Core\\Inc", "e:\\项目\\电能计量模块\\程序\\PowerGuard-L496\\Drivers\\STM32L4xx_HAL_Driver\\Inc", "e:\\项目\\电能计量模块\\程序\\PowerGuard-L496\\Drivers\\STM32L4xx_HAL_Driver\\Inc\\Legacy", "e:\\项目\\电能计量模块\\程序\\PowerGuard-L496\\Middlewares\\Third_Party\\FreeRTOS\\Source\\include", "e:\\项目\\电能计量模块\\程序\\PowerGuard-L496\\Middlewares\\Third_Party\\FreeRTOS\\Source\\portable\\RVDS\\ARM_CM4F", "e:\\项目\\电能计量模块\\程序\\PowerGuard-L496\\Drivers\\CMSIS\\Device\\ST\\STM32L4xx\\Include", "e:\\项目\\电能计量模块\\程序\\PowerGuard-L496\\Drivers\\CMSIS\\Include", "e:\\项目\\电能计量模块\\程序\\PowerGuard-L496\\UserCode\\App\\Include", "e:\\项目\\电能计量模块\\程序\\PowerGuard-L496\\UserCode\\Driver\\Include", "e:\\项目\\电能计量模块\\程序\\PowerGuard-L496\\UserCode\\Support\\Include", "e:\\项目\\电能计量模块\\程序\\PowerGuard-L496\\UserCode\\Service\\Include", "e:\\项目\\电能计量模块\\程序\\PowerGuard-L496\\Middlewares\\Third_Party\\FreeRTOS\\Source\\CMSIS_RTOS_V2", "e:\\项目\\电能计量模块\\程序\\PowerGuard-L496\\UserCode\\FreeModbus\\modbus\\ascii", "e:\\项目\\电能计量模块\\程序\\PowerGuard-L496\\UserCode\\FreeModbus\\modbus\\include", "e:\\项目\\电能计量模块\\程序\\PowerGuard-L496\\UserCode\\FreeModbus\\port", "e:\\项目\\电能计量模块\\程序\\PowerGuard-L496\\UserCode\\FreeModbus\\modbus\\rtu", "e:\\项目\\电能计量模块\\程序\\PowerGuard-L496\\UserCode\\FreeModbus\\modbus\\tcp", "D:\\Keil_v5\\ARM\\ARMCC\\include", "D:\\Keil_v5\\ARM\\ARMCC\\include\\rw", "e:\\项目\\电能计量模块\\程序\\PowerGuard-L496\\MDK-ARM", "e:\\项目\\电能计量模块\\程序\\PowerGuard-L496\\Core\\Src", "e:\\项目\\电能计量模块\\程序\\PowerGuard-L496\\Drivers\\STM32L4xx_HAL_Driver\\Src", "e:\\项目\\电能计量模块\\程序\\PowerGuard-L496\\Middlewares\\Third_Party\\FreeRTOS\\Source", "e:\\项目\\电能计量模块\\程序\\PowerGuard-L496\\Middlewares\\Third_Party\\FreeRTOS\\Source\\portable\\MemMang", "e:\\项目\\电能计量模块\\程序\\PowerGuard-L496\\UserCode\\App\\Source", "e:\\项目\\电能计量模块\\程序\\PowerGuard-L496\\UserCode\\Driver\\Source", "e:\\项目\\电能计量模块\\程序\\PowerGuard-L496\\UserCode\\Service\\Source", "e:\\项目\\电能计量模块\\程序\\PowerGuard-L496\\UserCode\\FreeModbus\\modbus\\functions", "e:\\项目\\电能计量模块\\程序\\PowerGuard-L496\\UserCode\\FreeModbus\\modbus"], "defines": ["USE_HAL_DRIVER", "STM32L496xx", "__CC_ARM", "__arm__", "__align(x)=", "__ALIGNOF__(x)=", "__alignof__(x)=", "__asm(x)=", "__forceinline=", "__restrict=", "__global_reg(n)=", "__inline=", "__int64=long long", "__INTADDR__(expr)=0", "__irq=", "__packed=", "__pure=", "__smc(n)=", "__svc(n)=", "__svc_indirect(n)=", "__svc_indirect_r7(n)=", "__value_in_regs=", "__weak=", "__writeonly=", "__declspec(x)=", "__attribute__(x)=", "__nonnull__(x)=", "__register=", "__breakpoint(x)=", "__cdp(x,y,z)=", "__clrex()=", "__clz(x)=0U", "__current_pc()=0U", "__current_sp()=0U", "__disable_fiq()=", "__disable_irq()=", "__dmb(x)=", "__dsb(x)=", "__enable_fiq()=", "__enable_irq()=", "__fabs(x)=0.0", "__fabsf(x)=0.0f", "__force_loads()=", "__force_stores()=", "__isb(x)=", "__ldrex(x)=0U", "__ldrexd(x)=0U", "__ldrt(x)=0U", "__memory_changed()=", "__nop()=", "__pld(...)=", "__pli(...)=", "__qadd(x,y)=0", "__qdbl(x)=0", "__qsub(x,y)=0", "__rbit(x)=0U", "__rev(x)=0U", "__return_address()=0U", "__ror(x,y)=0U", "__schedule_barrier()=", "__semihost(x,y)=0", "__sev()=", "__sqrt(x)=0.0", "__sqrtf(x)=0.0f", "__ssat(x,y)=0", "__strex(x,y)=0U", "__strexd(x,y)=0", "__strt(x,y)=", "__swp(x,y)=0U", "__usat(x,y)=0U", "__wfe()=", "__wfi()=", "__yield()=", "__vfp_status(x,y)=0"], "intelliSenseMode": "${default}"}], "version": 4}