#ifndef RN7326_PARAM_REG_H
#define RN7326_PARAM_REG_H

#include <stdint.h>

// 寄存器基地址（SOC RN7326（B64）用户手册_V1.5第90页）
#define RN7326_METERING_BASE_ADDR    0x50004000UL

// ======================= 全波计量参数寄存器 =======================
// 电压/电流采样数据（3B有效）
#define UAWAV_REG                   (RN7326_METERING_BASE_ADDR + 0x300)  // A相电压采样
#define UBWAW_REG                   (RN7326_METERING_BASE_ADDR + 0x304)  // B相电压采样
#define UCWAV_REG                   (RN7326_METERING_BASE_ADDR + 0x308)  // C相电压采样
#define IAWAV_REG                   (RN7326_METERING_BASE_ADDR + 0x30C)  // A相电流采样
#define IBWAV_REG                   (RN7326_METERING_BASE_ADDR + 0x310)  // B相电流采样
#define ICWAV_REG                   (RN7326_METERING_BASE_ADDR + 0x314)  // C相电流采样
#define INWAV_REG                   (RN7326_METERING_BASE_ADDR + 0x318)  // 零线电流采样

// 电压/电流有效值（4B有效）
#define UA_REG                      (RN7326_METERING_BASE_ADDR + 0x31C)  // A相电压有效值
#define UB_REG                      (RN7326_METERING_BASE_ADDR + 0x320)  // B相电压有效值
#define UC_REG                      (RN7326_METERING_BASE_ADDR + 0x324)  // C相电压有效值
#define USUM_REG                    (RN7326_METERING_BASE_ADDR + 0x328)  // 电压矢量和
#define IA_REG                      (RN7326_METERING_BASE_ADDR + 0x32C)  // A相电流有效值
#define IB_REG                      (RN7326_METERING_BASE_ADDR + 0x330)  // B相电流有效值
#define IC_REG                      (RN7326_METERING_BASE_ADDR + 0x334)  // C相电流有效值
#define IN_REG                      (RN7326_METERING_BASE_ADDR + 0x338)  // 零线电流有效值
#define ISUM_REG                    (RN7326_METERING_BASE_ADDR + 0x33C)  // 电流矢量和

// 功率相关（4B有效）
#define PA_REG                      (RN7326_METERING_BASE_ADDR + 0x344)  // A相有功功率
#define PB_REG                      (RN7326_METERING_BASE_ADDR + 0x348)  // B相有功功率
#define PC_REG                      (RN7326_METERING_BASE_ADDR + 0x34C)  // C相有功功率
#define PT_REG                      (RN7326_METERING_BASE_ADDR + 0x350)  // 合相有功功率
#define QA_REG                      (RN7326_METERING_BASE_ADDR + 0x354)  // A相无功功率
#define QB_REG                      (RN7326_METERING_BASE_ADDR + 0x358)  // B相无功功率
#define QC_REG                      (RN7326_METERING_BASE_ADDR + 0x35C)  // C相无功功率
#define QT_REG                      (RN7326_METERING_BASE_ADDR + 0x360)  // 合相无功功率
#define SA_REG                      (RN7326_METERING_BASE_ADDR + 0x364)  // A相视在功率
#define SB_REG                      (RN7326_METERING_BASE_ADDR + 0x368)  // B相视在功率
#define SC_REG                      (RN7326_METERING_BASE_ADDR + 0x36C)  // C相视在功率
#define STA_REG                     (RN7326_METERING_BASE_ADDR + 0x370)  // RMS合相视在功率

// 功率因数（3B有效）
#define PFA_REG                     (RN7326_METERING_BASE_ADDR + 0x374)  // A相功率因数
#define PFB_REG                     (RN7326_METERING_BASE_ADDR + 0x378)  // B相功率因数
#define PFC_REG                     (RN7326_METERING_BASE_ADDR + 0x37C)  // C相功率因数
#define PFT_REG                     (RN7326_METERING_BASE_ADDR + 0x380)  // 合相功率因数

// 能量累计（3B有效）
#define EPA_REG                     (RN7326_METERING_BASE_ADDR + 0x3B4)  // A相有功能量
#define EPB_REG                     (RN7326_METERING_BASE_ADDR + 0x3B8)  // B相有功能量
#define EPC_REG                     (RN7326_METERING_BASE_ADDR + 0x3BC)  // C相有功能量
#define EPT_REG                     (RN7326_METERING_BASE_ADDR + 0x3C0)  // 合相有功能量
#define POS_EPA_REG                 (RN7326_METERING_BASE_ADDR + 0x3C4)  // A相正向有功
#define NEG_EPA_REG                 (RN7326_METERING_BASE_ADDR + 0x3D4)  // A相反向有功
#define EQA_REG                     (RN7326_METERING_BASE_ADDR + 0x3E4)  // A相无功能量

// ======================= 基波谐波计量寄存器 =======================
#define FUA_REG                     (RN7326_METERING_BASE_ADDR + 0x454)  // A相基波电压
#define FIA_REG                     (RN7326_METERING_BASE_ADDR + 0x460)  // A相基波电流
#define FPA_REG                     (RN7326_METERING_BASE_ADDR + 0x46C)  // A相基波有功功率
#define FEPA_REG                    (RN7326_METERING_BASE_ADDR + 0x4DC)  // A相基波有功能量

// ======================= 配置和状态寄存器 =======================
#define OI2_LVL_REG                 (RN7326_METERING_BASE_ADDR + 0x260)  // 过流阈值
#define OI2_CNTTH_REG               (RN7326_METERING_BASE_ADDR + 0x264)  // 过流时间阈值
#define MODSEL_REG                  (RN7326_METERING_BASE_ADDR + 0x17C)  // 三相四线/三线模式

#endif // RN7326_PARAM_REG_H
