#ifndef RN7326_CFG_REG_H
#define RN7326_CFG_REG_H

#include <stdint.h>

// ======================= EMIU校表寄存器 =======================
#define HFConst1_REG               (RN7326_METERING_BASE_ADDR + 0x00)  // 高频脉冲常数1 (R/W, 2B)
#define HFConst2_REG               (RN7326_METERING_BASE_ADDR + 0x04)  // 高频脉冲常数2 (R/W, 2B)
#define Start_PS_REG               (RN7326_METERING_BASE_ADDR + 0x08)  // 有功视在启动阈值 (R/W, 2B)
#define Start_Q_REG                (RN7326_METERING_BASE_ADDR + 0x0C)  // 无功启动阈值 (R/W, 2B)
#define LostVoltage_REG            (RN7326_METERING_BASE_ADDR + 0x10)  // 失压阈值 (R/W, 2B)
#define ZXOT_REG                   (RN7326_METERING_BASE_ADDR + 0x14)  // 过零阈值 (R/W, 2B)

// ======================= 相位校正寄存器 =======================
#define PHSUA_REG                  (RN7326_METERING_BASE_ADDR + 0x30)  // UA相位校正 (R/W, 4B)
#define PHSUB_REG                  (RN7326_METERING_BASE_ADDR + 0x34)  // UB相位校正 (R/W, 4B)
#define PHSUC_REG                  (RN7326_METERING_BASE_ADDR + 0x38)  // UC相位校正 (R/W, 4B)
#define PHSIA_REG                  (RN7326_METERING_BASE_ADDR + 0x3C)  // IA分段相位校正 (R/W, 4B)
#define PHSIB_REG                  (RN7326_METERING_BASE_ADDR + 0x40)  // IB分段相位校正 (R/W, 4B)
#define PHSIC_REG                  (RN7326_METERING_BASE_ADDR + 0x44)  // IC分段相位校正 (R/W, 4B)
#define PHSIN_REG                  (RN7326_METERING_BASE_ADDR + 0x48)  // IN相位校正 (R/W, 1B)

// ======================= 通道增益校正 =======================
#define GSUA_REG                   (RN7326_METERING_BASE_ADDR + 0x4C)  // UA通道增益 (R/W, 2B)
#define GSIA_REG                   (RN7326_METERING_BASE_ADDR + 0x58)  // IA通道增益 (R/W, 2B)
#define GPA_REG                    (RN7326_METERING_BASE_ADDR + 0xA0)  // A相有功功率增益 (R/W, 2B)
#define GQA_REG                    (RN7326_METERING_BASE_ADDR + 0xAC)  // A相无功功率增益 (R/W, 2B)

// ======================= EMU配置和状态寄存器 =======================
#define MODSEL_REG                 (RN7326_METERING_BASE_ADDR + 0x17C) // 三相四线/三线模式 (R/W, 1B)
#define EMUCFG_REG                 (RN7326_METERING_BASE_ADDR + 0x188) // EMU配置寄存器 (R/W, 3B)
#define EMUIE_REG                  (RN7326_METERING_BASE_ADDR + 0x190) // EMU中断使能 (R/W, 2B)
#define EMUIF_REG                  (RN7326_METERING_BASE_ADDR + 0x194) // EMU中断标志 (R, 2B)
#define POSign_REG                 (RN7326_METERING_BASE_ADDR + 0x198) // 功率方向状态 (R, 2B)

// ======================= 过流/过压检测 =======================
#define OI2_LVL_REG                (RN7326_METERING_BASE_ADDR + 0x260) // 过流阈值 (R/W, 2B)
#define OI2_CNTTH_REG              (RN7326_METERING_BASE_ADDR + 0x264) // 过流时间阈值 (R/W, 1B)
#define OV2_LVL_REG                (RN7326_METERING_BASE_ADDR + 0x278) // 过压阈值 (R/W, 2B)
#define OV2_CNTTH_REG              (RN7326_METERING_BASE_ADDR + 0x27C) // 过压时间阈值 (R/W, 2B)

// ======================= DMA波形缓存控制 =======================
#define DMA_BUF_CTRL_REG           (RN7326_METERING_BASE_ADDR + 0x2A0) // ADC缓存控制 (R/W, 2B)
#define DMA_BUF_BASE_ADDR_REG      (RN7326_METERING_BASE_ADDR + 0x2A4) // 缓存基地址 (R/W, 2B)
#define DMA_WAVE_CH_SEL_REG        (RN7326_METERING_BASE_ADDR + 0x2AC) // 通道选择 (R/W, 1B)

#endif // RN7326_CFG_REG_H
