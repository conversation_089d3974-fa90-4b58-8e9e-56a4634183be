#ifndef __RN7326E_REGS_H__
#define __RN7326E_REGS_H__

/**
 * RN7326E计量芯片寄存器宏定义
 * 版本：锐能微三相计量电能质量芯片RN2026E RN7326E V2用户手册 v1.0.7
 * 设计原则：
 *   1. 统一前缀 RN7326E_REG_ 避免命名冲突
 *   2. 按功能模块分组并添加章节注释
 *   3. 关键寄存器标注默认值和特殊操作要求
 *   4. 地址统一使用4位十六进制格式（0xXXXX）
 */

/* ====================== 计量配置寄存器 (CMD_0x20) ====================== */
/* 高频脉冲常数 */
#define RN7326E_REG_HFConst1        0x0000  // 高频脉冲常数1, R/W, 默认0x1000
#define RN7326E_REG_HFConst2        0x0004  // 高频脉冲常数2, R/W, 默认0x1000
#define RN7326E_REG_HFCONST3        0x01C8  // 高频脉冲常数3, R/W, 默认0x0
#define RN7326E_REG_HFCONST4        0x0740  // 高频脉冲常数4, R/W, 默认0x1000
#define RN7326E_REG_HFCONST5        0x0744  // 高频脉冲常数5, R/W, 默认0x1000
#define RN7326E_REG_HFCONST6        0x0748  // 高频脉冲常数6, R/W, 默认0x1000

/* 启动阈值 */
#define RN7326E_REG_Start_PS        0x0008  // 有功/视在启动阈值, R/W, 默认0x0250
#define RN7326E_REG_Start_Q         0x000C  // 无功启动阈值, R/W, 默认0x0250
#define RN7326E_REG_LostVoltage     0x0010  // 失压阈值, R/W, 默认0x0400
#define RN7326E_REG_ZXOT            0x0014  // 过零阈值, R/W, 默认0x0073

/* 分段校正阈值 */
#define RN7326E_REG_PRTH1L          0x0018  // 相位校正电流阈值1下限, R/W, 默认0x0000
#define RN7326E_REG_PRTH1H          0x001C  // 相位校正电流阈值1上限, R/W, 默认0x0000
#define RN7326E_REG_PRTH2L          0x0020  // 相位校正电流阈值2下限, R/W, 默认0x0000
#define RN7326E_REG_PRTH2H          0x0024  // 相位校正电流阈值2上限, R/W, 默认0x0000
#define RN7326E_REG_IRegion3L       0x0028  // 电流阈值3下限, R/W, 默认0x0000
#define RN7326E_REG_IRegion3H       0x002C  // 电流阈值3上限, R/W, 默认0x0000

/* 电压通道相位校正 */
#define RN7326E_REG_PHSUA           0x0030  // UA相位校正, R/W, 默认0x80
#define RN7326E_REG_PHSUB           0x0034  // UB相位校正, R/W, 默认0x80
#define RN7326E_REG_PHSUC           0x0038  // UC相位校正, R/W, 默认0x80
#define RN7326E_REG_PHSIN           0x0048  // IN相位校正, R/W, 默认0x80

/* 电流通道分段相位校正 */
#define RN7326E_REG_PHSIA           0x003C  // IA分段相位校正, R/W, 默认0x808080
#define RN7326E_REG_PHSIB           0x0040  // IB分段相位校正, R/W, 默认0x808080
#define RN7326E_REG_PHSIC           0x0044  // IC分段相位校正, R/W, 默认0x808080

/* 通道增益配置（完整列表） */
#define RN7326E_REG_GSUA            0x004C  // UA增益, R/W, 默认0x0000
#define RN7326E_REG_GSUB            0x0050  // UB增益, R/W, 默认0x0000
#define RN7326E_REG_GSUC            0x0054  // UC增益, R/W, 默认0x0000
#define RN7326E_REG_GSIA            0x0058  // IA增益, R/W, 默认0x0000
#define RN7326E_REG_GSIB            0x005C  // IB增益, R/W, 默认0x0000
#define RN7326E_REG_GSIC            0x0060  // IC增益, R/W, 默认0x0000
#define RN7326E_REG_GSIN            0x0064  // IN增益, R/W, 默认0x0000

/* 有效值Offset校正（完整列表） */
#define RN7326E_REG_UA_OS           0x0084  // UA有效值Offset, R/W, 默认0x0000
#define RN7326E_REG_UB_OS           0x0088  // UB有效值Offset, R/W, 默认0x0000
#define RN7326E_REG_UC_OS           0x008C  // UC有效值Offset, R/W, 默认0x0000
#define RN7326E_REG_IA_OS           0x0090  // IA有效值Offset, R/W, 默认0x0000
#define RN7326E_REG_IB_OS           0x0094  // IB有效值Offset, R/W, 默认0x0000
#define RN7326E_REG_IC_OS           0x0098  // IC有效值Offset, R/W, 默认0x0000
#define RN7326E_REG_IN_OS           0x009C  // IN有效值Offset, R/W, 默认0x0000

/* ====================== 计量参数寄存器 (CMD_0x20读) ====================== */
/* 电压/电流有效值 */
#define RN7326E_REG_UA              0x031C  // UA有效值, R
#define RN7326E_REG_UB              0x0320  // UB有效值, R
#define RN7326E_REG_UC              0x0324  // UC有效值, R
#define RN7326E_REG_USUM            0x0328  // 电压矢量和, R
#define RN7326E_REG_IA              0x032C  // IA有效值, R
#define RN7326E_REG_IB              0x0330  // IB有效值, R
#define RN7326E_REG_IC              0x0334  // IC有效值, R
#define RN7326E_REG_IN              0x0338  // IN有效值, R
#define RN7326E_REG_ISUM            0x0340  // 电流矢量和, R

/* ----- 功率参数（全波） ----- */
#define RN7326E_REG_PA              0x0344 // A相有功功率, R, 默认值:0x0
#define RN7326E_REG_PB              0x0348 // B相有功功率, R, 默认值:0x0
#define RN7326E_REG_PC              0x034C // C相有功功率, R, 默认值:0x0
#define RN7326E_REG_PT              0x0350 // 合相有功功率, R, 默认值:0x0
#define RN7326E_REG_QA              0x0354 // A相无功功率, R, 默认值:0x0
#define RN7326E_REG_QB              0x0358 // B相无功功率, R, 默认值:0x0
#define RN7326E_REG_QC              0x035C // C相无功功率, R, 默认值:0x0
#define RN7326E_REG_QT              0x0360 // 合相无功功率, R, 默认值:0x0

/* ----- 能量计量 ----- */
#define RN7326E_REG_EPA             0x03B4 // A相有功能量, R, 默认值:0x0
#define RN7326E_REG_EPB             0x03B8 // B相有功能量, R, 默认值:0x0
#define RN7326E_REG_EPC             0x03BC // C相有功能量, R, 默认值:0x0
#define RN7326E_REG_EPT             0x03C0 // 合相有功能量, R, 默认值:0x0
#define RN7326E_REG_PosEPA          0x03C4 // A相正向有功能量, R, 默认值:0x0
#define RN7326E_REG_PosEPB          0x03C8 // B相正向有功能量, R, 默认值:0x0
#define RN7326E_REG_PosEPC          0x03CC // C相正向有功能量, R, 默认值:0x0
#define RN7326E_REG_PosEPT          0x03D0 // 合相正向有功能量, R, 默认值:0x0

/* ================== EMU控制寄存器 ================== */
#define RN7326E_REG_SPCMD           0x02FC // EMU写使能密码寄存器, R/W, 默认值:0x0
#define RN7326E_REG_EMUCFG          0x0188 // EMU配置寄存器, R/W, 默认值:0x400000
#define RN7326E_REG_EMUCON          0x018C // EMU控制寄存器, R/W, 默认值:0x000000
#define RN7326E_REG_EMUIF           0x0194 // EMU中断标志和状态寄存器, R, 默认值:--

/* ================== ADC控制寄存器 ================== */
#define RN7326E_REG_EXT_ADC_START   0x0804 // 启动ADC寄存器, R/W, 默认值:0x20267521
#define RN7326E_REG_PHS_UA_WAVE     0x0604 // ADC波形缓存UA通道相位校正, R/W, 默认值:0x0
#define RN7326E_REG_PHS_IA_WAVE     0x0610 // ADC波形缓存IA通道相位校正, R/W, 默认值:0x0
#define RN7326E_REG_GSUA_WAVE       0x0620 // ADC波形缓存UA通道增益校正, R/W, 默认值:0x0
#define RN7326E_REG_GSIA_WAVE       0x062C // ADC波形缓存IA通道增益校正, R/W, 默认值:0x0

/* ================== 辅助寄存器 (CMD_0x21) ================== */
#define RN7326E_REG_EMU_CHK_CAL     0x0000 // EMU校表寄存器和配置寄存器的校验和运算, R/W, 默认值:0x0
#define RN7326E_REG_LINE_UAB        0x0008 // 线电压UAB, R, 默认值:0x0
#define RN7326E_REG_LINE_UBC        0x000C // 线电压UBC, R, 默认值:0x0
#define RN7326E_REG_LINE_UCA        0x0010 // 线电压UCA, R, 默认值:0x0

/* ====================== 新增功能寄存器 ====================== */
#define RN7326E_REG_SPL_PA          0x05B8  // A相瞬时有功功率(8kHz), R
#define RN7326E_REG_SPL_PB          0x05BC  // B相瞬时有功功率(8kHz), R
#define RN7326E_REG_SPL_PC          0x05C0  // C相瞬时有功功率(8kHz), R

/* ====================== 控制寄存器 ====================== */
#define RN7326E_REG_SPCMD           0x02FC  // 写使能密码, R/W, 写0xE5解锁
#define RN7326E_REG_EXT_ADC_START   0x0804  // ADC启动控制, R/W, 需先解锁SPCMD

#endif // __RN7326E_REGS_H__
